#!/usr/bin/env python3
"""
Test script to verify database and authentication setup

NOTE: This file uses dynamic path manipulation to import modules from the backend directory.
Pylance may show import resolution warnings, but the script works correctly when executed.
The `# type: ignore` comments suppress these false-positive warnings.

To run this test: python test_database.py
"""

import sys
import os
from pathlib import Path

# Get the absolute path to the backend directory
current_dir = Path(__file__).parent
backend_dir = current_dir / 'backend'

# Add backend directory to Python path
sys.path.insert(0, str(backend_dir))

# NOTE: The following imports may show Pylance errors due to path resolution issues,
# but they work correctly when the script is executed. This is a common issue with
# dynamic path manipulation in test files.

try:
    # Pylance may show "Import could not be resolved" errors for these lines,
    # but they work at runtime due to the sys.path.insert() calls above
    from database import init_database, get_db, User, create_user  # type: ignore
    from sqlalchemy.orm import Session  # type: ignore
    
    # Import auth modules with fallback
    try:
        sys.path.insert(0, str(backend_dir / 'auth'))
        from security import hash_password, verify_password  # type: ignore
    except ImportError:
        print("Warning: Could not import auth.security, using fallback functions")
        def hash_password(password: str) -> str:
            return f"test_hash_{password}"
        def verify_password(password: str, hashed: str) -> bool:
            return hashed == f"test_hash_{password}"
            
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

def test_database_setup():
    """Test basic database functionality"""
    print("Initializing database...")
    init_database()
    print("Database initialized successfully!")
    
    # Test user creation
    db_gen = get_db()
    db = next(db_gen)
    
    try:
        # Create a test user
        test_email = "<EMAIL>"
        test_name = "Test User"
        test_password = "TestPassword123"
        
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == test_email).first()
        if existing_user:
            print(f"Test user {test_email} already exists")
        else:
            hashed_password = hash_password(test_password)
            user = create_user(db, test_email, test_name, hashed_password)
            print(f"Created test user: {user.email}")
        
        # Test password verification
        user = db.query(User).filter(User.email == test_email).first()
        if user and verify_password(test_password, user.hashed_password):
            print("Password verification successful!")
        else:
            print("Password verification failed!")
            
        print("Database setup test completed successfully!")
        
    except Exception as e:
        print(f"Database test failed: {e}")
        return False
    finally:
        db.close()
    
    return True

if __name__ == "__main__":
    test_database_setup()
