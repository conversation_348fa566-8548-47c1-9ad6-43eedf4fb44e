# My ÆI - Comprehensive Testing Documentation

## Overview

This document outlines the comprehensive testing strategy for the My ÆI application, covering all aspects from unit tests to end-to-end testing across multiple browsers and devices.

## Table of Contents

1. [Testing Philosophy](#testing-philosophy)
2. [Test Types](#test-types)
3. [Testing Tools](#testing-tools)
4. [Test Structure](#test-structure)
5. [Running Tests](#running-tests)
6. [Writing Tests](#writing-tests)
7. [Continuous Integration](#continuous-integration)
8. [Performance Testing](#performance-testing)
9. [Accessibility Testing](#accessibility-testing)
10. [Cross-Browser Testing](#cross-browser-testing)
11. [Visual Regression Testing](#visual-regression-testing)
12. [API Testing](#api-testing)
13. [Test Data Management](#test-data-management)
14. [Troubleshooting](#troubleshooting)

## Testing Philosophy

Our testing strategy follows the **Testing Pyramid** approach:

- **Unit Tests (70%)**: Fast, isolated tests for individual components and functions
- **Integration Tests (20%)**: Tests for component interactions and API integrations
- **End-to-End Tests (10%)**: Full user journey tests across the entire application

### Core Principles

1. **Test Early, Test Often**: Tests are written alongside development
2. **Test Behavior, Not Implementation**: Focus on what the code does, not how
3. **Maintain Test Quality**: Tests should be readable, maintainable, and reliable
4. **Automate Everything**: All tests run automatically in CI/CD pipeline
5. **Accessibility First**: Every feature is tested for accessibility compliance

## Test Types

### 1. Unit Tests
- **Purpose**: Test individual components, functions, and utilities in isolation
- **Tools**: Jest, React Testing Library
- **Coverage**: Components, hooks, utilities, business logic
- **Location**: `src/**/__tests__/**/*.test.js`

### 2. Integration Tests
- **Purpose**: Test component interactions and API integrations
- **Tools**: Jest, React Testing Library, MSW (Mock Service Worker)
- **Coverage**: Page components, context providers, API calls
- **Location**: `src/**/__tests__/**/*.integration.test.js`

### 3. End-to-End Tests
- **Purpose**: Test complete user workflows
- **Tools**: Cypress
- **Coverage**: User journeys, form submissions, navigation
- **Location**: `cypress/e2e/**/*.cy.js`

### 4. Cross-Browser Tests
- **Purpose**: Ensure compatibility across different browsers
- **Tools**: Playwright
- **Coverage**: Chrome, Firefox, Safari, Edge, Mobile browsers
- **Location**: `tests/playwright/**/*.spec.js`

### 5. Accessibility Tests
- **Purpose**: Ensure WCAG compliance and usability
- **Tools**: jest-axe, Cypress axe
- **Coverage**: All interactive elements, forms, navigation
- **Location**: Integrated within unit and e2e tests

### 6. Performance Tests
- **Purpose**: Measure and validate performance metrics
- **Tools**: Lighthouse, Web Vitals, custom performance tests
- **Coverage**: Load times, bundle sizes, runtime performance
- **Location**: `tests/performance/**/*.test.js`

### 7. Visual Regression Tests
- **Purpose**: Detect unintended visual changes
- **Tools**: Playwright screenshots, Percy (if integrated)
- **Coverage**: UI components, responsive layouts
- **Location**: `tests/visual/**/*.test.js`

### 8. API Tests
- **Purpose**: Test backend API endpoints
- **Tools**: pytest, FastAPI TestClient
- **Coverage**: All API endpoints, authentication, error handling
- **Location**: `backend/tests/**/*.py`

## Testing Tools

### Frontend Testing Stack

| Tool | Purpose | Configuration |
|------|---------|---------------|
| **Jest** | Unit test runner | `jest.config.js` |
| **React Testing Library** | Component testing utilities | `src/setupTests.js` |
| **MSW** | API mocking | `src/__mocks__/handlers.js` |
| **Cypress** | E2E testing | `cypress.config.js` |
| **Playwright** | Cross-browser testing | `playwright.config.js` |
| **jest-axe** | Accessibility testing | Integrated in test files |

### Backend Testing Stack

| Tool | Purpose | Configuration |
|------|---------|---------------|
| **pytest** | Python test runner | `pytest.ini` |
| **FastAPI TestClient** | API testing | Test files |
| **SQLite** | Test database | In-memory for tests |

## Test Structure

### Frontend Test Organization

```
frontend/
├── src/
│   ├── components/
│   │   └── __tests__/
│   │       ├── Button.test.js
│   │       ├── Card.test.js
│   │       └── FormElements.test.js
│   ├── pages/
│   │   └── __tests__/
│   │       ├── Home.test.js
│   │       └── Dashboard.test.js
│   ├── hooks/
│   │   └── __tests__/
│   ├── utils/
│   │   └── __tests__/
│   └── __tests__/
│       └── utils/
│           └── testUtils.js
├── cypress/
│   ├── e2e/
│   ├── support/
│   └── fixtures/
└── tests/
    └── playwright/
```

### Backend Test Organization

```
backend/
├── tests/
│   ├── __init__.py
│   ├── test_api.py
│   ├── test_auth.py
│   ├── test_database.py
│   └── test_services.py
└── conftest.py
```

## Running Tests

### Quick Commands

```bash
# Run all tests
npm run test:all

# Run specific test types
npm run test                    # Unit tests
npm run test:coverage          # Unit tests with coverage
npm run test:e2e               # End-to-end tests
npm run test:playwright        # Cross-browser tests
npm run test:accessibility     # Accessibility tests
npm run test:performance       # Performance tests
npm run test:visual            # Visual regression tests

# Backend tests
cd backend && python -m pytest
```

### Comprehensive Test Runner

Use the custom test runner for orchestrated testing:

```bash
# Run all test suites
node test-runner.js --all

# Run specific test types
node test-runner.js unit integration
node test-runner.js e2e cross-browser
node test-runner.js accessibility performance

# Get help
node test-runner.js --help
```

### Watch Mode for Development

```bash
# Run tests in watch mode during development
npm run test:watch

# Run specific test file in watch mode
npm test -- --watch Button.test.js
```

## Writing Tests

### Unit Test Example

```javascript
import React from 'react';
import { render, screen, fireEvent } from '../__tests__/utils/testUtils';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import Button from '../Button';

describe('Button Component', () => {
  const user = userEvent.setup();

  it('renders button with text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('calls onClick when clicked', async () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should not have accessibility violations', async () => {
    const { container } = render(<Button>Accessible Button</Button>);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
```

### Integration Test Example

```javascript
import React from 'react';
import { render, screen, waitFor } from '../__tests__/utils/testUtils';
import { server } from '../__mocks__/server';
import { http, HttpResponse } from 'msw';
import Dashboard from '../Dashboard';

describe('Dashboard Integration', () => {
  it('loads and displays dashboard data', async () => {
    render(<Dashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('5')).toBeInTheDocument(); // total_relationships
      expect(screen.getByText('23')).toBeInTheDocument(); // total_analyses
    });
  });

  it('handles API errors gracefully', async () => {
    server.use(
      http.get('/api/dashboard', () => {
        return new HttpResponse(null, { status: 500 });
      })
    );
    
    render(<Dashboard />);
    
    await waitFor(() => {
      expect(screen.getByText(/Failed to load/i)).toBeInTheDocument();
    });
  });
});
```

### End-to-End Test Example

```javascript
describe('User Journey', () => {
  it('completes message analysis workflow', () => {
    cy.visit('/');
    cy.login('<EMAIL>', 'password');
    
    cy.navigateToPage('message-analyzer');
    cy.fillMessageAnalyzer({
      message: 'I had a great time today!',
      relationship: 'Sarah'
    });
    
    cy.get('[data-testid="analyze-button"]').click();
    cy.wait('@postAnalysis');
    
    cy.get('[data-testid="analysis-results"]').should('be.visible');
    cy.get('[data-testid="sentiment-score"]').shouldHaveValidSentiment();
  });
});
```

### API Test Example

```python
import pytest
from fastapi.testclient import TestClient
from server import app

client = TestClient(app)

def test_create_relationship():
    response = client.post(
        "/relationships",
        json={
            "name": "Test Friend",
            "relationship_type": "friend",
            "description": "A test relationship"
        },
        headers={"Authorization": "Bearer test_token"}
    )
    assert response.status_code == 201
    assert response.json()["name"] == "Test Friend"
```

## Test Naming Conventions

### File Naming

- Unit tests: `ComponentName.test.js`
- Integration tests: `ComponentName.integration.test.js`
- E2E tests: `feature-name.cy.js`
- API tests: `test_endpoint_name.py`

### Test Case Naming

Use descriptive names that explain the behavior being tested:

```javascript
// Good
it('displays error message when API call fails')
it('navigates to dashboard after successful login')
it('validates email format in registration form')

// Avoid
it('should work')
it('test login')
it('API test')
```

## Test Data Management

### Fixtures

Store reusable test data in fixture files:

```javascript
// cypress/fixtures/user.json
{
  "testUser": {
    "email": "<EMAIL>",
    "name": "Test User",
    "id": "test-user-123"
  }
}

// Usage in tests
cy.fixture('user').then((userData) => {
  cy.login(userData.testUser.email, 'password');
});
```

### Factories

Create data factories for generating test data:

```javascript
// testUtils.js
export const createMockUser = (overrides = {}) => ({
  id: 'user-123',
  email: '<EMAIL>',
  name: 'Test User',
  ...overrides
});

export const createMockRelationship = (overrides = {}) => ({
  id: 'rel-123',
  name: 'Test Friend',
  type: 'friend',
  healthScore: 85,
  ...overrides
});
```

## Continuous Integration

### GitHub Actions Workflow

```yaml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:coverage
      - run: npm run test:e2e
      - run: npm run test:playwright
      - uses: codecov/codecov-action@v3
```

### Quality Gates

Tests must pass these criteria:

- **Unit Test Coverage**: ≥ 80% lines, branches, functions
- **E2E Test Coverage**: All critical user paths
- **Accessibility**: Zero violations for WCAG AA
- **Performance**: LCP < 2.5s, FID < 100ms, CLS < 0.1
- **Cross-Browser**: Pass on Chrome, Firefox, Safari, Edge

## Performance Testing

### Metrics Tracked

1. **Core Web Vitals**
   - Largest Contentful Paint (LCP)
   - First Input Delay (FID)
   - Cumulative Layout Shift (CLS)

2. **Custom Metrics**
   - Time to Interactive (TTI)
   - First Contentful Paint (FCP)
   - Bundle size
   - API response times

### Performance Test Example

```javascript
describe('Performance Tests', () => {
  it('meets Core Web Vitals thresholds', async () => {
    const vitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          resolve(entries);
        }).observe({ entryTypes: ['paint', 'largest-contentful-paint'] });
      });
    });
    
    expect(vitals.lcp).toBeLessThan(2500);
    expect(vitals.fcp).toBeLessThan(1800);
  });
});
```

## Accessibility Testing

### WCAG Compliance

All components are tested against WCAG 2.1 AA standards:

- **Perceivable**: Color contrast, text alternatives, captions
- **Operable**: Keyboard navigation, seizure safety, navigation
- **Understandable**: Readable text, predictable functionality
- **Robust**: Compatible with assistive technologies

### Accessibility Test Example

```javascript
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('Accessibility Tests', () => {
  it('has no accessibility violations', async () => {
    const { container } = render(<MyComponent />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('supports keyboard navigation', async () => {
    render(<Form />);
    
    await user.tab();
    expect(screen.getByLabelText('Name')).toHaveFocus();
    
    await user.tab();
    expect(screen.getByLabelText('Email')).toHaveFocus();
  });
});
```

## Cross-Browser Testing

### Supported Browsers

| Browser | Desktop | Mobile | Testing Method |
|---------|---------|--------|----------------|
| Chrome | ✅ | ✅ | Playwright |
| Firefox | ✅ | ✅ | Playwright |
| Safari | ✅ | ✅ | Playwright |
| Edge | ✅ | ❌ | Playwright |

### Browser-Specific Tests

```javascript
test.describe('Cross-Browser Compatibility', () => {
  test('works in all browsers', async ({ page, browserName }) => {
    await page.goto('/');
    
    // Browser-specific assertions
    if (browserName === 'webkit') {
      // Safari-specific tests
      await expect(page.locator('button')).toHaveCSS('appearance', 'none');
    }
    
    if (browserName === 'firefox') {
      // Firefox-specific tests
      await expect(page.locator('input')).toHaveCSS('-moz-appearance', 'none');
    }
  });
});
```

## Visual Regression Testing

### Screenshot Comparison

```javascript
describe('Visual Regression', () => {
  it('matches component screenshots', async () => {
    const { container } = render(<Button variant="primary">Test</Button>);
    expect(container.firstChild).toMatchSnapshot('button-primary');
  });
});
```

### Responsive Design Testing

```javascript
test('responsive design across viewports', async ({ page }) => {
  const viewports = [
    { width: 375, height: 667 },   // Mobile
    { width: 768, height: 1024 },  // Tablet
    { width: 1280, height: 720 }   // Desktop
  ];
  
  for (const viewport of viewports) {
    await page.setViewportSize(viewport);
    await page.screenshot({ 
      path: `screenshots/responsive-${viewport.width}x${viewport.height}.png` 
    });
  }
});
```

## Error Handling Tests

### Network Error Simulation

```javascript
describe('Error Handling', () => {
  it('handles network errors gracefully', async () => {
    server.use(
      http.get('/api/data', () => {
        return HttpResponse.error();
      })
    );
    
    render(<DataComponent />);
    
    await waitFor(() => {
      expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
    });
  });
});
```

## Mocking Strategies

### API Mocking with MSW

```javascript
// handlers.js
export const handlers = [
  http.get('/api/user', () => {
    return HttpResponse.json({
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>'
    });
  }),
  
  http.post('/api/analyze', async ({ request }) => {
    const { message } = await request.json();
    return HttpResponse.json({
      sentiment: message.includes('happy') ? 'positive' : 'neutral',
      score: 0.8
    });
  })
];
```

### Service Mocking

```javascript
// Mock external services
jest.mock('../services/groqService', () => ({
  analyzeMessage: jest.fn().mockResolvedValue({
    sentiment: 'positive',
    score: 0.8,
    insights: 'Mock insights'
  })
}));
```

## Troubleshooting

### Common Issues

#### 1. Tests Failing in CI but Passing Locally

**Cause**: Environment differences, timing issues, or dependencies

**Solutions**:
- Ensure consistent Node.js versions
- Add proper wait conditions
- Mock time-dependent functions
- Use `waitFor` for async operations

#### 2. Flaky Tests

**Cause**: Race conditions, network dependencies, timing issues

**Solutions**:
- Add explicit waits
- Mock external dependencies
- Use deterministic test data
- Increase timeouts for slow operations

#### 3. Accessibility Test Failures

**Cause**: Missing ARIA labels, color contrast issues, keyboard navigation

**Solutions**:
- Add proper ARIA attributes
- Ensure sufficient color contrast (4.5:1 ratio)
- Test with keyboard navigation
- Use semantic HTML elements

#### 4. Performance Test Inconsistencies

**Cause**: System load, network conditions, browser optimizations

**Solutions**:
- Run tests in isolated environment
- Use relative performance measurements
- Allow for reasonable variance
- Focus on trends over absolute values

### Debug Commands

```bash
# Run tests in debug mode
npm test -- --detectOpenHandles --forceExit

# Run specific test with verbose output
npm test -- --verbose Button.test.js

# Run Cypress in headed mode
npx cypress run --headed --browser chrome

# Generate coverage report
npm run test:coverage -- --coverage --watchAll=false
```

## Best Practices

### Do's

1. **Write tests first** (TDD approach when possible)
2. **Test user behavior**, not implementation details
3. **Use semantic queries** (getByRole, getByLabelText)
4. **Mock external dependencies** consistently
5. **Keep tests isolated** and independent
6. **Use descriptive test names** that explain the expected behavior
7. **Test error states** and edge cases
8. **Maintain high test coverage** (aim for 80%+ line coverage)

### Don'ts

1. **Don't test implementation details** (internal state, private methods)
2. **Don't use fragile selectors** (CSS classes, element positions)
3. **Don't skip accessibility tests**
4. **Don't ignore flaky tests** - fix them immediately
5. **Don't test external libraries** - mock them instead
6. **Don't write overly complex tests** - split them if needed
7. **Don't ignore test maintenance** - update tests when features change

## Contributing to Tests

### Adding New Tests

1. **Identify the test type** needed (unit, integration, e2e)
2. **Follow naming conventions** for files and test cases
3. **Use existing test utilities** and patterns
4. **Include accessibility checks** for UI components
5. **Add appropriate mocks** for external dependencies
6. **Update documentation** if introducing new patterns

### Code Review Checklist

- [ ] Tests cover happy path and error cases
- [ ] Accessibility tests included for UI changes
- [ ] Mocks are properly configured
- [ ] Test names are descriptive
- [ ] No hardcoded timeouts or delays
- [ ] Tests are isolated and don't depend on each other
- [ ] Performance impact considered
- [ ] Documentation updated if needed

## Resources

### Documentation
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Cypress Documentation](https://docs.cypress.io/)
- [Playwright Documentation](https://playwright.dev/)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

### Tools
- [Chrome DevTools](https://developer.chrome.com/docs/devtools/)
- [React DevTools](https://react.dev/learn/react-developer-tools)
- [Lighthouse](https://developers.google.com/web/tools/lighthouse)
- [axe DevTools](https://www.deque.com/axe/devtools/)

---

For questions or issues with testing, please refer to this documentation or reach out to the development team.