import groq
import os

# Get the API key from the environment variable or use the one from .env
api_key = os.environ.get("GROQ_API_KEY", "********************************************************")

# Initialize the Groq client
client = groq.Groq(api_key=api_key)

# Test a simple completion
try:
    chat_completion = client.chat.completions.create(
        messages=[
            {
                "role": "user",
                "content": "Hello, are you working?",
            }
        ],
        model="llama3-8b-8192",
    )
    
    # Print the response
    print("Groq API Response:")
    print(chat_completion.choices[0].message.content)
    print("\nAPI call successful!")
    
except Exception as e:
    print(f"Error: {e}")
    print("API call failed!")
