# Start with Python base image
FROM python:3.11-slim

ARG GITHUB_TOKEN
ARG PLUGIN_LIB_TAG
ARG TAG
# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive \
    PIP_NO_INPUT=1 \
    NODE_VERSION=20 \
    NEXT_TELEMETRY_DISABLED=1

# Install system dependencies including Node.js
RUN apt-get update && \
    apt-get install --no-install-recommends -y \
    curl \
    build-essential \
    software-properties-common \
    openssh-server \
    tmux \
    sudo \
    wget \
    git \
    gnupg2 \
    libssl-dev \
    zlib1g-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libncursesw5-dev \
    libffi-dev \
    liblzma-dev \
    libgdbm-dev \
    libgconf-2-4 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libgdk-pixbuf2.0-0 \
    libgtk-3-0 \
    libgbm-dev \
    libnss3-dev \
    libxss-dev \
    libasound2 \
    lsof \
    vim \
    jq \
    util-linux \
    supervisor && \
    # Install Node.js
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash - && \
    apt-get install -y nodejs && \
    apt-get install net-tools && \
    # Add MongoDB repository
    wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | gpg --dearmor > /etc/apt/trusted.gpg.d/mongodb-7.0.gpg && \
    echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list && \
    # Install MongoDB
    apt-get update && \
    apt-get install -y mongodb-org && \
    # Create MongoDB data directory
    mkdir -p /data/db && \
    chown -R root:root /data/db && \
    # Clean up
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

RUN pip install playwright
RUN playwright install chromium
RUN playwright install-deps


RUN curl -fsSL https://code-server.dev/install.sh | sh && \
   # Install any VS Code extensions you want
   code-server --install-extension ms-python.python || echo "Failed to install vscode extension"

WORKDIR /app

COPY .devcontainer/entrypoint.sh /entrypoint.sh
COPY . /app/
RUN rm -rf k8s
COPY scripts /app/scripts

COPY .devcontainer/supervisord_without_vscode.conf /etc/supervisor/conf.d/supervisord.conf
COPY .devcontainer/supervisord_code_server.conf /etc/supervisor/conf.d/supervisord_code_server.conf
RUN mkdir -p /root/.config/code-server
COPY .devcontainer/code_server_config.yaml /root/.config/code-server/config.yaml
COPY .devcontainer/context_params_app_builder.json /root/.config/context_params.json
RUN rm .devcontainer/generic_cloud_build.yaml
RUN rm -rf .git

RUN mkdir -p .emergent
RUN cat <<EOF > .emergent/emergent.yml
{
  "env_image_name": "fastapi_react_mongo_base_image_cloud_arm:${TAG}"
}
EOF

# Set up working directory for backend
WORKDIR /app/backend

RUN python -m venv /root/.venv --system-site-packages
ENV PATH="/root/.venv/bin:$PATH"
ENV VIRTUAL_ENV="/root/.venv"

RUN pip install -r requirements.txt
RUN mkdir -p /app/backend/external_integrations
RUN touch /app/backend/external_integrations/__init__.py

# Set up working directory for frontend
WORKDIR /app/frontend

RUN npm i -g yarn

# Install frontend dependencies
# COPY frontend/package.json frontend/yarn.lock ./
RUN yarn install

RUN pip install "git+https://${GITHUB_TOKEN}@github.com/emergentbase/emergent.git@${PLUGIN_LIB_TAG}#subdirectory=plugin_library"

# # Install ESLint packages globally
# RUN yarn install -g @eslint/js@9.20.0 eslint@9.20.0 eslint-plugin-react@7.37.4 globals@15.14.0 \
#     eslint-plugin-jsx-a11y@6.8.0 eslint-plugin-import@2.29.1

WORKDIR /app
RUN git init -b main
RUN git config --global user.name 'E1' && git config --global user.email '<EMAIL>'
RUN git add . && git commit --allow-empty -m 'Initial commit'
RUN git rev-parse HEAD  > /root/.git_init_commit_hash

# Expose ports
EXPOSE 3000 8001 22 1111 27017 8010
EXPOSE 55000-55999
# Set up entrypoint

WORKDIR /app
RUN chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
