# My AEI Application

This README provides instructions on how to set up and run the My AEI application, which consists of a React frontend and a FastAPI backend.

## Quick Start

To start the application:

1. **Set up environment:**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

2. **Start the backend:**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   uvicorn server:app --host 0.0.0.0 --port 8001
   ```

3. **Start the frontend (in a new terminal):**
   ```bash
   cd frontend
   yarn install
   yarn start
   ```

4. **Access the application at [http://localhost:3000](http://localhost:3000)**

For more detailed instructions, see the sections below.

## Prerequisites

- [Node.js](https://nodejs.org/) (v20 or later)
- [Yarn](https://yarnpkg.com/) package manager
- [Python](https://www.python.org/) (v3.11 or later)
- [Docker](https://www.docker.com/) (optional, for containerized deployment)

## Project Structure

- `frontend/`: React frontend application
- `backend/`: FastAPI backend application
- `scripts/`: Utility scripts
- `tests/`: Test files

## Configuration

1. Create environment files:

   ```bash
   # Copy the example environment file
   cp .env.example .env
   ```

2. Update the `.env` file with your configuration:
   - `SUPABASE_URL`: Your Supabase URL
   - `SUPABASE_KEY`: Your Supabase API key
   - `FRONTEND_URL`: URL for the frontend application
   - `BACKEND_DOCKER_URL`: URL for the backend when running in Docker
   - `MOCK_AUTH`: Set to `true` for development with mock authentication

## Running the Application Locally

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Create a Python virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Start the backend server:
   ```bash
   uvicorn server:app --host 0.0.0.0 --port 8001
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   yarn install
   ```

3. Start the development server:
   ```bash
   yarn start
   ```

4. The frontend will be available at [http://localhost:3000](http://localhost:3000)

## Running with Docker

You can also run the entire application using Docker:

1. Build the Docker image:
   ```bash
   docker build -t my-aei-app .
   ```

2. Run the container:
   ```bash
   docker run -p 80:80 -p 8001:8001 --env-file .env my-aei-app
   ```

3. Access the application at [http://localhost](http://localhost)

## Development

- Frontend code can be modified in the `frontend/` directory
- Backend code is in the `backend/` directory, with the main file being `server.py`

## Testing

Run backend tests:
```bash
python backend_test.py
```

Run frontend tests:
```bash
cd frontend
yarn test
```
