# Color System Usage Guide

## Overview

The enhanced color system provides a comprehensive token-based approach to colors, ensuring consistency, accessibility, and easy maintenance across the application.

## Quick Start

### Using Color Tokens

```jsx
// Primary colors
<div className="bg-primary-500 text-white">Primary button</div>
<div className="text-primary-700">Primary text (accessible)</div>

// Secondary colors
<div className="bg-secondary-coral-500">Coral element</div>
<div className="bg-secondary-lavender-500">Lavender element</div>
<div className="bg-secondary-sage-500">Sage element</div>

// Emotional colors
<div className="bg-emotion-positive-500">Success state</div>
<div className="text-emotion-positive-text">Success text</div>

// State colors
<div className="bg-state-error text-white">Error</div>
<div className="bg-state-warning-light text-state-warning-text">Warning</div>
```

### Dark Mode Support

All colors have dark mode variants that are automatically applied when using the `dark` class:

```jsx
// Automatic dark mode switching
<div className="text-text-primary dark:text-text-primary-dark">
  This text adapts to dark mode
</div>

<div className="bg-background-primary dark:bg-background-primary-dark">
  This background adapts to dark mode
</div>
```

### Using Gradients

```jsx
// Linear gradients
<div className="bg-gradient-primary">Primary gradient</div>
<div className="bg-gradient-emotional">Emotional gradient</div>

// Text gradients
<h1 className="text-gradient-primary">Gradient Text</h1>

// Animated gradients
<div className="bg-gradient-primary animate-gradient-x">Animated gradient</div>
```

### Glass Morphism Effects

```jsx
// Basic glass effect
<div className="bg-glass-white-80 backdrop-blur-md border border-glass-white-20">
  Glass card
</div>

// Colored glass
<div className="bg-glass-primary-20 backdrop-blur-lg">
  Primary glass overlay
</div>
```

## Accessibility Guidelines

### Text Contrast

Always use the appropriate text color variants for sufficient contrast:

**✅ Correct Usage:**
```jsx
// On white backgrounds
<p className="text-primary-700">Primary text</p>
<p className="text-secondary-coral-700">Coral text</p>

// On dark backgrounds
<p className="text-primary-300">Primary text</p>
<p className="text-secondary-coral-300">Coral text</p>
```

**❌ Incorrect Usage:**
```jsx
// Poor contrast
<p className="text-primary-500">Don't use base colors for text</p>
<p className="text-secondary-lavender-500">This has poor contrast</p>
```

### Interactive States

Use semantic color aliases for consistent interactions:

```jsx
<button className="
  bg-primary-500 
  hover:bg-primary-hover 
  active:bg-primary-active 
  disabled:bg-primary-disabled
">
  Interactive Button
</button>
```

## Common Patterns

### Cards with Depth

```jsx
<div className="bg-white dark:bg-background-elevated-dark shadow-elevated rounded-lg p-6">
  <h3 className="text-xl font-semibold text-text-primary dark:text-text-primary-dark">
    Card Title
  </h3>
  <p className="text-text-secondary dark:text-text-secondary-dark mt-2">
    Card content with proper text hierarchy
  </p>
</div>
```

### Status Messages

```jsx
// Success
<div className="bg-state-success-light border border-state-success rounded-lg p-4">
  <p className="text-state-success-text font-medium">Success message</p>
</div>

// Error
<div className="bg-state-error-light border border-state-error rounded-lg p-4">
  <p className="text-state-error-text font-medium">Error message</p>
</div>
```

### Emotional Context

```jsx
// Positive emotion
<div className="bg-emotion-positive-100 border-2 border-emotion-positive-500 rounded-lg p-6">
  <h4 className="text-emotion-positive-text font-semibold">Positive Feedback</h4>
  <p className="text-emotion-positive-800 mt-2">Great job!</p>
</div>
```

### Glass Cards

```jsx
<div className="relative">
  {/* Background */}
  <div className="absolute inset-0 bg-gradient-mesh" />
  
  {/* Glass card */}
  <div className="relative bg-glass-white-80 backdrop-blur-md border border-glass-white-30 rounded-xl p-6 shadow-glass">
    <h3 className="text-text-primary font-semibold">Glass Card</h3>
    <p className="text-text-secondary mt-2">Content with glass effect</p>
  </div>
</div>
```

## Migration Guide

### From Old to New Tokens

| Old Class | New Class | Notes |
|-----------|-----------|-------|
| `text-primary` | `text-primary-700` | For accessible text |
| `bg-coral` | `bg-secondary-coral-500` | Use full token path |
| `text-emotion-positive` | `text-emotion-positive-text` | Use semantic text variant |
| `bg-glass-white` | `bg-glass-white-80` | Specify opacity level |

## Best Practices

1. **Always test contrast**: Use the color test page at `/colors` to verify contrast ratios
2. **Use semantic tokens**: Prefer `primary-hover` over `primary-600` for hover states
3. **Consider dark mode**: Always provide dark mode variants for custom components
4. **Test with reduced motion**: Ensure glass effects work without backdrop-filter
5. **Use the right shade**: 
   - 50-200: Backgrounds and subtle elements
   - 300-400: Borders and secondary elements
   - 500: Primary brand color (UI elements only)
   - 600-700: Text and important UI elements
   - 800-950: Dark mode backgrounds and high contrast needs

## Testing

Visit `/colors` in your browser to:
- View all color scales and their hex values
- Test contrast ratios in real-time
- Preview dark mode appearances
- Experiment with gradients and glass effects
- Check shadow variations

## CSS Variables

The color system also generates CSS variables that can be used in custom CSS:

```css
/* Example custom CSS */
.custom-element {
  /* Colors are available as CSS variables */
  background: rgb(42 157 143); /* primary-500 */
  color: rgb(15 118 110); /* primary-700 */
}
```

## Troubleshooting

### Glass effects not working
- Ensure you're using `backdrop-blur-*` utilities
- Check browser support for `backdrop-filter`
- Fallback styles are automatically applied

### Dark mode not switching
- Ensure parent has `dark` class
- Use proper dark variant syntax: `dark:bg-background-primary-dark`
- Check that `darkMode: 'class'` is set in tailwind.config.js

### Gradients not animating
- Import the full tailwind.config.js (not a cached version)
- Ensure animation utilities are applied: `animate-gradient-x`

## Further Resources

- View the color test page: `/colors`
- Read the contrast analysis: `color-contrast-analysis.md`
- Check the enhanced config: `tailwind.config.js`
