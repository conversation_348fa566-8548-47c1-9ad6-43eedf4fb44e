const { test, expect } = require('@playwright/test');

test.describe('Cross-Browser Compatibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set up API mocking
    await page.route('/api/**', (route) => {
      const url = route.request().url();
      
      if (url.includes('/dashboard')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            total_relationships: 5,
            total_analyses: 23,
            recent_activity: [
              {
                id: '1',
                type: 'analysis',
                description: 'Message analyzed with <PERSON>',
                timestamp: '2024-01-15T10:30:00Z'
              }
            ],
            sentiment_timeline: [
              ['2024-01-15', 0.8],
              ['2024-01-16', 0.6]
            ],
            flag_counts: {
              'emotional_distress': 2,
              'communication_gap': 1
            },
            relationship_health_scores: [
              { name: '<PERSON>', score: 85 },
              { name: '<PERSON>', score: 92 }
            ]
          })
        });
      } else if (url.includes('/relationships')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              id: '1',
              name: '<PERSON>',
              relationship_type: 'romantic',
              created_at: '2024-01-01T00:00:00Z',
              health_score: 85
            }
          ])
        });
      } else {
        route.continue();
      }
    });
    
    await page.goto('/');
  });

  test('Home page renders correctly across browsers', async ({ page, browserName }) => {
    // Test basic page load
    await expect(page.locator('[data-testid="hero-section"]')).toBeVisible();
    await expect(page.locator('text=Welcome to My ÆI')).toBeVisible();
    
    // Test navigation
    await expect(page.locator('nav')).toBeVisible();
    await expect(page.locator('text=Dashboard')).toBeVisible();
    
    // Browser-specific checks
    if (browserName === 'webkit') {
      // Safari-specific tests
      await expect(page.locator('button')).toHaveCSS('appearance', 'none');
    }
    
    if (browserName === 'firefox') {
      // Firefox-specific tests
      await expect(page.locator('input')).toHaveCSS('-moz-appearance', 'none');
    }
  });

  test('Dashboard functionality works across browsers', async ({ page, browserName }) => {
    // Mock authentication
    await page.addInitScript(() => {
      window.localStorage.setItem('isAuthenticated', 'true');
      window.localStorage.setItem('privacyAccepted', 'true');
    });
    
    await page.goto('/dashboard');
    
    // Wait for data to load
    await expect(page.locator('[data-testid="total-relationships"]')).toContainText('5');
    await expect(page.locator('[data-testid="total-analyses"]')).toContainText('23');
    
    // Test charts rendering
    await expect(page.locator('[data-testid="sentiment-timeline-chart"]')).toBeVisible();
    
    // Browser-specific chart tests
    if (browserName === 'chromium' || browserName === 'chrome') {
      // Test Canvas rendering in Chrome
      const canvas = page.locator('canvas').first();
      await expect(canvas).toBeVisible();
    }
    
    // Test responsive behavior
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('[data-testid="dashboard-grid"]')).toHaveClass(/grid-cols-1|grid-cols-2/);
    
    await page.setViewportSize({ width: 1280, height: 720 });
    await expect(page.locator('[data-testid="dashboard-grid"]')).toHaveClass(/grid-cols-3/);
  });

  test('Form interactions work consistently', async ({ page }) => {
    await page.addInitScript(() => {
      window.localStorage.setItem('isAuthenticated', 'true');
      window.localStorage.setItem('privacyAccepted', 'true');
    });
    
    await page.goto('/message-analyzer');
    
    // Test text input
    const messageInput = page.locator('[data-testid="message-input"]');
    await messageInput.fill('Test message for cross-browser compatibility');
    await expect(messageInput).toHaveValue('Test message for cross-browser compatibility');
    
    // Test select dropdown
    const relationshipSelect = page.locator('[data-testid="relationship-select"]');
    await relationshipSelect.selectOption('Sarah');
    await expect(relationshipSelect).toHaveValue('Sarah');
    
    // Test button interactions
    const analyzeButton = page.locator('[data-testid="analyze-button"]');
    await expect(analyzeButton).toBeEnabled();
    await analyzeButton.click();
    
    // Verify form submission works
    await expect(page.locator('[data-testid="analyzing-spinner"]')).toBeVisible();
  });

  test('CSS animations and transitions work across browsers', async ({ page, browserName }) => {
    await page.goto('/');
    
    // Test hover effects
    const button = page.locator('[data-testid="get-started-button"]');
    await button.hover();
    
    // Check for transition properties
    await expect(button).toHaveCSS('transition-duration', /\d+(\.\d+)?s/);
    
    // Browser-specific animation tests
    if (browserName === 'webkit') {
      // Safari sometimes needs prefixed properties
      await expect(button).toHaveCSS('-webkit-transition-duration', /\d+(\.\d+)?s/);
    }
    
    // Test reduced motion preference
    await page.emulateMedia({ reducedMotion: 'reduce' });
    await page.reload();
    
    const animatedElement = page.locator('[data-testid="animated-element"]').first();
    if (await animatedElement.isVisible()) {
      await expect(animatedElement).toHaveClass(/motion-reduce/);
    }
  });

  test('Local storage and session management', async ({ page, context }) => {
    // Test localStorage functionality
    await page.addInitScript(() => {
      window.localStorage.setItem('testKey', 'testValue');
    });
    
    const localStorageValue = await page.evaluate(() => {
      return window.localStorage.getItem('testKey');
    });
    expect(localStorageValue).toBe('testValue');
    
    // Test sessionStorage
    await page.addInitScript(() => {
      window.sessionStorage.setItem('sessionKey', 'sessionValue');
    });
    
    const sessionStorageValue = await page.evaluate(() => {
      return window.sessionStorage.getItem('sessionKey');
    });
    expect(sessionStorageValue).toBe('sessionValue');
    
    // Test storage persistence across page reloads
    await page.reload();
    const persistedValue = await page.evaluate(() => {
      return window.localStorage.getItem('testKey');
    });
    expect(persistedValue).toBe('testValue');
  });

  test('Font rendering and typography', async ({ page, browserName }) => {
    await page.goto('/');
    
    // Test custom font loading
    await page.waitForLoadState('networkidle');
    
    const heading = page.locator('h1').first();
    
    // Check font family
    const fontFamily = await heading.evaluate((el) => {
      return window.getComputedStyle(el).fontFamily;
    });
    
    // Different browsers may report fonts differently
    expect(fontFamily).toMatch(/Inter|system-ui|sans-serif/);
    
    // Test font weight rendering
    await expect(heading).toHaveCSS('font-weight', /700|bold/);
    
    // Test font accessibility features
    await page.goto('/settings');
    await page.locator('[data-testid="font-toggle"]').click();
    await page.locator('[data-testid="font-dyslexic"]').click();
    
    await expect(page.locator('body')).toHaveClass(/font-dyslexic/);
  });

  test('Media queries and responsive design', async ({ page }) => {
    // Test desktop layout
    await page.setViewportSize({ width: 1280, height: 720 });
    await page.goto('/dashboard');
    
    await expect(page.locator('[data-testid="desktop-nav"]')).toBeVisible();
    await expect(page.locator('[data-testid="mobile-menu-toggle"]')).not.toBeVisible();
    
    // Test tablet layout
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('[data-testid="tablet-layout"]')).toBeVisible();
    
    // Test mobile layout
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('[data-testid="mobile-menu-toggle"]')).toBeVisible();
    await expect(page.locator('[data-testid="desktop-nav"]')).not.toBeVisible();
    
    // Test mobile navigation
    await page.locator('[data-testid="mobile-menu-toggle"]').click();
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
  });

  test('JavaScript ES6+ features compatibility', async ({ page, browserName }) => {
    // Test modern JavaScript features work across browsers
    const jsFeatures = await page.evaluate(() => {
      // Test arrow functions
      const arrowFunction = () => 'arrow function works';
      
      // Test template literals
      const name = 'test';
      const templateLiteral = `Hello ${name}`;
      
      // Test destructuring
      const obj = { a: 1, b: 2 };
      const { a, b } = obj;
      
      // Test async/await
      const asyncFunction = async () => {
        return new Promise(resolve => resolve('async works'));
      };
      
      // Test classes
      class TestClass {
        constructor(value) {
          this.value = value;
        }
        
        getValue() {
          return this.value;
        }
      }
      
      const instance = new TestClass('class works');
      
      return {
        arrowFunction: arrowFunction(),
        templateLiteral,
        destructuring: a + b,
        classMethod: instance.getValue()
      };
    });
    
    expect(jsFeatures.arrowFunction).toBe('arrow function works');
    expect(jsFeatures.templateLiteral).toBe('Hello test');
    expect(jsFeatures.destructuring).toBe(3);
    expect(jsFeatures.classMethod).toBe('class works');
  });

  test('WebAPI compatibility', async ({ page, browserName }) => {
    await page.goto('/');
    
    // Test Intersection Observer
    const hasIntersectionObserver = await page.evaluate(() => {
      return 'IntersectionObserver' in window;
    });
    expect(hasIntersectionObserver).toBeTruthy();
    
    // Test ResizeObserver
    const hasResizeObserver = await page.evaluate(() => {
      return 'ResizeObserver' in window;
    });
    expect(hasResizeObserver).toBeTruthy();
    
    // Test fetch API
    const hasFetch = await page.evaluate(() => {
      return 'fetch' in window;
    });
    expect(hasFetch).toBeTruthy();
    
    // Test localStorage
    const hasLocalStorage = await page.evaluate(() => {
      try {
        window.localStorage.setItem('test', 'test');
        window.localStorage.removeItem('test');
        return true;
      } catch (e) {
        return false;
      }
    });
    expect(hasLocalStorage).toBeTruthy();
  });

  test('Performance across browsers', async ({ page, browserName }) => {
    await page.goto('/');
    
    // Measure page load performance
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0
      };
    });
    
    // Verify reasonable performance across browsers
    expect(performanceMetrics.domContentLoaded).toBeLessThan(1000);
    expect(performanceMetrics.loadComplete).toBeLessThan(2000);
    
    // Browser-specific performance expectations
    if (browserName === 'chromium' || browserName === 'chrome') {
      expect(performanceMetrics.firstContentfulPaint).toBeLessThan(1500);
    }
  });

  test('Error handling consistency', async ({ page }) => {
    // Test 404 page
    await page.goto('/non-existent-page');
    await expect(page.locator('[data-testid="404-page"]')).toBeVisible();
    
    // Test API error handling
    await page.route('/api/dashboard', (route) => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });
    
    await page.goto('/dashboard');
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();
  });
});

test.describe('Mobile-Specific Tests', () => {
  test.use({ ...require('@playwright/test').devices['iPhone 12'] });
  
  test('Touch interactions work correctly', async ({ page }) => {
    await page.goto('/');
    
    // Test touch navigation
    await page.locator('[data-testid="mobile-menu-toggle"]').tap();
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
    
    // Test swipe gestures on charts
    await page.goto('/dashboard');
    await page.addInitScript(() => {
      window.localStorage.setItem('isAuthenticated', 'true');
    });
    
    const chartContainer = page.locator('[data-testid="chart-container"]');
    if (await chartContainer.isVisible()) {
      await chartContainer.hover();
      
      // Simulate swipe left
      await page.touchscreen.tap(300, 200);
      await page.touchscreen.tap(100, 200);
    }
  });
  
  test('Mobile form interactions', async ({ page }) => {
    await page.addInitScript(() => {
      window.localStorage.setItem('isAuthenticated', 'true');
    });
    
    await page.goto('/message-analyzer');
    
    // Test mobile keyboard behavior
    const input = page.locator('[data-testid="message-input"]');
    await input.tap();
    
    // Verify input focus
    await expect(input).toBeFocused();
    
    // Test mobile-optimized input
    await input.fill('Testing mobile input behavior');
    await expect(input).toHaveValue('Testing mobile input behavior');
  });
});