const { test, expect, devices } = require('@playwright/test');

// Test multiple device viewports
const testDevices = [
  { name: 'iPhone 12', device: devices['iPhone 12'] },
  { name: 'iPhone SE', device: devices['iPhone SE'] },
  { name: 'Pixel 5', device: devices['Pixel 5'] },
  { name: 'iPad', device: devices['iPad'] },
  { name: 'Galaxy S21', device: devices['Galaxy S21'] }
];

test.describe('Mobile Responsiveness Tests', () => {
  testDevices.forEach(({ name, device }) => {
    test.describe(`${name} Tests`, () => {
      test.use(device);

      test.beforeEach(async ({ page }) => {
        await page.goto('/mobile-test');
      });

      test('Should have proper touch target sizes', async ({ page }) => {
        // Test button touch targets
        const buttons = await page.$$('button');
        for (const button of buttons) {
          const box = await button.boundingBox();
          if (box) {
            expect(box.width).toBeGreaterThanOrEqual(48);
            expect(box.height).toBeGreaterThanOrEqual(48);
          }
        }

        // Test form input touch targets
        const inputs = await page.$$('input[type="text"], select');
        for (const input of inputs) {
          const box = await input.boundingBox();
          if (box) {
            expect(box.height).toBeGreaterThanOrEqual(48);
          }
        }
      });

      test('Should have fluid typography', async ({ page }) => {
        // Check that text scales properly
        const h1 = await page.$('h1');
        const h1FontSize = await h1.evaluate(el => 
          window.getComputedStyle(el).fontSize
        );
        
        const body = await page.$('p');
        const bodyFontSize = await body.evaluate(el => 
          window.getComputedStyle(el).fontSize
        );

        // Font sizes should be responsive
        expect(parseInt(h1FontSize)).toBeGreaterThan(16);
        expect(parseInt(bodyFontSize)).toBeGreaterThanOrEqual(16);
      });

      test('Should support swipe gestures on modal', async ({ page }) => {
        // Open swipe modal
        await page.click('text=Open Swipe-to-Dismiss Modal');
        
        // Wait for modal to appear
        await page.waitForSelector('[role="dialog"]');
        
        // Simulate swipe down gesture
        const modal = await page.$('[role="dialog"] > div > div');
        const box = await modal.boundingBox();
        
        if (box) {
          await page.mouse.move(box.x + box.width / 2, box.y + 50);
          await page.mouse.down();
          await page.mouse.move(box.x + box.width / 2, box.y + 200, { steps: 10 });
          await page.mouse.up();
        }
        
        // Modal should be closed
        await expect(page.locator('[role="dialog"]')).not.toBeVisible();
      });

      test('Should have responsive images with srcset', async ({ page }) => {
        const images = await page.$$('img[srcset]');
        
        for (const img of images) {
          const srcset = await img.getAttribute('srcset');
          const sizes = await img.getAttribute('sizes');
          
          expect(srcset).toBeTruthy();
          expect(sizes).toBeTruthy();
          
          // Check that srcset contains multiple sources
          const sources = srcset.split(',');
          expect(sources.length).toBeGreaterThan(1);
        }
      });

      test('Should show bottom navigation on mobile only', async ({ page, viewport }) => {
        const bottomNav = await page.$('.bottom-nav');
        
        if (viewport.width < 640) {
          // Should be visible on mobile
          await expect(page.locator('.bottom-nav')).toBeVisible();
        } else {
          // Should be hidden on larger screens
          await expect(page.locator('.bottom-nav')).not.toBeVisible();
        }
      });

      test('Should have proper grid layout', async ({ page, viewport }) => {
        const gridItems = await page.$$('.grid-mobile > *');
        
        if (gridItems.length > 0) {
          const firstItem = gridItems[0];
          const box = await firstItem.boundingBox();
          
          if (viewport.width < 640) {
            // Should be full width on mobile
            expect(box.width).toBeCloseTo(viewport.width - 32, 10); // accounting for padding
          }
        }
      });

      test('Should handle horizontal scrolling', async ({ page }) => {
        const swipeableContainer = await page.$('.swipeable-x');
        
        if (swipeableContainer) {
          // Check if container is scrollable
          const isScrollable = await swipeableContainer.evaluate(el => 
            el.scrollWidth > el.clientWidth
          );
          
          expect(isScrollable).toBeTruthy();
          
          // Test scroll
          await swipeableContainer.evaluate(el => el.scrollLeft = 100);
          const scrollLeft = await swipeableContainer.evaluate(el => el.scrollLeft);
          expect(scrollLeft).toBeGreaterThan(0);
        }
      });

      test('Should prevent zoom on input focus', async ({ page }) => {
        const input = await page.$('input[type="text"]');
        
        if (input) {
          const fontSize = await input.evaluate(el => 
            window.getComputedStyle(el).fontSize
          );
          
          // Font size should be at least 16px to prevent zoom on iOS
          expect(parseInt(fontSize)).toBeGreaterThanOrEqual(16);
        }
      });
    });
  });

  test.describe('Breakpoint Tests', () => {
    const breakpoints = [
      { name: 'Mobile', width: 375, height: 667 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Desktop', width: 1280, height: 800 },
      { name: 'Wide', width: 1920, height: 1080 }
    ];

    breakpoints.forEach(({ name, width, height }) => {
      test(`Should render correctly at ${name} breakpoint`, async ({ page }) => {
        await page.setViewportSize({ width, height });
        await page.goto('/mobile-test');
        
        // Take screenshot for visual comparison
        await page.screenshot({ 
          path: `./screenshots/mobile-${name.toLowerCase()}.png`,
          fullPage: true 
        });
        
        // Test specific breakpoint behaviors
        if (width < 640) {
          // Mobile specific tests
          await expect(page.locator('.bottom-nav')).toBeVisible();
          
          // Buttons should stack vertically
          const buttonGroup = await page.$('.btn-group-mobile');
          const isStacked = await buttonGroup.evaluate(el => {
            const buttons = el.querySelectorAll('button');
            return buttons.length > 1 && 
              buttons[0].getBoundingClientRect().top < 
              buttons[1].getBoundingClientRect().top;
          });
          expect(isStacked).toBeTruthy();
        } else {
          // Desktop specific tests
          await expect(page.locator('.bottom-nav')).not.toBeVisible();
        }
      });
    });
  });

  test.describe('Performance Tests', () => {
    test('Should load images lazily', async ({ page }) => {
      await page.goto('/mobile-test');
      
      // Check that images have loading="lazy"
      const images = await page.$$('img');
      for (const img of images) {
        const loading = await img.getAttribute('loading');
        expect(loading).toBe('lazy');
      }
    });

    test('Should have optimized animations on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto('/mobile-test');
      
      // Check for reduced motion support
      await page.emulateMedia({ reducedMotion: 'reduce' });
      
      const animatedElement = await page.$('.animate-mobile-optimized');
      if (animatedElement) {
        const animation = await animatedElement.evaluate(el => 
          window.getComputedStyle(el).animationDuration
        );
        
        // Animation should be very short or disabled
        expect(parseFloat(animation)).toBeLessThanOrEqual(0.2);
      }
    });
  });

  test.describe('Accessibility Tests', () => {
    test('Should maintain focus visibility on all interactive elements', async ({ page }) => {
      await page.goto('/mobile-test');
      
      // Tab through all interactive elements
      const interactiveElements = await page.$$('button, a, input, select, [tabindex]');
      
      for (const element of interactiveElements) {
        await element.focus();
        
        // Check if element has visible focus indicator
        const outline = await element.evaluate(el => {
          const styles = window.getComputedStyle(el);
          return styles.outline || styles.boxShadow;
        });
        
        expect(outline).toBeTruthy();
      }
    });

    test('Should have proper ARIA labels', async ({ page }) => {
      await page.goto('/mobile-test');
      
      // Check icon buttons have aria-labels
      const iconButtons = await page.$$('button:has(svg)');
      
      for (const button of iconButtons) {
        const ariaLabel = await button.getAttribute('aria-label');
        const text = await button.textContent();
        
        // Button should have either aria-label or text content
        expect(ariaLabel || text.trim()).toBeTruthy();
      }
    });
  });
});

// Helper function to create visual regression tests
test.describe('Visual Regression Tests', () => {
  const devices = ['iPhone 12', 'iPad', 'Desktop'];
  
  devices.forEach(device => {
    test(`Visual regression for ${device}`, async ({ page, browserName }) => {
      if (device !== 'Desktop') {
        await page.emulate(devices[device]);
      }
      
      await page.goto('/mobile-test');
      await page.waitForLoadState('networkidle');
      
      // Wait for any animations to complete
      await page.waitForTimeout(1000);
      
      await page.screenshot({
        path: `./screenshots/regression-${device.toLowerCase()}-${browserName}.png`,
        fullPage: true,
        animations: 'disabled'
      });
    });
  });
});
