#!/usr/bin/env node

/**
 * Comprehensive Test Runner for My ÆI Application
 * 
 * This script orchestrates all testing suites including:
 * - Unit tests (Jest)
 * - Integration tests
 * - End-to-end tests (Cypress)
 * - Cross-browser tests (Playwright)
 * - Accessibility tests
 * - Performance tests
 * - Visual regression tests
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

class TestRunner {
  constructor() {
    this.results = {
      unit: null,
      integration: null,
      e2e: null,
      crossBrowser: null,
      accessibility: null,
      performance: null,
      visual: null
    };
    this.startTime = Date.now();
  }

  async runCommand(command, options = {}) {
    return new Promise((resolve, reject) => {
      const child = spawn('npm', ['run', command], {
        stdio: 'inherit',
        shell: true,
        ...options
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, code });
        } else {
          resolve({ success: false, code });
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  async runUnitTests() {
    console.log('🧪 Running Unit Tests...');
    const result = await this.runCommand('test:coverage');
    this.results.unit = result;
    
    if (result.success) {
      console.log('✅ Unit tests passed');
    } else {
      console.log('❌ Unit tests failed');
    }
    
    return result;
  }

  async runIntegrationTests() {
    console.log('🔗 Running Integration Tests...');
    // Integration tests are part of the unit test suite but focus on component interactions
    const result = await this.runCommand('test', {
      env: { ...process.env, REACT_APP_TEST_MODE: 'integration' }
    });
    this.results.integration = result;
    
    if (result.success) {
      console.log('✅ Integration tests passed');
    } else {
      console.log('❌ Integration tests failed');
    }
    
    return result;
  }

  async runE2ETests() {
    console.log('🌐 Running End-to-End Tests...');
    
    // Start the development server
    console.log('🚀 Starting development server...');
    const serverProcess = spawn('npm', ['start'], {
      stdio: 'pipe',
      shell: true,
      detached: false
    });

    // Wait for server to be ready
    await this.waitForServer('http://localhost:3000', 60000);
    
    try {
      const result = await this.runCommand('test:e2e');
      this.results.e2e = result;
      
      if (result.success) {
        console.log('✅ E2E tests passed');
      } else {
        console.log('❌ E2E tests failed');
      }
      
      return result;
    } finally {
      // Kill the server process
      serverProcess.kill('SIGTERM');
    }
  }

  async runCrossBrowserTests() {
    console.log('🌍 Running Cross-Browser Tests...');
    
    // Start the development server
    const serverProcess = spawn('npm', ['start'], {
      stdio: 'pipe',
      shell: true,
      detached: false
    });

    await this.waitForServer('http://localhost:3000', 60000);
    
    try {
      const result = await this.runCommand('test:playwright');
      this.results.crossBrowser = result;
      
      if (result.success) {
        console.log('✅ Cross-browser tests passed');
      } else {
        console.log('❌ Cross-browser tests failed');
      }
      
      return result;
    } finally {
      serverProcess.kill('SIGTERM');
    }
  }

  async runAccessibilityTests() {
    console.log('♿ Running Accessibility Tests...');
    const result = await this.runCommand('test:accessibility');
    this.results.accessibility = result;
    
    if (result.success) {
      console.log('✅ Accessibility tests passed');
    } else {
      console.log('❌ Accessibility tests failed');
    }
    
    return result;
  }

  async runPerformanceTests() {
    console.log('⚡ Running Performance Tests...');
    const result = await this.runCommand('test:performance');
    this.results.performance = result;
    
    if (result.success) {
      console.log('✅ Performance tests passed');
    } else {
      console.log('❌ Performance tests failed');
    }
    
    return result;
  }

  async runVisualRegressionTests() {
    console.log('👀 Running Visual Regression Tests...');
    const result = await this.runCommand('test:visual');
    this.results.visual = result;
    
    if (result.success) {
      console.log('✅ Visual regression tests passed');
    } else {
      console.log('❌ Visual regression tests failed');
    }
    
    return result;
  }

  async waitForServer(url, timeout = 30000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const response = await fetch(url);
        if (response.ok) {
          console.log('✅ Server is ready');
          return;
        }
      } catch (error) {
        // Server not ready yet
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error('Server failed to start within timeout period');
  }

  generateReport() {
    const endTime = Date.now();
    const duration = (endTime - this.startTime) / 1000;
    
    const report = {
      timestamp: new Date().toISOString(),
      duration: `${duration}s`,
      results: this.results,
      summary: {
        total: Object.keys(this.results).length,
        passed: Object.values(this.results).filter(r => r && r.success).length,
        failed: Object.values(this.results).filter(r => r && !r.success).length,
        skipped: Object.values(this.results).filter(r => r === null).length
      }
    };
    
    // Write report to file
    const reportPath = path.join(__dirname, 'test-results', 'test-report.json');
    fs.mkdirSync(path.dirname(reportPath), { recursive: true });
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 Test Summary:');
    console.log(`Total Tests: ${report.summary.total}`);
    console.log(`✅ Passed: ${report.summary.passed}`);
    console.log(`❌ Failed: ${report.summary.failed}`);
    console.log(`⏭️ Skipped: ${report.summary.skipped}`);
    console.log(`⏱️ Duration: ${report.duration}`);
    console.log(`📄 Report saved to: ${reportPath}`);
    
    return report;
  }

  async runAll() {
    console.log('🚀 Starting Comprehensive Test Suite...\n');
    
    try {
      // Run tests in sequence to avoid resource conflicts
      await this.runUnitTests();
      await this.runIntegrationTests();
      await this.runAccessibilityTests();
      await this.runPerformanceTests();
      await this.runVisualRegressionTests();
      await this.runE2ETests();
      await this.runCrossBrowserTests();
      
    } catch (error) {
      console.error('❌ Test suite failed with error:', error);
    } finally {
      const report = this.generateReport();
      
      // Exit with error code if any tests failed
      const hasFailures = report.summary.failed > 0;
      process.exit(hasFailures ? 1 : 0);
    }
  }

  async runSubset(tests) {
    console.log(`🚀 Running Test Subset: ${tests.join(', ')}\n`);
    
    try {
      for (const test of tests) {
        switch (test) {
          case 'unit':
            await this.runUnitTests();
            break;
          case 'integration':
            await this.runIntegrationTests();
            break;
          case 'e2e':
            await this.runE2ETests();
            break;
          case 'cross-browser':
            await this.runCrossBrowserTests();
            break;
          case 'accessibility':
            await this.runAccessibilityTests();
            break;
          case 'performance':
            await this.runPerformanceTests();
            break;
          case 'visual':
            await this.runVisualRegressionTests();
            break;
          default:
            console.warn(`⚠️ Unknown test type: ${test}`);
        }
      }
    } catch (error) {
      console.error('❌ Test subset failed with error:', error);
    } finally {
      const report = this.generateReport();
      const hasFailures = report.summary.failed > 0;
      process.exit(hasFailures ? 1 : 0);
    }
  }
}

// CLI interface
const args = process.argv.slice(2);
const runner = new TestRunner();

if (args.length === 0 || args.includes('--all')) {
  runner.runAll();
} else if (args.includes('--help')) {
  console.log(`
🧪 My ÆI Test Runner

Usage: node test-runner.js [options] [test-types]

Options:
  --all                 Run all test suites (default)
  --help               Show this help message

Test Types:
  unit                 Unit tests with Jest
  integration          Integration tests
  e2e                  End-to-end tests with Cypress
  cross-browser        Cross-browser tests with Playwright
  accessibility        Accessibility tests with axe
  performance          Performance tests
  visual               Visual regression tests

Examples:
  node test-runner.js --all
  node test-runner.js unit integration
  node test-runner.js e2e cross-browser
  node test-runner.js accessibility performance
  `);
} else {
  runner.runSubset(args);
}

module.exports = TestRunner;