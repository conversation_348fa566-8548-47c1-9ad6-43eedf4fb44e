/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html"
  ],
  darkMode: 'class', // Enable dark mode support
  theme: {
    extend: {
      colors: {
        // Primary color scale with semantic tokens
        primary: {
          50: '#f0fdfa',
          100: '#ccfbf1',
          200: '#99f6e4',
          300: '#5eead4',
          400: '#2dd4bf',
          500: '#2A9D8F', // Base primary
          600: '#0d9488',
          700: '#0f766e',
          800: '#115e59',
          900: '#134e4a',
          950: '#042f2e', // Added for dark mode
          // Semantic aliases
          DEFAULT: '#2A9D8F',
          hover: '#0d9488',
          active: '#0f766e',
          disabled: '#5eead4',
        },

        // Secondary color scales with semantic tokens
        secondary: {
          coral: {
            50: '#fef2f2',
            100: '#fee2e2',
            200: '#fecaca',
            300: '#fca5a5',
            400: '#f87171',
            500: '#E76F51', // Base coral
            600: '#dc2626',
            700: '#b91c1c',
            800: '#991b1b',
            900: '#7f1d1d',
            950: '#450a0a',
            DEFAULT: '#E76F51',
            hover: '#dc2626',
            active: '#b91c1c',
          },
          lavender: {
            50: '#f5f3ff',
            100: '#ede9fe',
            200: '#ddd6fe',
            300: '#c4b5fd',
            400: '#a78bfa',
            500: '#9D8DF1', // Base lavender
            600: '#8b5cf6',
            700: '#7c3aed',
            800: '#6d28d9',
            900: '#5b21b6',
            950: '#2e1065',
            DEFAULT: '#9D8DF1',
            hover: '#8b5cf6',
            active: '#7c3aed',
          },
          sage: {
            50: '#f0fdf4',
            100: '#dcfce7',
            200: '#bbf7d0',
            300: '#86efac',
            400: '#4ade80',
            500: '#87A878', // Base sage
            600: '#16a34a',
            700: '#15803d',
            800: '#166534',
            900: '#14532d',
            950: '#052e16',
            DEFAULT: '#87A878',
            hover: '#16a34a',
            active: '#15803d',
          },
        },

        // Emotional state colors with WCAG AA/AAA compliance
        emotion: {
          positive: {
            50: '#f0fdf4',
            100: '#dcfce7',
            200: '#bbf7d0',
            300: '#86efac',
            400: '#4ade80',
            500: '#4AD295', // Base positive
            600: '#16a34a',
            700: '#15803d',
            800: '#166534',
            900: '#14532d',
            950: '#052e16',
            DEFAULT: '#4AD295',
            text: '#15803d', // For text on light backgrounds
            'text-dark': '#86efac', // For text on dark backgrounds
          },
          neutral: {
            50: '#f8fafc',
            100: '#f1f5f9',
            200: '#e2e8f0',
            300: '#cbd5e1',
            400: '#94a3b8',
            500: '#A5B4FC', // Base neutral
            600: '#475569',
            700: '#334155',
            800: '#1e293b',
            900: '#0f172a',
            950: '#020617',
            DEFAULT: '#A5B4FC',
            text: '#334155',
            'text-dark': '#cbd5e1',
          },
          negative: {
            50: '#fef2f2',
            100: '#fee2e2',
            200: '#fecaca',
            300: '#fca5a5',
            400: '#f87171',
            500: '#E56B6F', // Base negative
            600: '#dc2626',
            700: '#b91c1c',
            800: '#991b1b',
            900: '#7f1d1d',
            950: '#450a0a',
            DEFAULT: '#E56B6F',
            text: '#b91c1c',
            'text-dark': '#fca5a5',
          },
        },

        // Enhanced text colors for light and dark modes
        text: {
          primary: {
            DEFAULT: '#111827',
            dark: '#f9fafb',
          },
          secondary: {
            DEFAULT: '#6b7280',
            dark: '#d1d5db',
          },
          tertiary: {
            DEFAULT: '#9ca3af',
            dark: '#9ca3af',
          },
          disabled: {
            DEFAULT: '#d1d5db',
            dark: '#4b5563',
          },
          inverse: {
            DEFAULT: '#f9fafb',
            dark: '#111827',
          },
          link: {
            DEFAULT: '#2A9D8F',
            hover: '#0d9488',
            visited: '#7c3aed',
            dark: '#5eead4',
            'dark-hover': '#2dd4bf',
            'dark-visited': '#c4b5fd',
          },
        },

        // Enhanced background colors for light and dark modes
        background: {
          primary: {
            DEFAULT: '#FFFFFF',
            dark: '#0f172a',
          },
          secondary: {
            DEFAULT: '#f9fafb',
            dark: '#1e293b',
          },
          tertiary: {
            DEFAULT: '#f3f4f6',
            dark: '#334155',
          },
          elevated: {
            DEFAULT: '#FFFFFF',
            dark: '#1e293b',
          },
          overlay: {
            DEFAULT: 'rgba(0, 0, 0, 0.5)',
            dark: 'rgba(0, 0, 0, 0.7)',
          },
        },

        // Enhanced border colors
        border: {
          primary: {
            DEFAULT: '#e5e7eb',
            dark: '#334155',
          },
          secondary: {
            DEFAULT: '#d1d5db',
            dark: '#475569',
          },
          tertiary: {
            DEFAULT: '#f3f4f6',
            dark: '#1e293b',
          },
          focus: {
            DEFAULT: '#2A9D8F',
            dark: '#5eead4',
          },
        },

        // Enhanced glassmorphism colors with opacity variants
        glass: {
          white: {
            10: 'rgba(255, 255, 255, 0.1)',
            20: 'rgba(255, 255, 255, 0.2)',
            30: 'rgba(255, 255, 255, 0.3)',
            40: 'rgba(255, 255, 255, 0.4)',
            50: 'rgba(255, 255, 255, 0.5)',
            60: 'rgba(255, 255, 255, 0.6)',
            70: 'rgba(255, 255, 255, 0.7)',
            80: 'rgba(255, 255, 255, 0.8)',
            90: 'rgba(255, 255, 255, 0.9)',
            DEFAULT: 'rgba(255, 255, 255, 0.8)',
          },
          black: {
            10: 'rgba(0, 0, 0, 0.1)',
            20: 'rgba(0, 0, 0, 0.2)',
            30: 'rgba(0, 0, 0, 0.3)',
            40: 'rgba(0, 0, 0, 0.4)',
            50: 'rgba(0, 0, 0, 0.5)',
            60: 'rgba(0, 0, 0, 0.6)',
            70: 'rgba(0, 0, 0, 0.7)',
            80: 'rgba(0, 0, 0, 0.8)',
            90: 'rgba(0, 0, 0, 0.9)',
            DEFAULT: 'rgba(0, 0, 0, 0.5)',
          },
          primary: {
            10: 'rgba(42, 157, 143, 0.1)',
            20: 'rgba(42, 157, 143, 0.2)',
            30: 'rgba(42, 157, 143, 0.3)',
            40: 'rgba(42, 157, 143, 0.4)',
            50: 'rgba(42, 157, 143, 0.5)',
            DEFAULT: 'rgba(42, 157, 143, 0.2)',
          },
          lavender: {
            10: 'rgba(157, 141, 241, 0.1)',
            20: 'rgba(157, 141, 241, 0.2)',
            30: 'rgba(157, 141, 241, 0.3)',
            40: 'rgba(157, 141, 241, 0.4)',
            50: 'rgba(157, 141, 241, 0.5)',
            DEFAULT: 'rgba(157, 141, 241, 0.2)',
          },
        },

        // State colors with opacity variants
        state: {
          error: {
            DEFAULT: '#dc2626',
            light: '#fef2f2',
            dark: '#7f1d1d',
            text: '#b91c1c',
          },
          warning: {
            DEFAULT: '#f59e0b',
            light: '#fef3c7',
            dark: '#92400e',
            text: '#d97706',
          },
          success: {
            DEFAULT: '#10b981',
            light: '#d1fae5',
            dark: '#064e3b',
            text: '#059669',
          },
          info: {
            DEFAULT: '#3b82f6',
            light: '#dbeafe',
            dark: '#1e3a8a',
            text: '#2563eb',
          },
        },
      },

      // Gradient definitions
      backgroundImage: {
        // Linear gradients
        'gradient-primary': 'linear-gradient(135deg, #2A9D8F 0%, #0d9488 100%)',
        'gradient-secondary': 'linear-gradient(135deg, #9D8DF1 0%, #E76F51 100%)',
        'gradient-emotional': 'linear-gradient(135deg, #4AD295 0%, #A5B4FC 50%, #E56B6F 100%)',
        'gradient-warm': 'linear-gradient(135deg, #E76F51 0%, #f59e0b 50%, #fbbf24 100%)',
        'gradient-cool': 'linear-gradient(135deg, #2A9D8F 0%, #3b82f6 50%, #9D8DF1 100%)',
        'gradient-sunset': 'linear-gradient(135deg, #E76F51 0%, #9D8DF1 50%, #2A9D8F 100%)',
        
        // Radial gradients
        'gradient-radial-primary': 'radial-gradient(circle, #2A9D8F 0%, #0d9488 100%)',
        'gradient-radial-glow': 'radial-gradient(circle, rgba(157, 141, 241, 0.4) 0%, transparent 70%)',
        
        // Mesh gradients
        'gradient-mesh': `
          radial-gradient(at 40% 20%, rgba(42, 157, 143, 0.3) 0px, transparent 50%),
          radial-gradient(at 80% 0%, rgba(157, 141, 241, 0.3) 0px, transparent 50%),
          radial-gradient(at 0% 50%, rgba(231, 111, 81, 0.3) 0px, transparent 50%),
          radial-gradient(at 80% 50%, rgba(135, 168, 120, 0.3) 0px, transparent 50%),
          radial-gradient(at 0% 100%, rgba(74, 210, 149, 0.3) 0px, transparent 50%)
        `,
      },

      // Enhanced shadow system with colored shadows
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'elevated': '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'floating': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'inner-soft': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
        'inner-deep': 'inset 0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        
        // Colored shadows
        'glow-primary': '0 0 20px rgba(42, 157, 143, 0.4)',
        'glow-coral': '0 0 20px rgba(231, 111, 81, 0.4)',
        'glow-lavender': '0 0 20px rgba(157, 141, 241, 0.4)',
        'glow-success': '0 0 20px rgba(74, 210, 149, 0.4)',
        'glow-error': '0 0 20px rgba(229, 107, 111, 0.4)',
        
        // Glass shadows
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
        'glass-elevated': '0 8px 32px 0 rgba(31, 38, 135, 0.37), 0 2px 8px 0 rgba(31, 38, 135, 0.2)',
      },

      // Animation keyframes
      animation: {
        'gradient-x': 'gradient-x 15s ease infinite',
        'gradient-y': 'gradient-y 15s ease infinite',
        'gradient-xy': 'gradient-xy 15s ease infinite',
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-soft': 'bounce-soft 2s infinite',
        'shimmer': 'shimmer 2.5s infinite',
        'subtle-glow': 'subtle-glow 4s ease-in-out infinite',
        'fade-in': 'fade-in 0.5s ease-out',
        'slide-up': 'slide-up 0.5s ease-out',
      },

      keyframes: {
        'gradient-x': {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center'
          },
        },
        'gradient-y': {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'center top'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'center bottom'
          },
        },
        'gradient-xy': {
          '0%, 100%': {
            'background-size': '400% 400%',
            'background-position': 'left top'
          },
          '25%': {
            'background-size': '400% 400%',
            'background-position': 'right top'
          },
          '50%': {
            'background-size': '400% 400%',
            'background-position': 'right bottom'
          },
          '75%': {
            'background-size': '400% 400%',
            'background-position': 'left bottom'
          },
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'bounce-soft': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        'shimmer': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'subtle-glow': {
          '0%, 100%': { 
            filter: 'drop-shadow(0 0 20px rgba(42, 157, 143, 0.3)) drop-shadow(0 0 40px rgba(42, 157, 143, 0.1))'
          },
          '50%': { 
            filter: 'drop-shadow(0 0 30px rgba(42, 157, 143, 0.5)) drop-shadow(0 0 50px rgba(42, 157, 143, 0.3))'
          },
        },
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'slide-up': {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },

      // Enhanced backdrop blur utilities
      backdropBlur: {
        'xs': '2px',
        'sm': '4px',
        'md': '8px',
        'lg': '12px',
        'xl': '16px',
        '2xl': '24px',
        '3xl': '40px',
      },

      // Enhanced opacity scale
      opacity: {
        '5': '0.05',
        '10': '0.1',
        '15': '0.15',
        '20': '0.2',
        '25': '0.25',
        '30': '0.3',
        '35': '0.35',
        '40': '0.4',
        '45': '0.45',
        '50': '0.5',
        '55': '0.55',
        '60': '0.6',
        '65': '0.65',
        '70': '0.7',
        '75': '0.75',
        '80': '0.8',
        '85': '0.85',
        '90': '0.9',
        '95': '0.95',
        '100': '1',
      },

      fontFamily: {
        // Primary font (for body text)
        sans: ['Orbitron', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        // Secondary font (for headings)
        serif: ['Orbitron', 'ui-serif', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
        // Special font for dyslexia (will be used in TY-05)
        dyslexic: ['Orbitron', 'OpenDyslexic', 'ui-sans-serif', 'system-ui', 'sans-serif'],
      },
      
      // Enhanced spacing system based on 8px base unit
      spacing: {
        'xs': '0.25rem',    // 4px
        'sm': '0.5rem',     // 8px - base unit
        'md': '1rem',       // 16px - 2x base
        'lg': '1.5rem',     // 24px - 3x base
        'xl': '2rem',       // 32px - 4x base
        '2xl': '3rem',      // 48px - 6x base
        '3xl': '4rem',      // 64px - 8x base
        '4xl': '6rem',      // 96px - 12x base
        '5xl': '8rem',      // 128px - 16x base
      },

      fontSize: {
        // Enhanced typographic scale with better hierarchy
        'xs': ['0.75rem', { lineHeight: '1rem' }],      // 12px
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],  // 14px
        'base': ['1rem', { lineHeight: '1.5rem' }],     // 16px - base size
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],  // 18px
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],   // 20px
        '2xl': ['1.5rem', { lineHeight: '2rem' }],      // 24px
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],   // 36px
        '5xl': ['3rem', { lineHeight: '1' }],           // 48px
        '6xl': ['3.75rem', { lineHeight: '1' }],        // 60px
        '7xl': ['4.5rem', { lineHeight: '1' }],         // 72px
        '8xl': ['6rem', { lineHeight: '1' }],           // 96px
        '9xl': ['8rem', { lineHeight: '1' }],           // 128px
      },
    },
  },
  plugins: [
    // Plugin for text gradient utilities
    function({ addUtilities }) {
      const newUtilities = {
        '.text-gradient-primary': {
          'background': 'linear-gradient(135deg, #2A9D8F 0%, #0d9488 100%)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
        },
        '.text-gradient-secondary': {
          'background': 'linear-gradient(135deg, #9D8DF1 0%, #E76F51 100%)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
        },
        '.text-gradient-emotional': {
          'background': 'linear-gradient(135deg, #4AD295 0%, #A5B4FC 50%, #E56B6F 100%)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
        },
      }
      addUtilities(newUtilities)
    }
  ],
};
