// Import commands.js using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Import Cypress Testing Library commands
import '@testing-library/cypress/add-commands';

// Import accessibility testing commands
import 'cypress-axe';

// Global error handling
Cypress.on('uncaught:exception', (err, runnable) => {
  // Returning false here prevents <PERSON><PERSON> from failing the test on uncaught exceptions
  // We can log the error for debugging purposes
  console.error('Uncaught exception:', err);
  
  // Don't fail tests on Auth0 errors that are expected
  if (err.message.includes('Auth0')) {
    return false;
  }
  
  // Don't fail on ResizeObserver errors (common with chart libraries)
  if (err.message.includes('ResizeObserver')) {
    return false;
  }
  
  return true;
});

// Global before hook to set up test environment
beforeEach(() => {
  // Clear localStorage and sessionStorage before each test
  cy.window().then((win) => {
    win.localStorage.clear();
    win.sessionStorage.clear();
  });
  
  // Set up API interceptors for consistent testing
  cy.setupApiMocks();
  
  // Accept privacy banner by default
  cy.window().then((win) => {
    win.localStorage.setItem('privacyAccepted', 'true');
  });
});

// Global after hook for cleanup
afterEach(() => {
  // Take screenshot on failure
  cy.screenshot({ capture: 'viewport', onlyOnFailure: true });
  
  // Log any console errors
  cy.window().then((win) => {
    const errors = win.console._errors || [];
    if (errors.length > 0) {
      cy.task('log', `Console errors: ${JSON.stringify(errors)}`);
    }
  });
});

// Custom viewport presets
Cypress.Commands.add('setMobileViewport', () => {
  cy.viewport(375, 667); // iPhone SE
});

Cypress.Commands.add('setTabletViewport', () => {
  cy.viewport(768, 1024); // iPad
});

Cypress.Commands.add('setDesktopViewport', () => {
  cy.viewport(1280, 720); // Desktop
});

// Performance testing helpers
Cypress.Commands.add('measurePerformance', (callback) => {
  cy.window().then((win) => {
    const startTime = win.performance.now();
    callback();
    cy.then(() => {
      const endTime = win.performance.now();
      const duration = endTime - startTime;
      cy.task('log', `Performance measurement: ${duration}ms`);
      return duration;
    });
  });
});

// Web vitals measurement
Cypress.Commands.add('measureWebVitals', () => {
  cy.window().then((win) => {
    return new Promise((resolve) => {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        const vitals = {};
        
        getCLS((metric) => vitals.cls = metric.value);
        getFID((metric) => vitals.fid = metric.value);
        getFCP((metric) => vitals.fcp = metric.value);
        getLCP((metric) => vitals.lcp = metric.value);
        getTTFB((metric) => vitals.ttfb = metric.value);
        
        setTimeout(() => {
          cy.task('log', `Web Vitals: ${JSON.stringify(vitals)}`);
          resolve(vitals);
        }, 1000);
      });
    });
  });
});