// Custom commands for My ÆI application testing

// Authentication commands
Cypress.Commands.add('login', (email = '<EMAIL>', password = 'testpassword') => {
  cy.session([email, password], () => {
    cy.visit('/');
    cy.get('[data-testid="login-button"]').click();
    cy.origin(Cypress.env('auth0Domain'), { args: { email, password } }, ({ email, password }) => {
      cy.get('input[name="email"]').type(email);
      cy.get('input[name="password"]').type(password);
      cy.get('button[type="submit"]').click();
    });
    cy.url().should('include', '/dashboard');
  });
});

Cypress.Commands.add('logout', () => {
  cy.get('[data-testid="user-menu"]').click();
  cy.get('[data-testid="logout-button"]').click();
  cy.url().should('eq', Cypress.config().baseUrl + '/');
});

// API mocking commands
Cypress.Commands.add('setupApiMocks', () => {
  // Mock dashboard data
  cy.intercept('GET', '/api/dashboard', {
    fixture: 'dashboard.json'
  }).as('getDashboard');
  
  // Mock relationships data
  cy.intercept('GET', '/api/relationships', {
    fixture: 'relationships.json'
  }).as('getRelationships');
  
  // Mock analysis endpoint
  cy.intercept('POST', '/api/analyze', {
    fixture: 'analysis.json'
  }).as('postAnalysis');
  
  // Mock user settings
  cy.intercept('GET', '/api/settings', {
    fixture: 'settings.json'
  }).as('getSettings');
  
  // Mock reports
  cy.intercept('GET', '/api/reports', {
    fixture: 'reports.json'
  }).as('getReports');
  
  // Mock anomalies
  cy.intercept('GET', '/api/anomalies', {
    fixture: 'anomalies.json'
  }).as('getAnomalies');
});

// Navigation commands
Cypress.Commands.add('navigateToPage', (page) => {
  const routes = {
    'home': '/',
    'dashboard': '/dashboard',
    'message-analyzer': '/message-analyzer',
    'relationships': '/relationships',
    'reports': '/reports',
    'anomaly-detection': '/anomaly-detection',
    'growth-center': '/growth-center'
  };
  
  if (routes[page]) {
    cy.visit(routes[page]);
  } else {
    throw new Error(`Unknown page: ${page}`);
  }
});

// Form filling commands
Cypress.Commands.add('fillMessageAnalyzer', (messageData) => {
  cy.get('[data-testid="message-input"]').clear().type(messageData.message);
  
  if (messageData.relationship) {
    cy.get('[data-testid="relationship-select"]').select(messageData.relationship);
  }
  
  if (messageData.context) {
    cy.get('[data-testid="context-input"]').clear().type(messageData.context);
  }
});

Cypress.Commands.add('fillNewRelationship', (relationshipData) => {
  cy.get('[data-testid="relationship-name"]').clear().type(relationshipData.name);
  cy.get('[data-testid="relationship-type"]').select(relationshipData.type);
  
  if (relationshipData.description) {
    cy.get('[data-testid="relationship-description"]').clear().type(relationshipData.description);
  }
});

// Wait for loading states
Cypress.Commands.add('waitForPageLoad', () => {
  cy.get('[data-testid="loading-spinner"]').should('not.exist');
  cy.get('[data-testid="page-content"]').should('be.visible');
});

Cypress.Commands.add('waitForChartLoad', () => {
  cy.get('[data-testid="chart-loading"]').should('not.exist');
  cy.get('[data-testid="chart-container"]').should('be.visible');
});

// Accessibility testing commands
Cypress.Commands.add('checkA11y', (selector = null, options = {}) => {
  const defaultOptions = {
    includedImpacts: ['minor', 'moderate', 'serious', 'critical'],
    rules: {
      'color-contrast': { enabled: true },
      'focus-order-semantics': { enabled: true },
      'keyboard': { enabled: true }
    }
  };
  
  cy.injectAxe();
  cy.checkA11y(selector, { ...defaultOptions, ...options });
});

// Visual regression commands
Cypress.Commands.add('compareSnapshot', (name, options = {}) => {
  const defaultOptions = {
    threshold: 0.1,
    thresholdType: 'percent'
  };
  
  cy.matchImageSnapshot(name, { ...defaultOptions, ...options });
});

// Data manipulation commands
Cypress.Commands.add('createTestRelationship', (relationshipData = {}) => {
  const defaultData = {
    name: 'Test Relationship',
    type: 'friend',
    description: 'A test relationship for automated testing'
  };
  
  cy.navigateToPage('relationships');
  cy.get('[data-testid="add-relationship-button"]').click();
  cy.fillNewRelationship({ ...defaultData, ...relationshipData });
  cy.get('[data-testid="save-relationship-button"]').click();
  cy.get('[data-testid="success-message"]').should('be.visible');
});

Cypress.Commands.add('analyzeTestMessage', (messageData = {}) => {
  const defaultData = {
    message: 'This is a test message for analysis',
    relationship: 'Test Relationship'
  };
  
  cy.navigateToPage('message-analyzer');
  cy.fillMessageAnalyzer({ ...defaultData, ...messageData });
  cy.get('[data-testid="analyze-button"]').click();
  cy.wait('@postAnalysis');
  cy.get('[data-testid="analysis-results"]').should('be.visible');
});

// Settings and preferences commands
Cypress.Commands.add('enableFaithMode', () => {
  cy.get('[data-testid="faith-mode-toggle"]').click();
  cy.get('[data-testid="faith-mode-toggle"]').should('have.attr', 'aria-checked', 'true');
});

Cypress.Commands.add('setFontPreference', (fontType) => {
  cy.get('[data-testid="font-toggle"]').click();
  cy.get(`[data-testid="font-option-${fontType}"]`).click();
  cy.get('body').should('have.class', `font-${fontType}`);
});

Cypress.Commands.add('disableMotion', () => {
  cy.get('[data-testid="motion-toggle"]').click();
  cy.get('[data-testid="motion-toggle"]').should('have.attr', 'aria-checked', 'false');
});

// Error handling commands
Cypress.Commands.add('simulateNetworkError', (endpoint) => {
  cy.intercept(endpoint, { forceNetworkError: true }).as('networkError');
});

Cypress.Commands.add('simulateSlowNetwork', (endpoint, delay = 5000) => {
  cy.intercept(endpoint, (req) => {
    req.reply((res) => {
      res.delay(delay);
    });
  }).as('slowNetwork');
});

// Mobile-specific commands
Cypress.Commands.add('swipeLeft', { prevSubject: 'element' }, (subject) => {
  cy.wrap(subject)
    .trigger('touchstart', { touches: [{ clientX: 300, clientY: 150 }] })
    .trigger('touchmove', { touches: [{ clientX: 100, clientY: 150 }] })
    .trigger('touchend');
});

Cypress.Commands.add('swipeRight', { prevSubject: 'element' }, (subject) => {
  cy.wrap(subject)
    .trigger('touchstart', { touches: [{ clientX: 100, clientY: 150 }] })
    .trigger('touchmove', { touches: [{ clientX: 300, clientY: 150 }] })
    .trigger('touchend');
});

// Performance testing commands
Cypress.Commands.add('measurePageLoadTime', () => {
  cy.window().then((win) => {
    const observer = new win.PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.entryType === 'navigation') {
          cy.task('log', `Page load time: ${entry.loadEventEnd - entry.loadEventStart}ms`);
        }
      });
    });
    observer.observe({ entryTypes: ['navigation'] });
  });
});

// Custom assertions
Cypress.Commands.add('shouldHaveValidSentiment', { prevSubject: 'element' }, (subject) => {
  cy.wrap(subject).invoke('text').then((text) => {
    const sentiment = parseFloat(text);
    expect(sentiment).to.be.at.least(-1);
    expect(sentiment).to.be.at.most(1);
  });
});

Cypress.Commands.add('shouldBeWithinRange', { prevSubject: 'element' }, (subject, min, max) => {
  cy.wrap(subject).invoke('text').then((text) => {
    const value = parseFloat(text);
    expect(value).to.be.at.least(min);
    expect(value).to.be.at.most(max);
  });
});

// Database seeding commands (for integration testing)
Cypress.Commands.add('seedDatabase', (seedData = 'default') => {
  cy.task('db:seed', seedData);
});

Cypress.Commands.add('clearDatabase', () => {
  cy.task('db:clear');
});

// Cookie and storage management
Cypress.Commands.add('preserveSession', () => {
  Cypress.Cookies.preserveOnce('auth-token', 'session-id');
});

Cypress.Commands.add('clearAllStorage', () => {
  cy.clearCookies();
  cy.clearLocalStorage();
  cy.window().then((win) => {
    win.sessionStorage.clear();
  });
});