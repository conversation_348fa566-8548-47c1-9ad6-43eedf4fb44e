describe('Complete User Journey', () => {
  beforeEach(() => {
    cy.visit('/');
    cy.injectAxe();
  });

  describe('New User Onboarding', () => {
    it('completes the full onboarding flow', () => {
      // Home page interaction
      cy.get('[data-testid="hero-section"]').should('be.visible');
      cy.get('[data-testid="get-started-button"]').click();
      
      // Privacy acceptance
      cy.get('[data-testid="privacy-banner"]').should('be.visible');
      cy.get('[data-testid="accept-privacy"]').click();
      cy.get('[data-testid="privacy-banner"]').should('not.exist');
      
      // Navigate to message analyzer (unauthenticated flow)
      cy.url().should('include', '/message-analyzer');
      cy.get('[data-testid="auth-prompt"]').should('be.visible');
      
      // Login process
      cy.get('[data-testid="login-button"]').click();
      // Note: In real tests, we'd handle Auth0 flow here
      
      // Mock successful authentication
      cy.window().then((win) => {
        win.localStorage.setItem('isAuthenticated', 'true');
        win.localStorage.setItem('user', JSON.stringify({
          name: 'Test User',
          email: '<EMAIL>'
        }));
      });
      
      cy.reload();
      cy.waitForPageLoad();
    });

    it('handles authentication errors gracefully', () => {
      cy.get('[data-testid="login-button"]').click();
      
      // Simulate auth error
      cy.window().then((win) => {
        win.postMessage({ type: 'AUTH_ERROR', error: 'Authentication failed' }, '*');
      });
      
      cy.get('[data-testid="auth-error"]').should('be.visible');
      cy.get('[data-testid="retry-auth"]').should('be.visible');
    });
  });

  describe('Message Analysis Workflow', () => {
    beforeEach(() => {
      // Mock authenticated state
      cy.window().then((win) => {
        win.localStorage.setItem('isAuthenticated', 'true');
      });
      cy.navigateToPage('message-analyzer');
      cy.waitForPageLoad();
    });

    it('analyzes a message successfully', () => {
      const messageData = {
        message: 'Hey, I really enjoyed our conversation yesterday! Looking forward to hanging out again soon.',
        relationship: 'Sarah'
      };
      
      // Fill out the form
      cy.fillMessageAnalyzer(messageData);
      
      // Submit analysis
      cy.get('[data-testid="analyze-button"]').click();
      cy.get('[data-testid="analyzing-spinner"]').should('be.visible');
      
      // Wait for results
      cy.wait('@postAnalysis');
      cy.get('[data-testid="analysis-results"]').should('be.visible');
      
      // Verify sentiment score
      cy.get('[data-testid="sentiment-score"]').shouldHaveValidSentiment();
      
      // Verify emotional indicators
      cy.get('[data-testid="emotional-indicators"]').should('contain', 'positive');
      
      // Verify AI insights
      cy.get('[data-testid="ai-insights"]').should('be.visible');
      cy.get('[data-testid="ai-insights"]').should('not.be.empty');
      
      // Check suggested responses
      cy.get('[data-testid="suggested-responses"]').should('be.visible');
      cy.get('[data-testid="response-suggestion"]').should('have.length.at.least', 1);
    });

    it('handles various message types correctly', () => {
      const testCases = [
        {
          message: 'I am feeling really down today...',
          expectedSentiment: 'negative',
          expectedFlags: ['emotional_distress']
        },
        {
          message: 'Congratulations on your promotion! So happy for you!',
          expectedSentiment: 'positive',
          expectedFlags: []
        },
        {
          message: 'We need to talk about what happened.',
          expectedSentiment: 'neutral',
          expectedFlags: ['communication_gap']
        }
      ];
      
      testCases.forEach((testCase, index) => {
        cy.get('[data-testid="message-input"]').clear().type(testCase.message);
        cy.get('[data-testid="analyze-button"]').click();
        
        cy.wait('@postAnalysis');
        
        // Verify sentiment classification
        cy.get('[data-testid="sentiment-classification"]')
          .should('contain', testCase.expectedSentiment);
        
        // Verify flags if expected
        if (testCase.expectedFlags.length > 0) {
          testCase.expectedFlags.forEach(flag => {
            cy.get('[data-testid="communication-flags"]')
              .should('contain', flag);
          });
        }
        
        // Clear for next test
        cy.get('[data-testid="clear-analysis"]').click();
      });
    });

    it('validates form inputs', () => {
      // Try to submit empty form
      cy.get('[data-testid="analyze-button"]').click();
      cy.get('[data-testid="message-error"]').should('contain', 'Message is required');
      
      // Try with very short message
      cy.get('[data-testid="message-input"]').type('Hi');
      cy.get('[data-testid="analyze-button"]').click();
      cy.get('[data-testid="message-error"]').should('contain', 'Message too short');
      
      // Try with very long message
      const longMessage = 'a'.repeat(5001);
      cy.get('[data-testid="message-input"]').clear().type(longMessage);
      cy.get('[data-testid="message-error"]').should('contain', 'Message too long');
    });
  });

  describe('Relationship Management', () => {
    beforeEach(() => {
      cy.window().then((win) => {
        win.localStorage.setItem('isAuthenticated', 'true');
      });
      cy.navigateToPage('relationships');
      cy.waitForPageLoad();
    });

    it('creates, updates, and deletes a relationship', () => {
      // Create new relationship
      cy.get('[data-testid="add-relationship-button"]').click();
      
      const relationshipData = {
        name: 'Jane Doe',
        type: 'friend',
        description: 'College friend who moved to the city'
      };
      
      cy.fillNewRelationship(relationshipData);
      cy.get('[data-testid="save-relationship-button"]').click();
      
      cy.get('[data-testid="success-message"]').should('be.visible');
      cy.get('[data-testid="relationship-list"]').should('contain', 'Jane Doe');
      
      // Update relationship
      cy.get('[data-testid="relationship-Jane Doe"]').within(() => {
        cy.get('[data-testid="edit-button"]').click();
      });
      
      cy.get('[data-testid="relationship-description"]')
        .clear()
        .type('Best friend from college');
      cy.get('[data-testid="save-relationship-button"]').click();
      
      cy.get('[data-testid="success-message"]').should('be.visible');
      
      // View relationship details
      cy.get('[data-testid="relationship-Jane Doe"]').click();
      cy.get('[data-testid="relationship-details"]').should('be.visible');
      cy.get('[data-testid="relationship-history"]').should('be.visible');
      
      // Delete relationship
      cy.get('[data-testid="delete-relationship-button"]').click();
      cy.get('[data-testid="confirm-delete"]').click();
      
      cy.get('[data-testid="success-message"]').should('contain', 'deleted');
      cy.get('[data-testid="relationship-list"]').should('not.contain', 'Jane Doe');
    });

    it('displays relationship analytics', () => {
      // Select an existing relationship
      cy.get('[data-testid="relationship-Sarah"]').click();
      
      // Verify analytics are displayed
      cy.get('[data-testid="health-score"]').should('be.visible');
      cy.get('[data-testid="health-score"]').shouldBeWithinRange(0, 100);
      
      cy.get('[data-testid="sentiment-trend"]').should('be.visible');
      cy.get('[data-testid="communication-pattern"]').should('be.visible');
      cy.get('[data-testid="interaction-frequency"]').should('be.visible');
      
      // Check for insights
      cy.get('[data-testid="relationship-insights"]').should('be.visible');
      cy.get('[data-testid="improvement-suggestions"]').should('be.visible');
    });
  });

  describe('Dashboard Overview', () => {
    beforeEach(() => {
      cy.window().then((win) => {
        win.localStorage.setItem('isAuthenticated', 'true');
      });
      cy.navigateToPage('dashboard');
      cy.waitForPageLoad();
    });

    it('displays comprehensive dashboard data', () => {
      cy.wait('@getDashboard');
      
      // Verify key metrics
      cy.get('[data-testid="total-relationships"]').should('contain', '5');
      cy.get('[data-testid="total-analyses"]').should('contain', '23');
      
      // Verify charts are loaded
      cy.waitForChartLoad();
      cy.get('[data-testid="sentiment-timeline-chart"]').should('be.visible');
      cy.get('[data-testid="flag-counts-chart"]').should('be.visible');
      cy.get('[data-testid="relationship-health-chart"]').should('be.visible');
      
      // Verify recent activity
      cy.get('[data-testid="recent-activity"]').should('be.visible');
      cy.get('[data-testid="activity-item"]').should('have.length.at.least', 1);
      
      // Test interactive elements
      cy.get('[data-testid="filter-dropdown"]').select('last-week');
      cy.get('[data-testid="dashboard-content"]').should('contain', 'Last Week');
      
      // Test quick actions
      cy.get('[data-testid="quick-analyze"]').click();
      cy.url().should('include', '/message-analyzer');
    });

    it('handles real-time updates', () => {
      // Initial load
      cy.wait('@getDashboard');
      cy.get('[data-testid="total-analyses"]').should('contain', '23');
      
      // Simulate new analysis
      cy.intercept('GET', '/api/dashboard', {
        fixture: 'dashboard-updated.json'
      }).as('getDashboardUpdated');
      
      // Trigger refresh (could be automatic in real app)
      cy.get('[data-testid="refresh-button"]').click();
      cy.wait('@getDashboardUpdated');
      
      cy.get('[data-testid="total-analyses"]').should('contain', '24');
      cy.get('[data-testid="update-notification"]').should('be.visible');
    });
  });

  describe('Reports and Analytics', () => {
    beforeEach(() => {
      cy.window().then((win) => {
        win.localStorage.setItem('isAuthenticated', 'true');
      });
      cy.navigateToPage('reports');
      cy.waitForPageLoad();
    });

    it('generates and exports comprehensive reports', () => {
      cy.wait('@getReports');
      
      // Select date range
      cy.get('[data-testid="date-from"]').type('2024-01-01');
      cy.get('[data-testid="date-to"]').type('2024-01-31');
      cy.get('[data-testid="generate-report"]').click();
      
      // Verify report sections
      cy.get('[data-testid="executive-summary"]').should('be.visible');
      cy.get('[data-testid="sentiment-analysis"]').should('be.visible');
      cy.get('[data-testid="relationship-insights"]').should('be.visible');
      cy.get('[data-testid="communication-patterns"]').should('be.visible');
      
      // Test export functionality
      cy.get('[data-testid="export-pdf"]').click();
      cy.get('[data-testid="export-status"]').should('contain', 'Generating');
      
      // Wait for export completion
      cy.get('[data-testid="download-link"]', { timeout: 10000 }).should('be.visible');
    });
  });

  describe('Settings and Preferences', () => {
    beforeEach(() => {
      cy.window().then((win) => {
        win.localStorage.setItem('isAuthenticated', 'true');
      });
    });

    it('manages user preferences', () => {
      // Test faith mode toggle
      cy.enableFaithMode();
      cy.navigateToPage('dashboard');
      cy.get('[data-testid="faith-content"]').should('be.visible');
      
      // Test font preferences
      cy.setFontPreference('dyslexic');
      cy.get('body').should('have.class', 'font-dyslexic');
      
      // Test motion preferences
      cy.disableMotion();
      cy.get('[data-testid="animated-element"]').should('have.class', 'motion-reduce');
      
      // Verify preferences persist
      cy.reload();
      cy.get('body').should('have.class', 'font-dyslexic');
      cy.get('[data-testid="motion-toggle"]').should('have.attr', 'aria-checked', 'false');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    beforeEach(() => {
      cy.window().then((win) => {
        win.localStorage.setItem('isAuthenticated', 'true');
      });
    });

    it('handles network errors gracefully', () => {
      cy.simulateNetworkError('/api/dashboard');
      cy.navigateToPage('dashboard');
      
      cy.get('[data-testid="error-message"]').should('be.visible');
      cy.get('[data-testid="retry-button"]').should('be.visible');
      
      // Test retry functionality
      cy.setupApiMocks(); // Restore normal mocks
      cy.get('[data-testid="retry-button"]').click();
      
      cy.wait('@getDashboard');
      cy.get('[data-testid="dashboard-content"]').should('be.visible');
    });

    it('handles slow network conditions', () => {
      cy.simulateSlowNetwork('/api/dashboard', 3000);
      cy.navigateToPage('dashboard');
      
      cy.get('[data-testid="loading-spinner"]').should('be.visible');
      cy.get('[data-testid="slow-connection-notice"]', { timeout: 5000 }).should('be.visible');
      
      cy.wait('@slowNetwork');
      cy.get('[data-testid="dashboard-content"]').should('be.visible');
    });

    it('handles authentication expiration', () => {
      // Simulate expired token
      cy.intercept('/api/**', {
        statusCode: 401,
        body: { error: 'Unauthorized' }
      });
      
      cy.navigateToPage('dashboard');
      
      // Should redirect to login
      cy.get('[data-testid="session-expired"]').should('be.visible');
      cy.get('[data-testid="login-again"]').click();
      
      cy.url().should('include', '/login');
    });
  });

  describe('Performance and Accessibility', () => {
    it('meets performance benchmarks', () => {
      cy.visit('/');
      
      // Measure page load performance
      cy.measurePageLoadTime();
      cy.measureWebVitals().then((vitals) => {
        expect(vitals.lcp).to.be.lessThan(2500); // Largest Contentful Paint
        expect(vitals.fid).to.be.lessThan(100);  // First Input Delay
        expect(vitals.cls).to.be.lessThan(0.1);  // Cumulative Layout Shift
      });
      
      // Test performance on different pages
      const pages = ['dashboard', 'message-analyzer', 'relationships'];
      pages.forEach(page => {
        cy.measurePerformance(() => {
          cy.navigateToPage(page);
          cy.waitForPageLoad();
        }).should('be.lessThan', 1000);
      });
    });

    it('meets accessibility standards', () => {
      const pages = ['/', '/dashboard', '/message-analyzer', '/relationships'];
      
      pages.forEach(page => {
        cy.visit(page);
        cy.waitForPageLoad();
        
        // Check for accessibility violations
        cy.checkA11y(null, {
          includedImpacts: ['critical', 'serious']
        });
        
        // Test keyboard navigation
        cy.get('body').tab();
        cy.focused().should('have.attr', 'tabindex').or('be.focusable');
      });
    });
  });

  describe('Cross-Browser and Device Testing', () => {
    it('works correctly on mobile devices', () => {
      cy.setMobileViewport();
      cy.visit('/');
      
      // Test mobile navigation
      cy.get('[data-testid="mobile-menu-toggle"]').should('be.visible');
      cy.get('[data-testid="mobile-menu-toggle"]').click();
      cy.get('[data-testid="mobile-menu"]').should('be.visible');
      
      // Test touch interactions
      cy.navigateToPage('dashboard');
      cy.waitForPageLoad();
      
      cy.get('[data-testid="chart-container"]').swipeLeft();
      cy.get('[data-testid="next-chart"]').should('be.visible');
    });

    it('adapts to different screen sizes', () => {
      const viewports = [
        { width: 375, height: 667, device: 'mobile' },
        { width: 768, height: 1024, device: 'tablet' },
        { width: 1280, height: 720, device: 'desktop' }
      ];
      
      viewports.forEach(viewport => {
        cy.viewport(viewport.width, viewport.height);
        cy.visit('/dashboard');
        cy.waitForPageLoad();
        
        // Verify responsive layout
        cy.get('[data-testid="dashboard-grid"]')
          .should('have.class', `${viewport.device}-layout`);
      });
    });
  });
});