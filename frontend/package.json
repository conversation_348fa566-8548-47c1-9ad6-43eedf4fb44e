{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@auth0/auth0-react": "^2.3.0", "@playwright/test": "^1.52.0", "axios": "^1.8.4", "cra-template": "1.2.0", "framer-motion": "^12.23.0", "http-proxy-middleware": "^3.0.5", "lucide-react": "^0.525.0", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "recharts": "^2.15.3"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:watch": "react-scripts test --watch<PERSON>ll", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:ci": "react-scripts test --coverage --watchAll=false --testResultsProcessor=jest-sonar-reporter", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:playwright": "playwright test", "test:accessibility": "jest --testNamePattern='accessibility'", "test:visual": "jest --testNamePattern='visual regression'", "test:performance": "jest --testNamePattern='performance'", "test:all": "npm run test:coverage && npm run test:e2e && npm run test:playwright", "analyze": "npm run build && npx serve -s build", "bundle:analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@eslint/js": "9.23.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "autoprefixer": "^10.4.20", "axe-core": "^4.8.4", "cypress": "^13.6.6", "eslint": "9.23.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-react": "7.37.4", "globals": "15.15.0", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.2.1", "postcss": "^8.4.49", "puppeteer": "^22.0.0", "tailwindcss": "^3.4.17", "web-vitals": "^3.5.2"}}