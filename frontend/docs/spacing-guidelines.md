# 8-pt Spacing System Guidelines

## Overview
This document defines the 8-pt spacing system for the AEI Alpha project. All spacing values are based on multiples of 8 pixels, ensuring visual consistency and rhythm throughout the application.

## Core Spacing Scale

Our spacing system uses an 8-pixel base unit with the following scale:

| Token | Value | Pixels | Usage |
|-------|-------|--------|-------|
| `space-0` | 0 | 0px | No spacing |
| `space-1` | 0.25rem | 4px | Micro spacing (0.5x base) |
| `space-2` | 0.5rem | 8px | **Base unit** - tight spacing |
| `space-3` | 0.75rem | 12px | Small spacing (1.5x base) |
| `space-4` | 1rem | 16px | Medium spacing (2x base) |
| `space-5` | 1.25rem | 20px | Medium-large spacing (2.5x base) |
| `space-6` | 1.5rem | 24px | Large spacing (3x base) |
| `space-7` | 1.75rem | 28px | Extra-large spacing (3.5x base) |
| `space-8` | 2rem | 32px | XXL spacing (4x base) |
| `space-9` | 2.25rem | 36px | XXXL spacing (4.5x base) |
| `space-10` | 2.5rem | 40px | Jumbo spacing (5x base) |
| `space-12` | 3rem | 48px | Super spacing (6x base) |
| `space-14` | 3.5rem | 56px | Mega spacing (7x base) |
| `space-16` | 4rem | 64px | Ultra spacing (8x base) |
| `space-20` | 5rem | 80px | Hero spacing (10x base) |
| `space-24` | 6rem | 96px | Giant spacing (12x base) |
| `space-32` | 8rem | 128px | Colossal spacing (16x base) |

## Component-Specific Spacing

### Container Widths
- **Mobile**: `100%` with `px-4` (16px) padding
- **Tablet**: `max-w-3xl` (768px) with `px-6` (24px) padding
- **Desktop**: `max-w-7xl` (1280px) with `px-8` (32px) padding
- **Wide**: `max-w-screen-2xl` (1536px) with `px-8` (32px) padding

### Section Padding
- **Mobile**: `py-8` (32px) vertical padding
- **Tablet**: `py-12` (48px) vertical padding
- **Desktop**: `py-16` (64px) vertical padding
- **Hero sections**: `py-20` (80px) or `py-24` (96px)

### Card Components
- **Small cards**: `p-4` (16px) padding
- **Medium cards**: `p-6` (24px) padding
- **Large cards**: `p-8` (32px) padding
- **Card gap in grids**: `gap-6` (24px) standard, `gap-8` (32px) for emphasis

### Grid & Flex Layouts
- **Tight grid**: `gap-2` (8px)
- **Default grid**: `gap-4` (16px) mobile, `gap-6` (24px) desktop
- **Loose grid**: `gap-8` (32px)
- **Section spacing**: `space-y-12` (48px) between major sections

### Form Elements
- **Input padding**: `px-4 py-2` (16px horizontal, 8px vertical)
- **Label margin**: `mb-2` (8px)
- **Form group spacing**: `space-y-4` (16px)
- **Form section spacing**: `space-y-6` (24px)

### Typography Spacing
- **Paragraph margin**: `mb-4` (16px)
- **Heading margins**: 
  - H1: `mt-8 mb-6` (32px top, 24px bottom)
  - H2: `mt-6 mb-4` (24px top, 16px bottom)
  - H3: `mt-5 mb-3` (20px top, 12px bottom)
  - H4-H6: `mt-4 mb-2` (16px top, 8px bottom)
- **List item spacing**: `mb-2` (8px)

## Tailwind Utility Classes

### Spacing Utilities
```css
/* Padding */
p-0  → padding: 0
p-1  → padding: 4px
p-2  → padding: 8px
p-4  → padding: 16px
p-6  → padding: 24px
p-8  → padding: 32px
p-12 → padding: 48px
p-16 → padding: 64px

/* Margin */
m-0  → margin: 0
m-2  → margin: 8px
m-4  → margin: 16px
m-6  → margin: 24px
m-8  → margin: 32px

/* Gap (for Grid/Flex) */
gap-0  → gap: 0
gap-2  → gap: 8px
gap-4  → gap: 16px
gap-6  → gap: 24px
gap-8  → gap: 32px
gap-12 → gap: 48px

/* Space Between (Flex/Grid children) */
space-x-2 → horizontal spacing: 8px
space-x-4 → horizontal spacing: 16px
space-x-6 → horizontal spacing: 24px
space-x-8 → horizontal spacing: 32px

space-y-2 → vertical spacing: 8px
space-y-4 → vertical spacing: 16px
space-y-6 → vertical spacing: 24px
space-y-8 → vertical spacing: 32px
space-y-12 → vertical spacing: 48px
```

### Responsive Spacing
```css
/* Mobile-first approach */
p-4 sm:p-6 md:p-8     → 16px → 24px → 32px
gap-4 sm:gap-6 lg:gap-8 → 16px → 24px → 32px
py-8 sm:py-12 md:py-16  → 32px → 48px → 64px
```

## Layout Helpers

### CSS Grid Templates
```jsx
// 12-column grid with consistent gaps
<div className="grid grid-cols-12 gap-6">
  <div className="col-span-12 md:col-span-8">Main content</div>
  <div className="col-span-12 md:col-span-4">Sidebar</div>
</div>

// Card grid with responsive columns
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
  {/* Cards */}
</div>

// Auto-fit grid
<div className="grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-6">
  {/* Auto-sizing cards */}
</div>
```

### Flexbox Helpers
```jsx
// Centered flex container
<div className="flex items-center justify-center gap-4">
  {/* Centered items */}
</div>

// Space-between layout
<div className="flex justify-between items-start gap-4">
  {/* Header with actions */}
</div>

// Vertical stack
<div className="flex flex-col space-y-6">
  {/* Stacked items */}
</div>
```

## Implementation Checklist

### Phase 1: Audit Current Spacing
- [x] Document current spacing inconsistencies
- [x] Identify components needing updates
- [x] Create spacing guidelines

### Phase 2: Update Core Components
- [ ] Update Layout component with 8pt spacing
- [ ] Update Card component padding/margins
- [ ] Update Container component widths
- [ ] Update Section component padding

### Phase 3: Update Page Layouts
- [ ] Dashboard page spacing
- [ ] Message Analyzer page spacing
- [ ] Relationship Hub page spacing
- [ ] Growth Center page spacing

### Phase 4: Form & UI Elements
- [ ] Form input spacing
- [ ] Button padding/margins
- [ ] Modal spacing
- [ ] Navigation spacing

## Best Practices

1. **Always use the spacing scale** - Avoid arbitrary values
2. **Mobile-first responsive spacing** - Start small, increase for larger screens
3. **Consistent directional spacing** - Use the same spacing on all sides when possible
4. **Group related content** - Use smaller spacing within groups, larger between groups
5. **Vertical rhythm** - Maintain consistent vertical spacing throughout pages
6. **Whitespace is important** - Don't be afraid to use larger spacing for emphasis

## Common Patterns

### Page Layout
```jsx
<div className="min-h-screen bg-background-primary">
  <nav className="px-4 sm:px-6 lg:px-8 py-4">
    {/* Navigation */}
  </nav>
  
  <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
    <section className="space-y-12">
      {/* Page sections */}
    </section>
  </main>
  
  <footer className="mt-16 py-8 border-t">
    {/* Footer */}
  </footer>
</div>
```

### Card Grid
```jsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <Card className="p-6">
    {/* Card content */}
  </Card>
</div>
```

### Form Layout
```jsx
<form className="space-y-6 max-w-2xl">
  <div className="space-y-4">
    <label className="block mb-2">Label</label>
    <input className="w-full px-4 py-2" />
  </div>
  
  <div className="flex gap-4 mt-8">
    <Button>Submit</Button>
    <Button variant="secondary">Cancel</Button>
  </div>
</form>
```
