# Icon System Documentation

## Overview

The Icon system in My ÆI application uses **Lucide React** icons wrapped in a custom component that provides:
- Consistent sizing across the application
- Theme-aware color variants
- Built-in animations
- Accessibility features (decorative vs informative icons)
- TypeScript support

## Installation

The icon library is already installed as part of the project dependencies:

```bash
npm install lucide-react
```

## Usage

### Basic Usage

```jsx
import Icon from '../components/modern/Icon';

// Basic icon
<Icon name="Home" size="md" />

// With custom color
<Icon name="Heart" size="lg" className="text-red-500" />

// With variant
<Icon name="AlertCircle" size="lg" variant="danger" />
```

### Available Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | string | required | Name of the Lucide icon (e.g., 'Home', 'User', 'Settings') |
| `size` | string\|number | 'md' | Predefined size ('xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl', '4xl') or custom number |
| `className` | string | '' | Additional CSS classes |
| `color` | string | - | Icon color (overrides variant and className) |
| `strokeWidth` | number | - | Stroke width for the icon |
| `decorative` | boolean | true | Whether the icon is purely decorative or informative |
| `label` | string | - | Accessibility label for informative icons |
| `variant` | string | - | Predefined color variant |
| `animate` | string | - | Animation preset |

### Size System

The icon component supports predefined sizes that map to specific pixel values:

- `xs`: 12px
- `sm`: 16px
- `md`: 20px (default)
- `lg`: 24px
- `xl`: 32px
- `2xl`: 40px
- `3xl`: 48px
- `4xl`: 64px

You can also pass a custom number for pixel-perfect sizing:

```jsx
<Icon name="Star" size={18} />
```

### Color Variants

Use variants for quick theming that aligns with the design system:

```jsx
<Icon name="AlertCircle" variant="danger" />   // Red color
<Icon name="CheckCircle" variant="success" />  // Green color
<Icon name="Info" variant="info" />           // Blue color
<Icon name="HelpCircle" variant="warning" />  // Yellow color
```

Available variants:
- `primary`: Primary brand color
- `secondary`: Secondary lavender color
- `success`: Positive/success green
- `warning`: Warning yellow
- `danger`: Error/negative red
- `info`: Information blue
- `muted`: Muted gray
- `white`: White color

### Animations

Add smooth animations to icons:

```jsx
<Icon name="RefreshCw" animate="spin" />     // Rotating spinner
<Icon name="Heart" animate="pulse" />         // Pulsing effect
<Icon name="Bell" animate="bounce" />         // Bouncing animation
<Icon name="Circle" animate="ping" />         // Ping/ripple effect
```

### Accessibility

Icons are decorative by default (aria-hidden="true"). For informative icons, set `decorative={false}` and provide a label:

```jsx
// Decorative icon (default)
<Icon name="Star" />

// Informative icon with screen reader label
<Icon 
  name="AlertTriangle" 
  decorative={false} 
  label="Warning: This action cannot be undone" 
/>
```

### Using with Buttons

The Button component has built-in support for icons:

```jsx
<Button icon={<Icon name="Plus" size="sm" />}>
  Add Item
</Button>
```

## Common Icon Names

### Navigation
- `Home`, `ArrowLeft`, `ArrowRight`, `ChevronLeft`, `ChevronRight`, `Menu`, `X`

### Dashboard & Analytics
- `BarChart3`, `LineChart`, `PieChart`, `TrendingUp`, `TrendingDown`, `Activity`

### Communication
- `MessageSquare`, `MessageCircle`, `Mail`, `Send`, `Bell`, `BellOff`

### Emotions & Relationships
- `Heart`, `HeartHandshake`, `Users`, `UserPlus`, `Smile`, `Frown`, `Meh`

### Alerts & Status
- `AlertCircle`, `AlertTriangle`, `CheckCircle`, `XCircle`, `Info`, `HelpCircle`

### Actions
- `Plus`, `Minus`, `Edit3`, `Trash2`, `Save`, `Download`, `Upload`, `RefreshCw`, `Settings`, `Search`, `Filter`, `Copy`

### Growth & Progress
- `Target`, `Award`, `Star`, `Zap`, `Sparkles`, `Brain`, `BookOpen`, `Lightbulb`

## Migration Guide

To replace existing SVG icons with the Icon component:

1. Import the Icon component:
```jsx
import Icon from '../components/modern/Icon';
```

2. Replace SVG elements:
```jsx
// Before
<svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.081 16.5c-.77.833.192 2.5 1.732 2.5z" />
</svg>

// After
<Icon name="AlertTriangle" size="md" className="text-red-500" />
```

## Best Practices

1. **Use semantic icon names**: Choose icons that clearly represent their function
2. **Maintain consistent sizing**: Use the predefined size system for consistency
3. **Consider accessibility**: Mark informative icons appropriately
4. **Use variants for theming**: Leverage color variants instead of custom colors when possible
5. **Animate sparingly**: Use animations for loading states or to draw attention

## Extending the Icon System

To add new icons:
1. Check if the icon exists in [Lucide's icon library](https://lucide.dev/icons)
2. Use the exact name as shown in Lucide's documentation
3. If a custom icon is needed, consider contributing it to Lucide or creating a custom SVG component

## Performance Considerations

- Icons are loaded on-demand from the Lucide library
- The Icon component is lightweight and doesn't add significant overhead
- For optimal performance, avoid animating many icons simultaneously
