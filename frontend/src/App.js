import React, { useState, useEffect, Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import './App.css';

// Context Providers
import { AppProvider } from './contexts/AppContext';
import { FaithProvider, useFaith } from './contexts/FaithContext';
import { FontProvider, useFontPreference } from './contexts/FontContext';
import { MotionProvider, useMotion } from './contexts/MotionContext';

// Auth0
import Auth0ProviderWithHistory from './auth/Auth0ProviderWithHistory';
import MockAuth0Provider from './auth/MockAuth0Provider';
import { UserProfile } from './auth/AuthButtons';
import DevelopmentAuthButtons from './auth/DevelopmentAuthButtons';

// Components
import FontToggle from './components/FontToggle';
import MotionToggle from './components/MotionToggle';
import ThemeToggle from './components/ThemeToggle';
import PrivacyBanner from './components/PrivacyBanner';
import { ThemeProvider } from './contexts/ThemeContext';

// Lazy-loaded pages for better performance and code splitting
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const MessageAnalyzer = React.lazy(() => import('./pages/MessageAnalyzer'));
const RelationshipHub = React.lazy(() => import('./pages/RelationshipHub'));
const GrowthCenter = React.lazy(() => import('./pages/GrowthCenter'));
const Reports = React.lazy(() => import('./pages/Reports'));
const AnomalyDetection = React.lazy(() => import('./pages/AnomalyDetection'));
const Home = React.lazy(() => import('./pages/Home'));
const TestPage = React.lazy(() => import('./TestPage'));
const ColorTestPage = React.lazy(() => import('./pages/ColorTestPage'));
const ComponentShowcase = React.lazy(() => import('./pages/ComponentShowcase'));
const DashboardShowcase = React.lazy(() => import('./pages/DashboardShowcase'));
const MobileTestPage = React.lazy(() => import('./pages/MobileTestPage'));
const ContrastTestPage = React.lazy(() => import('./pages/ContrastTestPage'));
const TypographyShowcase = React.lazy(() => import('./pages/TypographyShowcase'));

// Loading component for Suspense fallback
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-screen bg-gray-50">
    <div className="flex flex-col items-center space-y-4">
      <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
      <p className="text-gray-600 font-medium">Loading...</p>
    </div>
  </div>
);

// Components
const Header = () => {
  const location = useLocation();
  const { faithModeEnabled, toggleFaithMode } = useFaith();
  const { fontPreference } = useFontPreference();
  const { motionPreference } = useMotion();

  return (
    <header className="bg-gradient-to-r from-primary to-secondary-lavender text-inverse py-6 shadow-lg">
      {/* Skip Navigation Link for Accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:surface-primary focus:text-primary focus:rounded-md focus:shadow-lg focus:outline-none focus:ring-2 focus:border-focus focus:ring-offset-2 transition-all duration-200"
        tabIndex="0"
      >
        Skip to main content
      </a>
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center">
          <Link to="/" className="flex items-center space-x-2">
            <span className="text-3xl font-extrabold">My ÆI</span>
            <span className="text-sm bg-purple-900 px-2 py-1 rounded-md opacity-75">Alpha</span>
          </Link>

          <div className="flex items-center">
            <nav className="hidden md:flex space-x-8 mr-6">
              <Link to="/message-analyzer"
                className={`font-medium hover:text-purple-300 transition-colors ${location.pathname === '/message-analyzer' ? 'text-white' : 'text-purple-300'}`}>
                Message Analyzer
              </Link>
              <Link to="/dashboard"
                className={`font-medium hover:text-purple-300 transition-colors ${location.pathname === '/dashboard' ? 'text-white' : 'text-purple-300'}`}>
                Dashboard
              </Link>
              <Link to="/relationships"
                className={`font-medium hover:text-purple-300 transition-colors ${location.pathname === '/relationships' ? 'text-white' : 'text-purple-300'}`}>
                Relationships
              </Link>
              <Link to="/reports"
                className={`font-medium hover:text-purple-300 transition-colors ${location.pathname === '/reports' ? 'text-white' : 'text-purple-300'}`}>
                Reports
              </Link>
              <Link to="/anomaly-detection"
                className={`font-medium hover:text-purple-300 transition-colors ${location.pathname === '/anomaly-detection' ? 'text-white' : 'text-purple-300'}`}>
                Anomaly Detection
              </Link>
              <Link to="/growth-center"
                className={`font-medium hover:text-purple-300 transition-colors ${location.pathname === '/growth-center' ? 'text-white' : 'text-purple-300'}`}>
                Growth Center
              </Link>
              <Link to="/test"
                className={`font-medium hover:text-purple-300 transition-colors ${location.pathname === '/test' ? 'text-white' : 'text-purple-300'}`}>
                UI Test
              </Link>
              <Link to="/colors"
                className={`font-medium hover:text-purple-300 transition-colors ${location.pathname === '/colors' ? 'text-white' : 'text-purple-300'}`}>
                Colors
              </Link>
              <Link to="/components"
                className={`font-medium hover:text-purple-300 transition-colors ${location.pathname === '/components' ? 'text-white' : 'text-purple-300'}`}>
                Components
              </Link>
              <Link to="/dashboard-showcase"
                className={`font-medium hover:text-purple-300 transition-colors ${location.pathname === '/dashboard-showcase' ? 'text-white' : 'text-purple-300'}`}>
                Dashboard UI
              </Link>
              <Link to="/mobile-test"
                className={`font-medium hover:text-purple-300 transition-colors ${location.pathname === '/mobile-test' ? 'text-white' : 'text-purple-300'}`}>
                Mobile
              </Link>
            </nav>

            <div className="hidden md:flex items-center">
              {/* Theme Toggle */}
              <div className="flex items-center space-x-2 pr-6">
                <ThemeToggle />
              </div>

              {/* Faith Mode Toggle */}
              <div className="flex items-center space-x-2 border-l border-purple-600 pl-6 pr-6">
                <span className="text-xs font-medium">Faith Mode</span>
                <button
                  onClick={toggleFaithMode}
                  className={`relative inline-flex items-center h-5 rounded-full w-10 transition-colors focus:outline-none ${
                    faithModeEnabled ? 'bg-green-500' : 'bg-gray-600'
                  }`}
                  aria-label="Toggle Faith Mode"
                >
                  <span
                    className={`inline-block w-3 h-3 transform bg-white rounded-full transition-transform ${
                      faithModeEnabled ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Auth0 User Profile */}
              <div className="flex items-center space-x-2 border-l border-purple-600 pl-6">
                <DevelopmentAuthButtons />
              </div>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button className="text-white focus:outline-none">
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-gray-400 py-8 mt-12">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between">
          <div className="mb-4 md:mb-0">
            <h3 className="text-white text-lg font-bold mb-2">My ÆI</h3>
            <p className="text-sm">Emotional Intelligence & Relationship Clarity</p>
          </div>
          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-8">
            <div>
              <h4 className="text-white font-medium mb-2">Privacy</h4>
              <p className="text-xs">Your data is encrypted and never stored without consent.</p>
            </div>
            <div>
              <h4 className="text-white font-medium mb-2">Support</h4>
              <p className="text-xs"><EMAIL></p>
            </div>
          </div>
        </div>

        {/* Settings toggles in footer */}
        <div className="mt-6 pt-4 border-t border-gray-800">
          <div className="flex flex-wrap justify-center gap-6">
            <FontToggle />
            <MotionToggle />
          </div>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-800 text-xs text-center">
          &copy; {new Date().getFullYear()} My ÆI. All rights reserved.
        </div>
      </div>
    </footer>
  );
};

const PrivacyBanner = ({ onAccept }) => (
  <div className="fixed bottom-0 left-0 right-0 bg-gray-900 text-white py-4 px-6 shadow-lg z-50">
    <div className="container mx-auto">
      <div className="flex flex-col md:flex-row justify-between items-center">
        <p className="mb-4 md:mb-0 text-sm">
          <span className="font-bold">Privacy Notice:</span> Text is sent to an AI service for analysis (encrypted in transit).
          You can disable this in Settings.
        </p>
        <button
          onClick={onAccept}
          className="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md transition-colors text-sm"
        >
          Accept & Continue
        </button>
      </div>
    </div>
  </div>
);

function MainLayout({ children }) {
  const [showPrivacyBanner, setShowPrivacyBanner] = useState(true);

  useEffect(() => {
    // Check if user has already accepted privacy notice
    const hasAccepted = localStorage.getItem('privacyAccepted');
    if (hasAccepted) {
      setShowPrivacyBanner(false);
    }
  }, []);

  const handlePrivacyAccept = () => {
    localStorage.setItem('privacyAccepted', 'true');
    setShowPrivacyBanner(false);
  };

  return (
    <div className="flex flex-col min-h-screen surface-secondary">
      <Header />
      <main id="main-content" className="flex-grow" tabIndex="-1">
        {children}
      </main>
      <Footer />
      {showPrivacyBanner && <PrivacyBanner onAccept={handlePrivacyAccept} />}
    </div>
  );
}

function App() {
  return (
    <Auth0ProviderWithHistory>
      <MockAuth0Provider>
        <Router>
          <AppProvider>
            <ThemeProvider>
              <FaithProvider>
                <FontProvider>
                  <MotionProvider>
                  <MainLayout>
                    <Suspense fallback={<PageLoader />}>
                      <Routes>
                        <Route path="/" element={<Home />} />
                        <Route path="/dashboard" element={<Dashboard />} />
                        <Route path="/message-analyzer" element={<MessageAnalyzer />} />
                        <Route path="/relationships" element={<RelationshipHub />} />
                        <Route path="/reports" element={<Reports />} />
                        <Route path="/anomaly-detection" element={<AnomalyDetection />} />
                        <Route path="/growth-center" element={<GrowthCenter />} />
                        <Route path="/test" element={<TestPage />} />
                        <Route path="/colors" element={<ColorTestPage />} />
                        <Route path="/components" element={<ComponentShowcase />} />
                        <Route path="/dashboard-showcase" element={<DashboardShowcase />} />
                        <Route path="/mobile-test" element={<MobileTestPage />} />
                        <Route path="/contrast-test" element={<ContrastTestPage />} />
                        <Route path="/typography" element={<TypographyShowcase />} />
                      </Routes>
                    </Suspense>
                  </MainLayout>
                </MotionProvider>
              </FontProvider>
            </FaithProvider>
          </ThemeProvider>
          </AppProvider>
        </Router>
      </MockAuth0Provider>
    </Auth0ProviderWithHistory>
  );
}

export default App;
