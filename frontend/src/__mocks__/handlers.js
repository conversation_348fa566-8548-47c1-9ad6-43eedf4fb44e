import { http, HttpResponse } from 'msw';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

export const handlers = [
  // Authentication endpoints
  http.get(`${API_BASE_URL}/auth/user`, () => {
    return HttpResponse.json({
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User',
      picture: 'https://example.com/avatar.jpg'
    });
  }),

  // Dashboard endpoint
  http.get(`${API_BASE_URL}/dashboard`, () => {
    return HttpResponse.json({
      total_relationships: 5,
      total_analyses: 23,
      recent_activity: [
        {
          id: '1',
          type: 'analysis',
          description: 'Message analyzed with <PERSON>',
          timestamp: '2024-01-15T10:30:00Z'
        }
      ],
      sentiment_timeline: [
        ['2024-01-15', 0.8],
        ['2024-01-16', 0.6],
        ['2024-01-17', 0.9]
      ],
      flag_counts: {
        'emotional_distress': 2,
        'communication_gap': 1,
        'conflict_indicator': 0
      },
      relationship_health_scores: [
        { name: '<PERSON>', score: 85 },
        { name: '<PERSON>', score: 92 }
      ]
    });
  }),

  // Relationships endpoints
  http.get(`${API_BASE_URL}/relationships`, () => {
    return HttpResponse.json([
      {
        id: '1',
        name: 'Sarah',
        relationship_type: 'romantic',
        created_at: '2024-01-01T00:00:00Z',
        health_score: 85,
        total_analyses: 12,
        last_analysis: '2024-01-15T10:30:00Z'
      },
      {
        id: '2',
        name: 'John',
        relationship_type: 'friend',
        created_at: '2024-01-02T00:00:00Z',
        health_score: 92,
        total_analyses: 8,
        last_analysis: '2024-01-14T15:20:00Z'
      }
    ]);
  }),

  http.post(`${API_BASE_URL}/relationships`, async ({ request }) => {
    const newRelationship = await request.json();
    return HttpResponse.json({
      id: 'new-relationship-id',
      ...newRelationship,
      created_at: new Date().toISOString(),
      health_score: 100,
      total_analyses: 0,
      last_analysis: null
    }, { status: 201 });
  }),

  http.put(`${API_BASE_URL}/relationships/:id`, async ({ request, params }) => {
    const updates = await request.json();
    return HttpResponse.json({
      id: params.id,
      ...updates,
      updated_at: new Date().toISOString()
    });
  }),

  http.delete(`${API_BASE_URL}/relationships/:id`, () => {
    return new HttpResponse(null, { status: 204 });
  }),

  http.get(`${API_BASE_URL}/relationships/:id/history`, () => {
    return HttpResponse.json({
      analyses: [
        {
          id: '1',
          message_content: 'Hey, how are you doing?',
          sentiment_score: 0.8,
          emotional_indicators: ['positive', 'caring'],
          communication_flags: [],
          ai_insights: 'This message shows genuine care and concern.',
          timestamp: '2024-01-15T10:30:00Z'
        }
      ],
      sentiment_timeline: [
        ['2024-01-15', 0.8],
        ['2024-01-16', 0.6]
      ],
      sentiment_counts: {
        'positive': 10,
        'neutral': 5,
        'negative': 2
      },
      flag_types: {
        'emotional_distress': 1,
        'communication_gap': 0
      },
      health_trend: [
        ['2024-01-15', 85],
        ['2024-01-16', 87]
      ]
    });
  }),

  // Message analysis endpoint
  http.post(`${API_BASE_URL}/analyze`, async ({ request }) => {
    const analysis = await request.json();
    return HttpResponse.json({
      id: 'analysis-id',
      sentiment_score: 0.75,
      emotional_indicators: ['positive', 'excitement'],
      communication_flags: [],
      ai_insights: 'This message demonstrates positive communication with enthusiasm.',
      relationship_impact: 'Likely to strengthen the relationship',
      suggested_responses: [
        'That sounds wonderful! Tell me more about it.',
        'I\'m so happy to hear that!'
      ],
      timestamp: new Date().toISOString()
    });
  }),

  // Anomaly detection endpoint
  http.get(`${API_BASE_URL}/anomalies`, () => {
    return HttpResponse.json([
      {
        id: '1',
        type: 'sentiment_drop',
        severity: 'medium',
        relationship_id: '1',
        description: 'Significant drop in positive sentiment detected',
        timestamp: '2024-01-15T10:30:00Z',
        details: {
          previous_score: 0.8,
          current_score: 0.4,
          threshold: 0.3
        }
      }
    ]);
  }),

  // Reports endpoint
  http.get(`${API_BASE_URL}/reports`, () => {
    return HttpResponse.json({
      summary: {
        total_relationships: 5,
        total_analyses: 23,
        average_sentiment: 0.72,
        most_active_relationship: 'Sarah'
      },
      sentiment_timeline: [
        ['2024-01-15', 0.8],
        ['2024-01-16', 0.6]
      ],
      flag_counts: {
        'emotional_distress': 2,
        'communication_gap': 1
      },
      relationship_insights: [
        {
          relationship_name: 'Sarah',
          health_score: 85,
          key_insights: ['Strong emotional connection', 'Good communication patterns']
        }
      ]
    });
  }),

  // Growth center endpoints
  http.get(`${API_BASE_URL}/growth-plans`, () => {
    return HttpResponse.json([
      {
        id: '1',
        title: 'Improve Communication Skills',
        description: 'Focus on active listening and emotional validation',
        target_relationship: 'Sarah',
        activities: [
          {
            id: '1',
            title: 'Practice active listening daily',
            description: 'Spend 10 minutes each day practicing active listening techniques',
            completed: false,
            due_date: '2024-01-20'
          }
        ],
        progress: 25,
        created_at: '2024-01-10T00:00:00Z'
      }
    ]);
  }),

  http.post(`${API_BASE_URL}/growth-plans`, async ({ request }) => {
    const plan = await request.json();
    return HttpResponse.json({
      id: 'new-plan-id',
      ...plan,
      progress: 0,
      created_at: new Date().toISOString()
    }, { status: 201 });
  }),

  // User settings endpoint
  http.get(`${API_BASE_URL}/settings`, () => {
    return HttpResponse.json({
      faith_mode_enabled: false,
      font_preference: 'default',
      motion_preference: 'enabled',
      privacy_level: 'standard',
      notification_preferences: {
        email_notifications: true,
        push_notifications: false
      }
    });
  }),

  http.put(`${API_BASE_URL}/settings`, async ({ request }) => {
    const settings = await request.json();
    return HttpResponse.json({
      ...settings,
      updated_at: new Date().toISOString()
    });
  }),

  // Error simulation endpoints for testing error handling
  http.get(`${API_BASE_URL}/test/error/500`, () => {
    return new HttpResponse(null, { status: 500 });
  }),

  http.get(`${API_BASE_URL}/test/error/404`, () => {
    return new HttpResponse(null, { status: 404 });
  }),

  http.get(`${API_BASE_URL}/test/error/timeout`, () => {
    return new Promise(() => {}); // Never resolves to simulate timeout
  }),

  // Slow response simulation for performance testing
  http.get(`${API_BASE_URL}/test/slow`, async () => {
    await new Promise(resolve => setTimeout(resolve, 3000));
    return HttpResponse.json({ message: 'Slow response' });
  })
];

export default handlers;