/* Additional custom styles - most styling is done with Tai<PERSON><PERSON> directly in the JSX */

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Custom focus styles */
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid rgba(124, 58, 237, 0.5);
  outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Enhanced Typography & Brand Styling */
.drop-shadow-glow {
  filter: drop-shadow(0 0 20px rgba(251, 191, 36, 0.4)) drop-shadow(0 0 40px rgba(245, 158, 11, 0.2));
}

.text-shadow-luxury {
  text-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.1);
}

/* Custom letter spacing for luxury typography */
.tracking-luxury {
  letter-spacing: 0.05em;
}

.tracking-brand {
  letter-spacing: 0.1em;
}

/* Enhanced gradient text effect */
.gradient-text-luxury {
  background: linear-gradient(135deg, #fff 0%, #e0e7ff 50%, #c7d2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-brand {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Sophisticated hover animations */
.brand-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.brand-hover:hover {
  transform: translateY(-2px);
  filter: drop-shadow(0 0 30px rgba(251, 191, 36, 0.6));
}

/* Animations */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(124, 58, 237, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(124, 58, 237, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(124, 58, 237, 0);
  }
}

@keyframes subtle-glow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  }
  50% {
    text-shadow: 0 0 30px rgba(251, 191, 36, 0.5), 0 0 40px rgba(245, 158, 11, 0.3);
  }
}

.animate-subtle-glow {
  animation: subtle-glow 4s ease-in-out infinite;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Enhanced floating animation with different speeds */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Gradient animation for background elements */
@keyframes gradient-x {
  0%, 100% {
    background-size: 200% 200%;
    background-position: left center;
  }
  50% {
    background-size: 200% 200%;
    background-position: right center;
  }
}

.animate-gradient-x {
  animation: gradient-x 15s ease infinite;
}

/* Slow pulse for glow effects */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced shadow effects for buttons */
.shadow-floating {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.4), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .animate-gradient-x,
  .animate-pulse-slow,
  .animate-subtle-glow {
    animation: none;
  }
}

/* Hero Section Enhancements */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

/* Performance-optimized animated gradient using background-position */
.hero-gradient-animated {
  background: linear-gradient(
    135deg,
    rgba(147, 51, 234, 0.3) 0%,
    rgba(79, 70, 229, 0.3) 25%,
    rgba(99, 102, 241, 0.3) 50%,
    rgba(147, 51, 234, 0.3) 75%,
    rgba(79, 70, 229, 0.3) 100%
  );
  background-size: 400% 400%;
  animation: gradient-shift 20s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Secondary gradient for depth */
.hero-gradient-secondary {
  background: radial-gradient(
    ellipse at top left,
    rgba(251, 191, 36, 0.1) 0%,
    transparent 50%
  ),
  radial-gradient(
    ellipse at bottom right,
    rgba(167, 139, 250, 0.1) 0%,
    transparent 50%
  );
  mix-blend-mode: screen;
}

/* Mesh gradient for texture */
.hero-mesh-gradient {
  background-image: 
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.03) 2px,
      rgba(255, 255, 255, 0.03) 4px
    ),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.03) 2px,
      rgba(255, 255, 255, 0.03) 4px
    );
}

/* Parallax orbs with CSS variables for CLS optimization */
.hero-orb {
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.6;
  will-change: transform;
  transition: transform 0.3s ease-out;
}

.hero-orb-1 {
  top: 10%;
  left: 5%;
  width: var(--orb-size-md);
  height: var(--orb-size-md);
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.3), rgba(245, 158, 11, 0.3));
  animation: float-slow 15s ease-in-out infinite;
}

.hero-orb-2 {
  top: 30%;
  right: 10%;
  width: var(--orb-size-lg);
  height: var(--orb-size-lg);
  background: linear-gradient(135deg, rgba(167, 139, 250, 0.3), rgba(139, 92, 246, 0.3));
  animation: float-slow 20s ease-in-out infinite reverse;
}

.hero-orb-3 {
  bottom: 15%;
  left: 25%;
  width: var(--orb-size-sm);
  height: var(--orb-size-sm);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(79, 70, 229, 0.3));
  animation: float-slow 18s ease-in-out infinite;
  animation-delay: 2s;
}

@keyframes float-slow {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.05);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.95);
  }
}

/* Trust Badges Styling */
.trust-badge {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 9999px;
  transition: all 0.3s ease;
}

.trust-badge:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.trust-badge-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.trust-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.trust-avatar-1 {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.trust-avatar-2 {
  background: linear-gradient(135deg, #ec4899, #a855f7);
}

.trust-avatar-3 {
  background: linear-gradient(135deg, #6366f1, #3b82f6);
}

.trust-avatar-4 {
  background: linear-gradient(135deg, #10b981, #059669);
}

.trust-badge-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.trust-badge-number {
  font-size: 1.125rem;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.trust-badge-label {
  font-size: 0.75rem;
  color: rgba(229, 231, 235, 0.8);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Enhanced CTA Button Hierarchy */
.cta-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

@media (min-width: 640px) {
  .cta-container {
    flex-direction: row;
    justify-content: center;
  }
}

.cta-primary-wrapper {
  position: relative;
}

.cta-primary {
  position: relative;
  font-size: 1.25rem;
  font-weight: 600;
  padding: 1.25rem 2.5rem;
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.9), rgba(245, 158, 11, 0.9));
  color: #1f2937;
  border: 2px solid rgba(251, 191, 36, 0.3);
  border-radius: 9999px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 
    0 4px 15px rgba(251, 191, 36, 0.3),
    0 8px 30px rgba(0, 0, 0, 0.1);
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 6px 20px rgba(251, 191, 36, 0.4),
    0 12px 40px rgba(0, 0, 0, 0.15);
}

.cta-link {
  display: block;
  position: relative;
  z-index: 1;
}

.cta-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.cta-text {
  font-weight: 600;
}

.cta-icon {
  transition: transform 0.3s ease;
}

.cta-icon-sparkle {
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
}

.cta-icon-arrow {
  opacity: 0.8;
}

.cta-primary:hover .cta-icon-arrow {
  transform: translateX(4px);
}

.cta-shimmer {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.cta-primary:hover .cta-shimmer {
  left: 100%;
}

.cta-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 9999px;
  opacity: 0;
  filter: blur(10px);
  transition: opacity 0.3s ease;
  z-index: -1;
}

.cta-primary:hover .cta-glow {
  opacity: 0.6;
}

.cta-pulse {
  position: absolute;
  inset: -20px;
  border: 2px solid rgba(251, 191, 36, 0.5);
  border-radius: 9999px;
  animation: pulse-ring 2s ease-out infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.95);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}

.cta-secondary {
  font-size: 1.125rem;
  font-weight: 600;
  padding: 1rem 2rem;
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 9999px;
  transition: all 0.3s ease;
}

.cta-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.cta-secondary:hover .cta-icon {
  transform: scale(1.1);
}

/* Trust Signal Styling */
.trust-signal-container {
  margin-top: 2rem;
}

.trust-signal {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 9999px;
  font-size: 0.875rem;
}

.trust-signal-icon {
  color: rgba(251, 191, 36, 0.8);
}

.trust-signal-text {
  color: rgba(229, 231, 235, 0.8);
  font-weight: 300;
}

.trust-signal-separator {
  color: rgba(229, 231, 235, 0.4);
  margin: 0 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .hero-orb-1 {
    width: calc(var(--orb-size-md) * 0.75);
    height: calc(var(--orb-size-md) * 0.75);
  }
  
  .hero-orb-2 {
    width: calc(var(--orb-size-lg) * 0.75);
    height: calc(var(--orb-size-lg) * 0.75);
  }
  
  .hero-orb-3 {
    width: calc(var(--orb-size-sm) * 0.75);
    height: calc(var(--orb-size-sm) * 0.75);
  }
  
  .trust-badge {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
  
  .cta-primary {
    font-size: 1.125rem;
    padding: 1rem 2rem;
  }
  
  .cta-secondary {
    font-size: 1rem;
    padding: 0.875rem 1.75rem;
  }
}
