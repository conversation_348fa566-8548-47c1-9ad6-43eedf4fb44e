<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Component Testing - My ÆI Alpha</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Test our new design tokens */
        .test-primary { background-color: #2A9D8F; }
        .test-primary-50 { background-color: #f0fdfa; }
        .test-primary-600 { background-color: #0d9488; }
        
        .test-typography h1 { font-size: 2.25rem; font-weight: bold; }
        .test-typography h2 { font-size: 1.875rem; font-weight: 600; }
        .test-typography h3 { font-size: 1.5rem; font-weight: 600; }
        
        .test-button-primary {
            background-color: #2A9D8F;
            color: white;
            padding: 0.625rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }
        .test-button-primary:hover {
            background-color: #0d9488;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .test-card-default {
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1.5rem;
            transition: all 0.2s ease-in-out;
        }
        .test-card-default:hover {
            border-color: #d1d5db;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto space-y-8">
        <h1 class="text-4xl font-bold text-gray-900 mb-8">UI Component Testing</h1>
        
        <!-- Typography Testing -->
        <section class="test-card-default">
            <h2 class="text-2xl font-semibold mb-4">Typography Hierarchy</h2>
            <div class="test-typography space-y-4">
                <h1>Heading 1 - Main Page Title</h1>
                <h2>Heading 2 - Section Title</h2>
                <h3>Heading 3 - Subsection Title</h3>
                <p class="text-base text-gray-700">Body text with improved readability and consistent line height.</p>
                <p class="text-sm text-gray-600">Secondary text for less important information.</p>
            </div>
        </section>
        
        <!-- Color System Testing -->
        <section class="test-card-default">
            <h2 class="text-2xl font-semibold mb-4">Enhanced Color System</h2>
            <div class="grid grid-cols-3 gap-4">
                <div class="space-y-2">
                    <div class="test-primary-50 p-4 rounded text-center">Primary 50</div>
                    <div class="test-primary p-4 rounded text-center text-white">Primary 500</div>
                    <div class="test-primary-600 p-4 rounded text-center text-white">Primary 600</div>
                </div>
            </div>
        </section>
        
        <!-- Button Testing -->
        <section class="test-card-default">
            <h2 class="text-2xl font-semibold mb-4">Simplified Button Variants</h2>
            <div class="space-x-4">
                <button class="test-button-primary">Primary Button</button>
                <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2.5 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200">Secondary Button</button>
                <button class="bg-transparent text-gray-700 px-4 py-2.5 rounded-lg font-medium hover:bg-gray-100 transition-all duration-200">Ghost Button</button>
                <button class="bg-red-500 text-white px-4 py-2.5 rounded-lg font-medium hover:bg-red-600 transition-all duration-200">Danger Button</button>
            </div>
        </section>
        
        <!-- Card Testing -->
        <section class="space-y-4">
            <h2 class="text-2xl font-semibold">Card Variants</h2>
            
            <div class="test-card-default">
                <h3 class="text-lg font-medium mb-2">Default Card</h3>
                <p class="text-gray-600">Clean styling with subtle border and hover effects.</p>
            </div>
            
            <div class="bg-white border border-gray-300 rounded-lg p-6 shadow-lg hover:shadow-xl transition-all duration-200">
                <h3 class="text-lg font-medium mb-2">Elevated Card</h3>
                <p class="text-gray-600">More prominent shadow for important content.</p>
            </div>
            
            <div class="bg-white border border-gray-300 rounded-lg p-6 hover:bg-gray-50 hover:shadow-md transition-all duration-200 cursor-pointer">
                <h3 class="text-lg font-medium mb-2">Interactive Card</h3>
                <p class="text-gray-600">Hover effects for clickable content.</p>
            </div>
        </section>
        
        <!-- Spacing Testing -->
        <section class="test-card-default">
            <h2 class="text-2xl font-semibold mb-4">Consistent Spacing System</h2>
            <div class="space-y-4">
                <div class="bg-blue-100 p-2 rounded">8px padding (sm)</div>
                <div class="bg-blue-200 p-4 rounded">16px padding (md)</div>
                <div class="bg-blue-300 p-6 rounded">24px padding (lg)</div>
                <div class="bg-blue-400 p-8 rounded text-white">32px padding (xl)</div>
            </div>
        </section>
        
        <!-- Focus Testing -->
        <section class="test-card-default">
            <h2 class="text-2xl font-semibold mb-4">Enhanced Focus States</h2>
            <p class="text-gray-600 mb-4">Tab through these elements to test focus indicators:</p>
            <div class="space-x-4">
                <button class="test-button-primary focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">Focusable Button</button>
                <input type="text" placeholder="Focusable Input" class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <a href="#" class="text-blue-600 underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded">Focusable Link</a>
            </div>
        </section>
    </div>
</body>
</html>
