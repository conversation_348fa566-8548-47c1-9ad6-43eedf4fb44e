import React from 'react';
import { render as rtlRender } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { axe, toHaveNoViolations } from 'jest-axe';

// Context Providers
import { AppProvider } from '../../contexts/AppContext';
import { FaithProvider } from '../../contexts/FaithContext';
import { FontProvider } from '../../contexts/FontContext';
import { MotionProvider } from '../../contexts/MotionContext';

// Mock Auth0 Provider
const MockAuth0Provider = ({ children }) => children;

// Custom render function that wraps components with all providers
function render(ui, options = {}) {
  const {
    initialEntries = ['/'],
    route = '/',
    ...renderOptions
  } = options;

  // Create wrapper with all providers
  const Wrapper = ({ children }) => (
    <BrowserRouter>
      <MockAuth0Provider>
        <AppProvider>
          <FaithProvider>
            <FontProvider>
              <MotionProvider>
                {children}
              </MotionProvider>
            </FontProvider>
          </FaithProvider>
        </AppProvider>
      </MockAuth0Provider>
    </BrowserRouter>
  );

  return rtlRender(ui, { wrapper: Wrapper, ...renderOptions });
}

// Accessibility testing helper
export const testAccessibility = async (component) => {
  const results = await axe(component);
  expect(results).toHaveNoViolations();
};

// Performance testing helper
export const testPerformance = (callback, maxTime = 1000) => {
  const startTime = performance.now();
  callback();
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  expect(duration).toBeLessThan(maxTime);
  return duration;
};

// Visual regression testing helper
export const takeSnapshot = (component) => {
  expect(component).toMatchSnapshot();
};

// Common test data
export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Test User',
  picture: 'https://example.com/avatar.jpg'
};

export const mockRelationship = {
  id: '1',
  name: 'Sarah',
  relationship_type: 'romantic',
  created_at: '2024-01-01T00:00:00Z',
  health_score: 85,
  total_analyses: 12,
  last_analysis: '2024-01-15T10:30:00Z'
};

export const mockAnalysis = {
  id: 'analysis-id',
  sentiment_score: 0.75,
  emotional_indicators: ['positive', 'excitement'],
  communication_flags: [],
  ai_insights: 'This message demonstrates positive communication with enthusiasm.',
  relationship_impact: 'Likely to strengthen the relationship',
  suggested_responses: [
    'That sounds wonderful! Tell me more about it.',
    'I\'m so happy to hear that!'
  ],
  timestamp: '2024-01-15T10:30:00Z'
};

export const mockDashboardData = {
  total_relationships: 5,
  total_analyses: 23,
  recent_activity: [
    {
      id: '1',
      type: 'analysis',
      description: 'Message analyzed with Sarah',
      timestamp: '2024-01-15T10:30:00Z'
    }
  ],
  sentiment_timeline: [
    ['2024-01-15', 0.8],
    ['2024-01-16', 0.6],
    ['2024-01-17', 0.9]
  ],
  flag_counts: {
    'emotional_distress': 2,
    'communication_gap': 1,
    'conflict_indicator': 0
  },
  relationship_health_scores: [
    { name: 'Sarah', score: 85 },
    { name: 'John', score: 92 }
  ]
};

// Custom matchers
expect.extend({
  toBeValidEmail(received) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const pass = emailRegex.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid email`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid email`,
        pass: false,
      };
    }
  },
  
  toHaveValidSentimentScore(received) {
    const pass = typeof received === 'number' && received >= -1 && received <= 1;
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid sentiment score`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid sentiment score between -1 and 1`,
        pass: false,
      };
    }
  }
});

// Wait for async operations
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};

// Mock API responses
export const mockApiResponse = (data, status = 200) => {
  return Promise.resolve({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
  });
};

// Mock API error
export const mockApiError = (status = 500, message = 'Internal Server Error') => {
  return Promise.reject({
    status,
    message,
    response: {
      status,
      data: { error: message }
    }
  });
};

// Form testing helpers
export const fillForm = async (user, fields) => {
  for (const [fieldName, value] of Object.entries(fields)) {
    const field = screen.getByLabelText(new RegExp(fieldName, 'i'));
    await user.clear(field);
    await user.type(field, value);
  }
};

export const submitForm = async (user, submitButtonText = /submit/i) => {
  const submitButton = screen.getByRole('button', { name: submitButtonText });
  await user.click(submitButton);
};

// Re-export everything from testing-library
export * from '@testing-library/react';
export { render };
export { axe };