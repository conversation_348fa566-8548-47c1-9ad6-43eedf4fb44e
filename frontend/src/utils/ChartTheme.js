/**
 * Comprehensive Recharts theme configuration
 * Includes colors, typography, tooltips, animations, dark mode support, and accessibility
 */

import { useContext, useEffect, useState } from 'react';
import MotionContext from '../contexts/MotionContext';

// Color Tokens for light and dark modes
export const chartColors = {
  light: {
    // Primary chart colors
    primary: ['#2A9D8F', '#238F82', '#31AE9E', '#1D7A6E', '#3BBFAD'],
    secondary: {
      coral: ['#E76F51', '#D35F41', '#F27F61', '#C54F31', '#F88F71'],
      lavender: ['#9D8DF1', '#8D7DE1', '#AD9DF1', '#7D6DD1', '#BDADF1'],
      sage: ['#87A878', '#77A868', '#97B888', '#678858', '#A7C898'],
    },
    
    // Semantic colors
    emotion: {
      positive: '#4AD295',
      neutral: '#A5B4FC',
      negative: '#E56B6F',
    },
    
    // Gradients for special effects
    gradients: {
      primary: ['#2A9D8F', '#0d9488'],
      secondary: ['#9D8DF1', '#E76F51'],
      emotional: ['#4AD295', '#A5B4FC', '#E56B6F'],
      warm: ['#E76F51', '#f59e0b', '#fbbf24'],
      cool: ['#2A9D8F', '#3b82f6', '#9D8DF1'],
    },
    
    // UI colors
    background: '#FFFFFF',
    surface: '#f9fafb',
    text: {
      primary: '#111827',
      secondary: '#6b7280',
      tertiary: '#9ca3af',
    },
    grid: '#e5e7eb',
    border: '#d1d5db',
  },
  
  dark: {
    // Primary chart colors - adjusted for dark mode
    primary: ['#31AE9E', '#3BBFAD', '#2A9D8F', '#45C2B1', '#238F82'],
    secondary: {
      coral: ['#F27F61', '#F88F71', '#E76F51', '#F99F81', '#D35F41'],
      lavender: ['#AD9DF1', '#BDADF1', '#9D8DF1', '#CDBDF1', '#8D7DE1'],
      sage: ['#97B888', '#A7C898', '#87A878', '#B7D8A8', '#77A868'],
    },
    
    // Semantic colors - brighter for dark mode
    emotion: {
      positive: '#5AE2A5',
      neutral: '#B5C4FC',
      negative: '#F57B7F',
    },
    
    // Gradients adjusted for dark mode
    gradients: {
      primary: ['#31AE9E', '#45C2B1'],
      secondary: ['#AD9DF1', '#F27F61'],
      emotional: ['#5AE2A5', '#B5C4FC', '#F57B7F'],
      warm: ['#F27F61', '#f59e0b', '#fcd34d'],
      cool: ['#31AE9E', '#60a5fa', '#AD9DF1'],
    },
    
    // UI colors for dark mode
    background: '#0f172a',
    surface: '#1e293b',
    text: {
      primary: '#f9fafb',
      secondary: '#d1d5db',
      tertiary: '#9ca3af',
    },
    grid: '#334155',
    border: '#475569',
  }
};

// Typography configuration
export const chartTypography = {
  fontFamily: "'Orbitron', 'ui-sans-serif', 'system-ui', '-apple-system', 'sans-serif'",
  fontSize: {
    tick: 12,
    label: 14,
    title: 16,
    tooltip: 13,
  },
  fontWeight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
};

// Animation configurations
export const chartAnimations = {
  // Entry animations
  entry: {
    duration: 800,
    easing: 'ease-out',
    delay: 0,
  },
  
  // Update animations
  update: {
    duration: 300,
    easing: 'ease-in-out',
  },
  
  // Hover animations
  hover: {
    duration: 200,
    scale: 1.05,
  },
  
  // Reduced motion animations
  reduced: {
    duration: 0,
    easing: 'linear',
  }
};

// Custom Tooltip Component
export const CustomTooltip = ({ active, payload, label, isDarkMode = false }) => {
  if (!active || !payload || !payload.length) return null;

  const colors = isDarkMode ? chartColors.dark : chartColors.light;
  
  return (
    <div
      className={`
        px-3 py-2 rounded-lg shadow-lg backdrop-blur-md
        ${isDarkMode 
          ? 'bg-gray-800/90 border border-gray-700' 
          : 'bg-white/90 border border-gray-200'
        }
      `}
      style={{
        fontFamily: chartTypography.fontFamily,
        fontSize: chartTypography.fontSize.tooltip,
      }}
    >
      {label && (
        <p className={`font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          {label}
        </p>
      )}
      {payload.map((entry, index) => (
        <p
          key={`tooltip-${index}`}
          className={`flex items-center gap-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-600'}`}
        >
          <span
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: entry.color }}
          />
          <span className="font-medium">{entry.name}:</span>
          <span className="font-semibold">{entry.value}</span>
        </p>
      ))}
    </div>
  );
};

// Custom Legend Component
export const CustomLegend = ({ payload, isDarkMode = false }) => {
  const colors = isDarkMode ? chartColors.dark : chartColors.light;
  
  return (
    <ul className="flex flex-wrap justify-center gap-4 mt-4">
      {payload.map((entry, index) => (
        <li
          key={`legend-${index}`}
          className={`flex items-center gap-2 text-sm ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}
          style={{ fontFamily: chartTypography.fontFamily }}
        >
          <span
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: entry.color }}
          />
          <span>{entry.value}</span>
        </li>
      ))}
    </ul>
  );
};

// Hook to get current theme based on dark mode
export const useChartTheme = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const motionContext = useContext(MotionContext);
  const prefersReducedMotion = motionContext?.isReducedMotion ?? false;

  useEffect(() => {
    // Check for dark mode class on document
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'));
    };

    // Initial check
    checkDarkMode();

    // Watch for changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class'],
    });

    return () => observer.disconnect();
  }, []);

  const colors = isDarkMode ? chartColors.dark : chartColors.light;
  const animations = prefersReducedMotion ? chartAnimations.reduced : chartAnimations.entry;

  return {
    colors,
    typography: chartTypography,
    animations,
    isDarkMode,
    prefersReducedMotion,
  };
};

// Chart configuration helpers
export const getChartConfig = (type = 'bar', isDarkMode = false, prefersReducedMotion = false) => {
  const colors = isDarkMode ? chartColors.dark : chartColors.light;
  const animations = prefersReducedMotion ? chartAnimations.reduced : chartAnimations.entry;
  
  const baseConfig = {
    margin: { top: 20, right: 30, bottom: 20, left: 30 },
    style: {
      fontFamily: chartTypography.fontFamily,
    },
  };

  const configs = {
    bar: {
      ...baseConfig,
      barGap: 8,
      barCategoryGap: '20%',
      animationBegin: animations.delay,
      animationDuration: animations.duration,
      animationEasing: animations.easing,
    },
    line: {
      ...baseConfig,
      strokeWidth: 3,
      dot: { 
        r: 4, 
        strokeWidth: 2,
        fill: colors.background,
      },
      activeDot: { 
        r: 6,
        strokeWidth: 0,
        style: {
          filter: isDarkMode 
            ? 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.4))' 
            : 'drop-shadow(0 0 8px rgba(0, 0, 0, 0.2))',
        }
      },
      animationBegin: animations.delay,
      animationDuration: animations.duration,
      animationEasing: animations.easing,
    },
    pie: {
      ...baseConfig,
      cx: '50%',
      cy: '50%',
      animationBegin: animations.delay,
      animationDuration: animations.duration * 1.5,
      animationEasing: animations.easing,
      innerRadius: 0,
      outerRadius: '80%',
      paddingAngle: 2,
    },
    area: {
      ...baseConfig,
      strokeWidth: 2,
      fillOpacity: 0.3,
      animationBegin: animations.delay,
      animationDuration: animations.duration,
      animationEasing: animations.easing,
    },
  };

  return configs[type] || baseConfig;
};

// Get axis style configuration
export const getAxisStyle = (isDarkMode = false) => {
  const colors = isDarkMode ? chartColors.dark : chartColors.light;
  
  return {
    stroke: colors.text.secondary,
    style: {
      fontSize: chartTypography.fontSize.tick,
      fontFamily: chartTypography.fontFamily,
      fill: colors.text.secondary,
    },
    tick: {
      fill: colors.text.secondary,
    },
  };
};

// Get grid style configuration
export const getGridStyle = (isDarkMode = false) => {
  const colors = isDarkMode ? chartColors.dark : chartColors.light;
  
  return {
    stroke: colors.grid,
    strokeDasharray: '3 3',
    strokeOpacity: 0.5,
  };
};

// Color scheme generators for different chart types
export const getColorScheme = (type = 'categorical', count = 6, isDarkMode = false) => {
  const colors = isDarkMode ? chartColors.dark : chartColors.light;
  
  const schemes = {
    categorical: [
      ...colors.primary.slice(0, 2),
      colors.secondary.coral[0],
      colors.secondary.lavender[0],
      colors.secondary.sage[0],
      colors.primary[2],
    ],
    sequential: colors.primary,
    diverging: [
      colors.emotion.negative,
      '#F0A5A5',
      colors.emotion.neutral,
      '#B8E0D2',
      colors.emotion.positive,
    ],
    emotional: [
      colors.emotion.negative,
      colors.emotion.neutral,
      colors.emotion.positive,
    ],
    warm: colors.gradients.warm,
    cool: colors.gradients.cool,
  };

  const scheme = schemes[type] || schemes.categorical;
  return scheme.slice(0, count);
};

// Gradient definitions for charts
export const chartGradients = {
  primary: (id = 'primaryGradient') => ({
    id,
    x1: "0",
    y1: "0",
    x2: "0",
    y2: "1",
    stops: [
      { offset: "0%", color: "#2A9D8F", opacity: 1 },
      { offset: "100%", color: "#0d9488", opacity: 0.8 },
    ],
  }),
  
  secondary: (id = 'secondaryGradient') => ({
    id,
    x1: "0",
    y1: "0",
    x2: "1",
    y2: "0",
    stops: [
      { offset: "0%", color: "#9D8DF1", opacity: 1 },
      { offset: "100%", color: "#E76F51", opacity: 1 },
    ],
  }),
  
  emotional: (id = 'emotionalGradient') => ({
    id,
    x1: "0",
    y1: "0",
    x2: "1",
    y2: "0",
    stops: [
      { offset: "0%", color: "#4AD295", opacity: 1 },
      { offset: "50%", color: "#A5B4FC", opacity: 1 },
      { offset: "100%", color: "#E56B6F", opacity: 1 },
    ],
  }),
  
  fadeOut: (color, id = 'fadeOutGradient') => ({
    id,
    x1: "0",
    y1: "0",
    x2: "0",
    y2: "1",
    stops: [
      { offset: "0%", color, opacity: 0.8 },
      { offset: "100%", color, opacity: 0.1 },
    ],
  }),
};

// Render gradient definitions for SVG
export const renderGradient = (gradient) => (
  <linearGradient key={gradient.id} id={gradient.id} x1={gradient.x1} y1={gradient.y1} x2={gradient.x2} y2={gradient.y2}>
    {gradient.stops.map((stop, index) => (
      <stop
        key={index}
        offset={stop.offset}
        stopColor={stop.color}
        stopOpacity={stop.opacity}
      />
    ))}
  </linearGradient>
);

// Export all theme utilities
export default {
  chartColors,
  chartTypography,
  chartAnimations,
  CustomTooltip,
  CustomLegend,
  useChartTheme,
  getChartConfig,
  getAxisStyle,
  getGridStyle,
  getColorScheme,
  chartGradients,
  renderGradient,
};
