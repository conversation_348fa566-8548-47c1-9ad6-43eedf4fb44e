/**
 * Chart color schemes based on the new color palette
 * For use with Recharts and other visualization libraries
 */

// Primary color and its variations
export const primaryColors = [
  '#2A9D8F', // primary
  '#238F82', // darker
  '#31AE9E', // lighter
  '#1D7A6E', // darkest
  '#3BBFAD', // lightest
];

// Secondary colors
export const secondaryColors = [
  '#E76F51', // coral
  '#9D8DF1', // lavender
  '#87A878', // sage
  '#D35F41', // darker coral
  '#8D7DE1', // darker lavender
  '#77A868', // darker sage
];

// Emotional state colors
export const emotionColors = {
  positive: '#4AD295',
  neutral: '#A5B4FC',
  negative: '#E56B6F',
};

// Color schemes for different chart types
export const chartSchemes = {
  // For bar charts
  bar: [
    primaryColors[0],
    secondaryColors[0],
    secondaryColors[1],
    secondaryColors[2],
    primaryColors[1],
    primaryColors[2],
  ],
  
  // For line charts
  line: [
    primaryColors[0],
    secondaryColors[0],
    secondaryColors[1],
    secondaryColors[2],
    primaryColors[1],
    primaryColors[2],
  ],
  
  // For pie/donut charts
  pie: [
    primaryColors[0],
    secondaryColors[0],
    secondaryColors[1],
    secondaryColors[2],
    primaryColors[1],
    primaryColors[2],
    primaryColors[3],
    primaryColors[4],
    secondaryColors[3],
    secondaryColors[4],
    secondaryColors[5],
  ],
  
  // For health score indicators
  health: [
    emotionColors.negative,
    '#E87F7F', // lighter negative
    '#F0A5A5', // lightest negative
    '#D4D4D4', // neutral gray
    '#B8E0D2', // lightest positive
    '#8FCDB8', // lighter positive
    emotionColors.positive,
  ],
  
  // For sentiment analysis
  sentiment: [
    emotionColors.negative,
    '#E87F7F', // lighter negative
    '#F0A5A5', // lightest negative
    emotionColors.neutral,
    '#B8E0D2', // lightest positive
    '#8FCDB8', // lighter positive
    emotionColors.positive,
  ],
  
  // For categorical data
  categorical: [
    primaryColors[0],
    secondaryColors[0],
    secondaryColors[1],
    secondaryColors[2],
    primaryColors[1],
    primaryColors[2],
    primaryColors[3],
    primaryColors[4],
    secondaryColors[3],
    secondaryColors[4],
    secondaryColors[5],
  ],
  
  // For sequential data (light to dark)
  sequential: [
    '#D1ECE9', // lightest primary
    '#A3DED7', // lighter primary
    '#75CFC5', // light primary
    primaryColors[0], // primary
    '#238F82', // dark primary
    '#1D7A6E', // darker primary
    '#165F56', // darkest primary
  ],
};

// Function to get colors for a specific chart type
export const getChartColors = (type = 'bar', count = 6) => {
  const colors = chartSchemes[type] || chartSchemes.categorical;
  return colors.slice(0, count);
};

// Function to get a color for a specific emotion
export const getEmotionColor = (emotion) => {
  return emotionColors[emotion] || emotionColors.neutral;
};

// Function to get a color for a specific health score (0-100)
export const getHealthScoreColor = (score) => {
  if (score >= 80) return emotionColors.positive;
  if (score >= 60) return '#8FCDB8'; // lighter positive
  if (score >= 40) return emotionColors.neutral;
  if (score >= 20) return '#E87F7F'; // lighter negative
  return emotionColors.negative;
};

// Recharts color scale for continuous data
export const continuousColorScale = [
  emotionColors.negative,
  '#E87F7F', // lighter negative
  emotionColors.neutral,
  '#8FCDB8', // lighter positive
  emotionColors.positive,
];

// Export default object with all color schemes
export default {
  primaryColors,
  secondaryColors,
  emotionColors,
  chartSchemes,
  getChartColors,
  getEmotionColor,
  getHealthScoreColor,
  continuousColorScale,
};