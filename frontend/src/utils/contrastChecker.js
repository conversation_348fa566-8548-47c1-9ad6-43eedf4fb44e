/**
 * Contrast Checker Utility for WCAG AA Compliance
 * Ensures glass morphism and other color combinations meet accessibility standards
 */

/**
 * Convert hex color to RGB values
 * @param {string} hex - Hex color code (e.g., "#ffffff")
 * @returns {object} RGB values {r, g, b}
 */
export const hexToRgb = (hex) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
};

/**
 * Convert RGB to relative luminance
 * @param {object} rgb - RGB values {r, g, b}
 * @returns {number} Relative luminance value
 */
export const getLuminance = (rgb) => {
  const { r, g, b } = rgb;
  
  // Convert to sRGB
  const rsRGB = r / 255;
  const gsRGB = g / 255;
  const bsRGB = b / 255;
  
  // Apply gamma correction
  const rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
  const gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
  const bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);
  
  // Calculate relative luminance
  return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
};

/**
 * Calculate contrast ratio between two colors
 * @param {string} color1 - First color (hex)
 * @param {string} color2 - Second color (hex)
 * @returns {number} Contrast ratio
 */
export const getContrastRatio = (color1, color2) => {
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) return 0;
  
  const lum1 = getLuminance(rgb1);
  const lum2 = getLuminance(rgb2);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
};

/**
 * Check if contrast ratio meets WCAG standards
 * @param {number} ratio - Contrast ratio
 * @param {string} level - WCAG level ('AA' or 'AAA')
 * @param {string} size - Text size ('normal' or 'large')
 * @returns {boolean} Whether the ratio meets the standard
 */
export const meetsWCAG = (ratio, level = 'AA', size = 'normal') => {
  const standards = {
    AA: {
      normal: 4.5,
      large: 3.0
    },
    AAA: {
      normal: 7.0,
      large: 4.5
    }
  };
  
  return ratio >= standards[level][size];
};

/**
 * Test glass morphism color combinations
 * @param {string} backgroundColor - Background color behind the glass
 * @param {string} glassColor - Glass surface color (with opacity)
 * @param {string} textColor - Text color on glass
 * @returns {object} Contrast test results
 */
export const testGlassContrast = (backgroundColor, glassColor, textColor) => {
  // For glass effects, we need to calculate the effective color
  // This is a simplified calculation - in reality, backdrop-filter affects this
  const backgroundRgb = hexToRgb(backgroundColor);
  const glassRgb = hexToRgb(glassColor);
  const textRgb = hexToRgb(textColor);
  
  if (!backgroundRgb || !glassRgb || !textRgb) {
    return { error: 'Invalid color format' };
  }
  
  // Simplified blend calculation (assuming 80% glass opacity)
  const blendedRgb = {
    r: Math.round(glassRgb.r * 0.8 + backgroundRgb.r * 0.2),
    g: Math.round(glassRgb.g * 0.8 + backgroundRgb.g * 0.2),
    b: Math.round(glassRgb.b * 0.8 + backgroundRgb.b * 0.2)
  };
  
  const blendedHex = `#${blendedRgb.r.toString(16).padStart(2, '0')}${blendedRgb.g.toString(16).padStart(2, '0')}${blendedRgb.b.toString(16).padStart(2, '0')}`;
  
  const contrastRatio = getContrastRatio(blendedHex, textColor);
  
  return {
    contrastRatio,
    blendedColor: blendedHex,
    meetsAA: meetsWCAG(contrastRatio, 'AA', 'normal'),
    meetsAAA: meetsWCAG(contrastRatio, 'AAA', 'normal'),
    meetsAALarge: meetsWCAG(contrastRatio, 'AA', 'large'),
    recommendation: contrastRatio < 4.5 ? 'Consider using darker text or lighter glass surface' : 'Contrast is acceptable'
  };
};

/**
 * Predefined color combinations for testing
 */
export const colorCombinations = {
  // Glass morphism combinations
  glassOnLight: {
    background: '#ffffff',
    glass: '#ffffff',
    text: '#1f2937'
  },
  glassOnDark: {
    background: '#0f172a',
    glass: '#0f172a',
    text: '#f9fafb'
  },
  glassOnPrimary: {
    background: '#2A9D8F',
    glass: '#ffffff',
    text: '#1f2937'
  },
  
  // Standard combinations
  primaryButton: {
    background: '#2A9D8F',
    text: '#ffffff'
  },
  errorState: {
    background: '#E56B6F',
    text: '#ffffff'
  },
  successState: {
    background: '#4AD295',
    text: '#ffffff'
  }
};

/**
 * Run comprehensive contrast tests
 * @returns {object} Test results for all combinations
 */
export const runContrastTests = () => {
  const results = {};
  
  // Test glass morphism combinations
  Object.entries(colorCombinations).forEach(([key, colors]) => {
    if (colors.glass) {
      results[key] = testGlassContrast(colors.background, colors.glass, colors.text);
    } else {
      const ratio = getContrastRatio(colors.background, colors.text);
      results[key] = {
        contrastRatio: ratio,
        meetsAA: meetsWCAG(ratio, 'AA', 'normal'),
        meetsAAA: meetsWCAG(ratio, 'AAA', 'normal'),
        meetsAALarge: meetsWCAG(ratio, 'AA', 'large')
      };
    }
  });
  
  return results;
};

/**
 * Generate accessible color suggestions
 * @param {string} baseColor - Base color to generate suggestions for
 * @returns {array} Array of accessible color combinations
 */
export const generateAccessibleColors = (baseColor) => {
  const suggestions = [];
  const baseRgb = hexToRgb(baseColor);
  
  if (!baseRgb) return suggestions;
  
  // Generate lighter and darker variants
  for (let i = 0; i <= 10; i++) {
    const factor = i / 10;
    
    // Lighter variant
    const lighterRgb = {
      r: Math.round(baseRgb.r + (255 - baseRgb.r) * factor),
      g: Math.round(baseRgb.g + (255 - baseRgb.g) * factor),
      b: Math.round(baseRgb.b + (255 - baseRgb.b) * factor)
    };
    
    // Darker variant
    const darkerRgb = {
      r: Math.round(baseRgb.r * (1 - factor)),
      g: Math.round(baseRgb.g * (1 - factor)),
      b: Math.round(baseRgb.b * (1 - factor))
    };
    
    const lighterHex = `#${lighterRgb.r.toString(16).padStart(2, '0')}${lighterRgb.g.toString(16).padStart(2, '0')}${lighterRgb.b.toString(16).padStart(2, '0')}`;
    const darkerHex = `#${darkerRgb.r.toString(16).padStart(2, '0')}${darkerRgb.g.toString(16).padStart(2, '0')}${darkerRgb.b.toString(16).padStart(2, '0')}`;
    
    // Test contrast with white and black text
    const lighterWithBlack = getContrastRatio(lighterHex, '#000000');
    const lighterWithWhite = getContrastRatio(lighterHex, '#ffffff');
    const darkerWithBlack = getContrastRatio(darkerHex, '#000000');
    const darkerWithWhite = getContrastRatio(darkerHex, '#ffffff');
    
    if (meetsWCAG(lighterWithBlack, 'AA')) {
      suggestions.push({ background: lighterHex, text: '#000000', ratio: lighterWithBlack });
    }
    if (meetsWCAG(lighterWithWhite, 'AA')) {
      suggestions.push({ background: lighterHex, text: '#ffffff', ratio: lighterWithWhite });
    }
    if (meetsWCAG(darkerWithBlack, 'AA')) {
      suggestions.push({ background: darkerHex, text: '#000000', ratio: darkerWithBlack });
    }
    if (meetsWCAG(darkerWithWhite, 'AA')) {
      suggestions.push({ background: darkerHex, text: '#ffffff', ratio: darkerWithWhite });
    }
  }
  
  // Sort by contrast ratio (highest first)
  return suggestions.sort((a, b) => b.ratio - a.ratio);
};
