import React from 'react';
import { useMotion, MOTION_PREFERENCES } from '../contexts/MotionContext';

const MotionToggle = () => {
  const { motionPreference, toggleMotionPreference } = useMotion();
  const isReducedMotion = motionPreference === MOTION_PREFERENCES.REDUCED;
  
  return (
    <div className="flex items-center space-x-3">
      <span className="text-xs font-medium text-gray-300">Reduce Motion</span>
      <button
        onClick={toggleMotionPreference}
        className={`relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-purple-500 ${
          isReducedMotion ? 'bg-purple-600' : 'bg-gray-600'
        }`}
        aria-label="Toggle Motion Preference"
        aria-pressed={isReducedMotion}
      >
        <span
          className={`inline-block w-4 h-4 transform bg-white rounded-full transition-transform shadow-lg ${
            isReducedMotion ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );
};

export default MotionToggle;