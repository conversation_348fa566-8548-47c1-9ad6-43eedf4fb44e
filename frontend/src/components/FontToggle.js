import React from 'react';
import { useFontPreference, FONT_PREFERENCES } from '../contexts/FontContext';

const FontToggle = () => {
  const { fontPreference, toggleFontPreference } = useFontPreference();
  const isDyslexicFont = fontPreference === FONT_PREFERENCES.DYSLEXIC;
  
  return (
    <div className="flex items-center space-x-3">
      <span className="text-xs font-medium text-gray-300">Dyslexia-Friendly</span>
      <button
        onClick={toggleFontPreference}
        className={`relative inline-flex items-center h-6 rounded-full w-11 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-indigo-500 ${
          isDyslexicFont ? 'bg-indigo-600' : 'bg-gray-600'
        }`}
        aria-label="Toggle Dyslexia-Friendly Font"
        aria-pressed={isDyslexicFont}
      >
        <span
          className={`inline-block w-4 h-4 transform bg-white rounded-full transition-transform shadow-lg ${
            isDyslexicFont ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );
};

export default FontToggle;