import React from 'react';
import PropTypes from 'prop-types';

/**
 * Enhanced Card component with modern glass morphism and hover effects
 */
const Card = ({
  children,
  variant = 'default',
  padding = 'medium',
  elevation = 'medium',
  className = '',
  hover = false,
  interactive = false,
  onClick,
  onKeyDown,
  role,
  tabIndex,
  'aria-label': ariaLabel,
  ...props
}) => {
  // Handle keyboard interaction for interactive cards
  const handleKeyDown = (event) => {
    if (interactive && onClick && (event.key === 'Enter' || event.key === ' ')) {
      event.preventDefault();
      onClick(event);
    }
    if (onKeyDown) {
      onKeyDown(event);
    }
  };

  // Base classes for all cards with enhanced animations and focus management
  const baseClasses = `
    rounded-xl overflow-hidden transition-all duration-300 ease-out
    ${hover || interactive ? 'hover:-translate-y-1 hover:shadow-floating' : ''}
    ${interactive ? 'cursor-pointer transform active:scale-95 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus-visible:ring-4' : ''}
  `.trim().replace(/\s+/g, ' ');

  // Padding variations aligned with 8pt spacing system
  const paddingClasses = {
    none: '',
    small: 'p-4',    // 16px
    medium: 'p-6',   // 24px
    large: 'p-8'     // 32px
  };

  // Enhanced elevation (shadow) variations
  const elevationClasses = {
    none: '',
    low: 'shadow-soft',
    medium: 'shadow-elevated',
    high: 'shadow-floating',
    glow: 'shadow-glow'
  };

  // Enhanced variant styles using semantic design tokens
  const variantClasses = {
    // Default card with semantic surface colors
    default: `
      surface-primary border border-default
      hover:border-medium hover:shadow-md
    `.trim().replace(/\s+/g, ' '),

    // Elevated card with enhanced shadow
    elevated: `
      surface-elevated border border-default shadow-lg
      hover:shadow-xl hover:border-medium
    `.trim().replace(/\s+/g, ' '),

    // Interactive card with hover effects
    interactive: `
      surface-primary border border-default
      hover:surface-secondary hover:border-medium hover:shadow-md
    `.trim().replace(/\s+/g, ' '),

    // Glassmorphism card with proper contrast
    glass: `
      glass-surface backdrop-blur-md border
      hover:glass-surface:hover shadow-lg
    `.trim().replace(/\s+/g, ' '),
  };

  // Combine all classes
  const cardClasses = [
    baseClasses,
    paddingClasses[padding],
    elevationClasses[elevation],
    variantClasses[variant],
    className
  ].filter(Boolean).join(' ');

  // Determine the appropriate element and props for interactive cards
  const Element = interactive && onClick ? 'button' : 'div';
  const elementProps = {
    className: cardClasses,
    ...(interactive && onClick && {
      onClick,
      onKeyDown: handleKeyDown,
      role: role || 'button',
      tabIndex: tabIndex !== undefined ? tabIndex : 0,
      'aria-label': ariaLabel,
    }),
    ...props
  };

  return (
    <Element {...elementProps}>
      {children}
    </Element>
  );
};

Card.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf([
    'default',
    'elevated',
    'interactive',
    'glass'
  ]),
  padding: PropTypes.oneOf(['none', 'small', 'medium', 'large']),
  elevation: PropTypes.oneOf(['none', 'low', 'medium', 'high', 'glow']),
  className: PropTypes.string,
  hover: PropTypes.bool,
  interactive: PropTypes.bool,
  onClick: PropTypes.func,
  onKeyDown: PropTypes.func,
  role: PropTypes.string,
  tabIndex: PropTypes.number,
  'aria-label': PropTypes.string,
};

/**
 * CardHeader component for consistent card headers
 */
export const CardHeader = ({
  title,
  subtitle,
  action,
  className = '',
  ...props
}) => {
  return (
    <div className={`mb-4 ${className}`} {...props}>
      <div className="flex justify-between items-start">
        <div>
          {title && (
            <h3 className="text-xl font-bold">
              {title}
            </h3>
          )}
          {subtitle && (
            <p className="text-text-secondary mt-1">
              {subtitle}
            </p>
          )}
        </div>
        {action && (
          <div className="ml-4">
            {action}
          </div>
        )}
      </div>
    </div>
  );
};

CardHeader.propTypes = {
  title: PropTypes.node,
  subtitle: PropTypes.node,
  action: PropTypes.node,
  className: PropTypes.string,
};

/**
 * CardContent component for consistent card content
 */
export const CardContent = ({
  children,
  className = '',
  ...props
}) => {
  return (
    <div className={className} {...props}>
      {children}
    </div>
  );
};

CardContent.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
};

/**
 * CardFooter component for consistent card footers
 */
export const CardFooter = ({
  children,
  className = '',
  ...props
}) => {
  return (
    <div className={`mt-4 pt-4 border-t border-border-light ${className}`} {...props}>
      {children}
    </div>
  );
};

CardFooter.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
};

/**
 * Container component for section containers
 */
export const Container = ({
  children,
  variant = 'default',
  className = '',
  ...props
}) => {
  // Base classes for all containers
  const baseClasses = 'w-full mx-auto px-4 sm:px-6 lg:px-8';

  // Variant styles
  const variantClasses = {
    default: 'max-w-7xl',
    narrow: 'max-w-3xl',
    wide: 'max-w-screen-2xl',
    full: 'max-w-none',
  };

  // Combine all classes
  const containerClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${className}
  `;

  return (
    <div className={containerClasses} {...props}>
      {children}
    </div>
  );
};

Container.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf(['default', 'narrow', 'wide', 'full']),
  className: PropTypes.string,
};

/**
 * Section component for page sections
 */
export const Section = ({
  children,
  variant = 'default',
  className = '',
  ...props
}) => {
  // Base classes for all sections - aligned with 8pt spacing
  const baseClasses = 'py-8 sm:py-12 md:py-16'; // 32px → 48px → 64px

  // Variant styles using the new color palette
  const variantClasses = {
    default: 'bg-white',
    subtle: 'bg-background-subtle',
    primary: 'bg-primary/10',
    coral: 'bg-secondary-coral/10',
    lavender: 'bg-secondary-lavender/10',
    sage: 'bg-secondary-sage/10',
  };

  // Combine all classes
  const sectionClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${className}
  `;

  return (
    <section className={sectionClasses} {...props}>
      {children}
    </section>
  );
};

Section.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf(['default', 'subtle', 'primary', 'coral', 'lavender', 'sage']),
  className: PropTypes.string,
};

/**
 * Modal component for dialogs
 */
export const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'medium',
  className = '',
  ...props
}) => {
  if (!isOpen) return null;

  // Size variations
  const sizeClasses = {
    small: 'max-w-md',
    medium: 'max-w-lg',
    large: 'max-w-2xl',
    xlarge: 'max-w-4xl',
  };

  return (
    <div
      className="fixed inset-0 z-50 overflow-y-auto"
      aria-labelledby={title ? 'modal-title' : undefined}
      role="dialog"
      aria-modal="true"
      {...props}
    >
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        aria-hidden="true"
        onClick={onClose}
      />

      {/* Modal container */}
      <div className="flex min-h-screen items-center justify-center p-4 text-center sm:p-0">
        <div
          className={`
            relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl
            transition-all sm:my-8 w-full ${sizeClasses[size]} ${className}
          `}
        >
          {/* Close button - aligned with 8pt spacing */}
          <button
            type="button"
            className="absolute top-4 right-4 text-text-secondary hover:text-text-primary"
            onClick={onClose}
            aria-label="Close"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Modal content - aligned with 8pt spacing */}
          <div className="p-6">
            {title && (
              <h3 className="text-xl font-bold mb-4" id="modal-title">
                {title}
              </h3>
            )}
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

Modal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string,
  children: PropTypes.node.isRequired,
  size: PropTypes.oneOf(['small', 'medium', 'large', 'xlarge']),
  className: PropTypes.string,
};

export default Card;