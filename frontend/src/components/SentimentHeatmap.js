import React from 'react';
import { motion } from 'framer-motion';

const SentimentHeatmap = ({ data, onCellClick }) => {
  // Process data into a heatmap format
  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const weeks = 12; // Show last 12 weeks
  
  // Generate mock data if none provided
  const generateHeatmapData = () => {
    const heatmapData = [];
    const today = new Date();
    
    for (let week = weeks - 1; week >= 0; week--) {
      const weekData = [];
      for (let day = 0; day < 7; day++) {
        const date = new Date(today);
        date.setDate(date.getDate() - (week * 7) - (6 - day));
        
        const dataPoint = data?.find(d => {
          const dDate = new Date(d.date);
          return dDate.toDateString() === date.toDateString();
        });
        
        weekData.push({
          date: date,
          sentiment: dataPoint?.sentiment || null,
          value: dataPoint?.value || 0,
          flags: dataPoint?.flags || []
        });
      }
      heatmapData.push(weekData);
    }
    
    return heatmapData;
  };
  
  const heatmapData = generateHeatmapData();
  
  const getSentimentColor = (sentiment, value) => {
    if (!sentiment) return 'bg-surface';
    
    const intensity = Math.min(Math.max(value || 0.5, 0), 1);
    const opacity = intensity * 100;
    
    switch (sentiment) {
      case 'positive':
        return `bg-emotion-positive/${opacity}`;
      case 'negative':
        return `bg-emotion-negative/${opacity}`;
      case 'neutral':
        return `bg-primary/${opacity}`;
      default:
        return 'bg-surface';
    }
  };
  
  const getTooltipContent = (data) => {
    if (!data.sentiment) return 'No data';
    
    const dateStr = data.date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
    
    return `${dateStr}\nSentiment: ${data.sentiment}\nFlags: ${data.flags.length || 0}`;
  };
  
  return (
    <div className="sentiment-heatmap">
      <div className="flex items-start gap-4">
        {/* Day labels */}
        <div className="flex flex-col gap-1 mt-6">
          {days.map((day, index) => (
            <div key={day} className="h-6 flex items-center">
              <span className="text-xs text-text-secondary">{day}</span>
            </div>
          ))}
        </div>
        
        {/* Heatmap grid */}
        <div className="flex-1">
          {/* Week labels */}
          <div className="flex gap-1 mb-2">
            {heatmapData.map((_, weekIndex) => {
              const weeksAgo = weeks - weekIndex - 1;
              return (
                <div key={weekIndex} className="flex-1 text-center">
                  <span className="text-xs text-text-secondary">
                    {weeksAgo === 0 ? 'This week' : weeksAgo === 1 ? '1w ago' : `${weeksAgo}w ago`}
                  </span>
                </div>
              );
            })}
          </div>
          
          {/* Heatmap cells */}
          <div className="flex gap-1">
            {heatmapData.map((week, weekIndex) => (
              <div key={weekIndex} className="flex flex-col gap-1">
                {week.map((day, dayIndex) => (
                  <motion.button
                    key={`${weekIndex}-${dayIndex}`}
                    className={`w-6 h-6 rounded transition-all duration-300 ${
                      getSentimentColor(day.sentiment, day.value)
                    } ${day.sentiment ? 'hover:ring-2 hover:ring-primary cursor-pointer' : 'cursor-default'}`}
                    onClick={() => day.sentiment && onCellClick && onCellClick(day)}
                    whileHover={day.sentiment ? { scale: 1.2 } : {}}
                    whileTap={day.sentiment ? { scale: 0.95 } : {}}
                    title={getTooltipContent(day)}
                  >
                    {day.flags.length > 0 && (
                      <span className="text-xs font-bold text-white">!</span>
                    )}
                  </motion.button>
                ))}
              </div>
            ))}
          </div>
          
          {/* Legend */}
          <div className="flex items-center gap-4 mt-4">
            <span className="text-xs text-text-secondary">Less</span>
            <div className="flex gap-1">
              <div className="w-4 h-4 bg-surface rounded"></div>
              <div className="w-4 h-4 bg-primary/25 rounded"></div>
              <div className="w-4 h-4 bg-primary/50 rounded"></div>
              <div className="w-4 h-4 bg-primary/75 rounded"></div>
              <div className="w-4 h-4 bg-primary rounded"></div>
            </div>
            <span className="text-xs text-text-secondary">More</span>
            
            <div className="ml-auto flex items-center gap-2">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-emotion-positive rounded"></div>
                <span className="text-xs text-text-secondary">Positive</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-primary rounded"></div>
                <span className="text-xs text-text-secondary">Neutral</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-emotion-negative rounded"></div>
                <span className="text-xs text-text-secondary">Negative</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SentimentHeatmap;
