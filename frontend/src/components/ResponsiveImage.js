import React, { useState } from 'react';
import PropTypes from 'prop-types';

/**
 * Responsive Image component with srcset support for optimal loading
 */
const ResponsiveImage = ({
  src,
  srcSet,
  sizes,
  alt,
  aspectRatio = '16/9',
  className = '',
  loading = 'lazy',
  onLoad,
  onError,
  placeholder = null,
  ...props
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = (e) => {
    setIsLoading(false);
    if (onLoad) onLoad(e);
  };

  const handleError = (e) => {
    setIsLoading(false);
    setHasError(true);
    if (onError) onError(e);
  };

  // Generate srcSet if not provided but we have multiple sources
  const generateSrcSet = () => {
    if (srcSet) return srcSet;
    
    // If src contains size indicators, generate srcset
    const basePath = src.replace(/\.(jpg|jpeg|png|webp)$/i, '');
    const extension = src.match(/\.(jpg|jpeg|png|webp)$/i)?.[0] || '.jpg';
    
    return `
      ${basePath}-320w${extension} 320w,
      ${basePath}-640w${extension} 640w,
      ${basePath}-768w${extension} 768w,
      ${basePath}-1024w${extension} 1024w,
      ${basePath}-1280w${extension} 1280w,
      ${basePath}-1536w${extension} 1536w
    `.trim();
  };

  // Generate sizes attribute if not provided
  const generateSizes = () => {
    if (sizes) return sizes;
    
    // Default responsive sizes
    return `
      (max-width: 640px) 100vw,
      (max-width: 768px) 90vw,
      (max-width: 1024px) 80vw,
      (max-width: 1280px) 70vw,
      60vw
    `.trim();
  };

  return (
    <div 
      className={`responsive-image-wrapper ${className}`}
      style={{ '--aspect-ratio': aspectRatio }}
    >
      {/* Loading placeholder */}
      {isLoading && (
        <div className="absolute inset-0 image-loading">
          {placeholder || (
            <div className="flex items-center justify-center h-full">
              <div className="w-8 h-8 border-2 border-gray-300 border-t-primary-500 rounded-full animate-spin"></div>
            </div>
          )}
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
          <div className="text-center p-4">
            <svg className="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-sm text-gray-500">Failed to load image</p>
          </div>
        </div>
      )}

      {/* Actual image */}
      {!hasError && (
        <img
          src={src}
          srcSet={generateSrcSet()}
          sizes={generateSizes()}
          alt={alt}
          loading={loading}
          onLoad={handleLoad}
          onError={handleError}
          className={`responsive-image ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          {...props}
        />
      )}
    </div>
  );
};

ResponsiveImage.propTypes = {
  src: PropTypes.string.isRequired,
  srcSet: PropTypes.string,
  sizes: PropTypes.string,
  alt: PropTypes.string.isRequired,
  aspectRatio: PropTypes.string,
  className: PropTypes.string,
  loading: PropTypes.oneOf(['lazy', 'eager']),
  onLoad: PropTypes.func,
  onError: PropTypes.func,
  placeholder: PropTypes.node,
};

export default ResponsiveImage;
