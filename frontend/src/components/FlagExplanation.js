import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const flagDetails = {
  'Gaslighting': {
    definition: 'A form of psychological manipulation where someone makes you question your reality, memory, or perceptions.',
    examples: [
      '"That never happened"',
      '"You\'re being too sensitive"',
      '"You\'re imagining things"'
    ],
    impact: 'Can lead to self-doubt, confusion, and erosion of self-confidence.',
    response: 'Document incidents, trust your perceptions, and seek support from trusted friends or professionals.'
  },
  'Manipulation': {
    definition: 'Using deceptive or underhanded tactics to control or influence someone for personal gain.',
    examples: [
      'Guilt-tripping',
      'Playing victim',
      'Love bombing followed by withdrawal'
    ],
    impact: 'Creates unhealthy power dynamics and erodes trust in relationships.',
    response: 'Set clear boundaries, recognize manipulation tactics, and don\'t be afraid to say no.'
  },
  'Invalidation': {
    definition: 'Dismissing, minimizing, or denying someone\'s feelings, thoughts, or experiences.',
    examples: [
      '"You shouldn\'t feel that way"',
      '"It\'s not a big deal"',
      '"You\'re overreacting"'
    ],
    impact: 'Makes you feel unheard, unimportant, and can damage emotional well-being.',
    response: 'Validate your own feelings, communicate your needs clearly, and seek relationships that honor your experiences.'
  },
  'Blame-shifting': {
    definition: 'Refusing to take responsibility and instead placing blame on others, often the victim.',
    examples: [
      '"You made me do it"',
      '"If you hadn\'t..., I wouldn\'t have..."',
      '"This is your fault"'
    ],
    impact: 'Prevents healthy conflict resolution and can make you feel responsible for others\' actions.',
    response: 'Recognize that you\'re only responsible for your own actions, not others\' choices or behaviors.'
  },
  'Emotional Blackmail': {
    definition: 'Using fear, obligation, or guilt to manipulate someone into doing what you want.',
    examples: [
      '"If you loved me, you would..."',
      '"I\'ll hurt myself if you leave"',
      '"After all I\'ve done for you..."'
    ],
    impact: 'Creates a toxic cycle of compliance based on fear rather than genuine care.',
    response: 'Don\'t negotiate with emotional terrorists. Seek professional help if threats of self-harm are involved.'
  },
  'Stonewalling': {
    definition: 'Refusing to communicate or cooperate, giving the "silent treatment" to punish or control.',
    examples: [
      'Ignoring messages or calls',
      'Refusing to discuss issues',
      'Walking away mid-conversation'
    ],
    impact: 'Prevents healthy communication and problem-solving in relationships.',
    response: 'Give space initially, but address the pattern. If it continues, consider if the relationship is healthy.'
  }
};

const FlagExplanation = ({ flag, variant = 'inline' }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const details = flagDetails[flag.type] || flagDetails[flag];
  
  if (!details) {
    return <span className="text-emotion-negative">{flag.type || flag}</span>;
  }
  
  if (variant === 'inline') {
    return (
      <div className="inline-flex items-center gap-2">
        <span className="text-emotion-negative font-medium">{flag.type || flag}</span>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-primary hover:text-primary-dark transition-colors duration-200"
          aria-label="Toggle explanation"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </button>
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 10 }}
              className="absolute z-50 mt-2 w-80 bg-surface border border-surface-light rounded-lg shadow-xl p-4"
              style={{ top: '100%', left: '0' }}
            >
              <button
                onClick={() => setIsExpanded(false)}
                className="absolute top-2 right-2 text-text-secondary hover:text-text-primary"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              
              <h4 className="font-semibold text-emotion-negative mb-2">{flag.type || flag}</h4>
              <p className="text-sm text-text-primary mb-3">{details.definition}</p>
              
              <div className="space-y-3">
                <div>
                  <h5 className="text-xs font-medium text-text-secondary uppercase tracking-wider mb-1">Examples</h5>
                  <ul className="text-sm text-text-primary space-y-1">
                    {details.examples.map((example, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-emotion-negative mt-1">•</span>
                        <span className="italic">{example}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h5 className="text-xs font-medium text-text-secondary uppercase tracking-wider mb-1">Impact</h5>
                  <p className="text-sm text-text-primary">{details.impact}</p>
                </div>
                
                <div>
                  <h5 className="text-xs font-medium text-text-secondary uppercase tracking-wider mb-1">How to Respond</h5>
                  <p className="text-sm text-text-primary">{details.response}</p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }
  
  // Full variant for detailed view
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-emotion-negative/10 border border-emotion-negative/30 rounded-lg p-4"
    >
      <h3 className="font-semibold text-emotion-negative mb-2 flex items-center gap-2">
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
        {flag.type || flag}
      </h3>
      
      <p className="text-text-primary mb-3">{details.definition}</p>
      
      {flag.description && (
        <div className="mb-3 p-3 bg-surface/50 rounded-md">
          <h4 className="text-sm font-medium text-text-secondary mb-1">In this message:</h4>
          <p className="text-sm text-text-primary">{flag.description}</p>
        </div>
      )}
      
      <details className="group">
        <summary className="cursor-pointer text-sm font-medium text-primary hover:text-primary-dark transition-colors duration-200 flex items-center gap-2">
          Learn more
          <svg className="w-4 h-4 transform transition-transform group-open:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </summary>
        
        <div className="mt-3 space-y-3 text-sm">
          <div>
            <h5 className="font-medium text-text-secondary mb-1">Common Examples:</h5>
            <ul className="text-text-primary space-y-1 ml-4">
              {details.examples.map((example, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-emotion-negative">•</span>
                  <span className="italic">{example}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div>
            <h5 className="font-medium text-text-secondary mb-1">Potential Impact:</h5>
            <p className="text-text-primary">{details.impact}</p>
          </div>
          
          <div>
            <h5 className="font-medium text-text-secondary mb-1">Healthy Response:</h5>
            <p className="text-text-primary">{details.response}</p>
          </div>
        </div>
      </details>
    </motion.div>
  );
};

export default FlagExplanation;
