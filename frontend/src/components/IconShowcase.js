import React, { useState } from 'react';
import Icon from './modern/Icon';
import Card from './modern/Card';

/**
 * IconShowcase component to display available icons in the system
 * Helps developers find and use the right icons
 */
const IconShowcase = () => {
  const [selectedSize, setSelectedSize] = useState('md');
  const [searchTerm, setSearchTerm] = useState('');

  // Common icons used in the application
  const iconCategories = {
    'Navigation': [
      { name: 'Home', label: 'Home' },
      { name: 'ArrowLeft', label: 'Back' },
      { name: 'ArrowRight', label: 'Forward' },
      { name: 'ChevronLeft', label: 'Previous' },
      { name: 'ChevronRight', label: 'Next' },
      { name: 'Menu', label: 'Menu' },
      { name: 'X', label: 'Close' },
    ],
    'Dashboard & Analytics': [
      { name: 'Bar<PERSON>hart3', label: 'Bar Chart' },
      { name: '<PERSON><PERSON><PERSON>', label: 'Line Chart' },
      { name: '<PERSON><PERSON><PERSON>', label: 'Pie Chart' },
      { name: 'TrendingUp', label: 'Growth' },
      { name: 'TrendingDown', label: 'Decline' },
      { name: 'Activity', label: 'Activity' },
    ],
    'Communication': [
      { name: 'MessageSquare', label: 'Message' },
      { name: 'MessageCircle', label: 'Chat' },
      { name: 'Mail', label: 'Email' },
      { name: 'Send', label: 'Send' },
      { name: 'Bell', label: 'Notifications' },
      { name: 'BellOff', label: 'Muted' },
    ],
    'Emotions & Relationships': [
      { name: 'Heart', label: 'Love' },
      { name: 'HeartHandshake', label: 'Care' },
      { name: 'Users', label: 'People' },
      { name: 'UserPlus', label: 'Add Person' },
      { name: 'UserMinus', label: 'Remove Person' },
      { name: 'Smile', label: 'Happy' },
      { name: 'Frown', label: 'Sad' },
      { name: 'Meh', label: 'Neutral' },
    ],
    'Alerts & Status': [
      { name: 'AlertCircle', label: 'Alert' },
      { name: 'AlertTriangle', label: 'Warning' },
      { name: 'CheckCircle', label: 'Success' },
      { name: 'XCircle', label: 'Error' },
      { name: 'Info', label: 'Info' },
      { name: 'HelpCircle', label: 'Help' },
    ],
    'Actions': [
      { name: 'Plus', label: 'Add' },
      { name: 'Minus', label: 'Remove' },
      { name: 'Edit3', label: 'Edit' },
      { name: 'Trash2', label: 'Delete' },
      { name: 'Save', label: 'Save' },
      { name: 'Download', label: 'Download' },
      { name: 'Upload', label: 'Upload' },
      { name: 'RefreshCw', label: 'Refresh' },
      { name: 'Settings', label: 'Settings' },
      { name: 'Search', label: 'Search' },
      { name: 'Filter', label: 'Filter' },
      { name: 'Copy', label: 'Copy' },
    ],
    'Growth & Progress': [
      { name: 'Target', label: 'Goal' },
      { name: 'Award', label: 'Achievement' },
      { name: 'Star', label: 'Favorite' },
      { name: 'Zap', label: 'Energy' },
      { name: 'Sparkles', label: 'Magic' },
      { name: 'Brain', label: 'Mind' },
      { name: 'BookOpen', label: 'Learn' },
      { name: 'Lightbulb', label: 'Idea' },
    ],
  };

  // Filter icons based on search term
  const filteredCategories = Object.entries(iconCategories).reduce((acc, [category, icons]) => {
    const filteredIcons = icons.filter(icon => 
      icon.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      icon.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    if (filteredIcons.length > 0) {
      acc[category] = filteredIcons;
    }
    return acc;
  }, {});

  const sizes = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-3xl font-bold mb-4">Icon System</h2>
        <p className="text-gray-600 mb-6">
          We use Lucide icons throughout the application. Icons can be styled with size, color, and animation properties.
        </p>
      </div>

      {/* Controls */}
      <Card variant="glass" className="p-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Icons
            </label>
            <div className="relative">
              <Icon
                name="Search"
                size="sm"
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search icons..."
                className="pl-10 pr-4 py-2 w-full rounded-lg border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Icon Size
            </label>
            <div className="flex gap-2">
              {sizes.map((size) => (
                <button
                  key={size}
                  onClick={() => setSelectedSize(size)}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    selectedSize === size
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {size}
                </button>
              ))}
            </div>
          </div>
        </div>
      </Card>

      {/* Icon Categories */}
      {Object.entries(filteredCategories).map(([category, icons]) => (
        <Card key={category} variant="floating" className="p-6">
          <h3 className="text-xl font-semibold mb-4">{category}</h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {icons.map((icon) => (
              <div
                key={icon.name}
                className="flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Icon
                  name={icon.name}
                  size={selectedSize}
                  className="mb-2 text-gray-700"
                />
                <span className="text-xs text-gray-600 text-center">{icon.label}</span>
                <code className="text-xs text-gray-400 mt-1">{icon.name}</code>
              </div>
            ))}
          </div>
        </Card>
      ))}

      {/* Usage Examples */}
      <Card variant="glass" className="p-6">
        <h3 className="text-xl font-semibold mb-4">Usage Examples</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Basic Usage</h4>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto">
              <code>{`<Icon name="Home" size="md" />`}</code>
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">With Variants</h4>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto">
              <code>{`<Icon name="AlertCircle" size="lg" variant="danger" />
<Icon name="CheckCircle" size="lg" variant="success" />
<Icon name="Info" size="lg" variant="info" />`}</code>
            </pre>
            <div className="flex gap-4 mt-2">
              <Icon name="AlertCircle" size="lg" variant="danger" />
              <Icon name="CheckCircle" size="lg" variant="success" />
              <Icon name="Info" size="lg" variant="info" />
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">With Animation</h4>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto">
              <code>{`<Icon name="RefreshCw" size="lg" animate="spin" />
<Icon name="Heart" size="lg" animate="pulse" variant="danger" />`}</code>
            </pre>
            <div className="flex gap-4 mt-2">
              <Icon name="RefreshCw" size="lg" animate="spin" />
              <Icon name="Heart" size="lg" animate="pulse" variant="danger" />
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">Accessibility</h4>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto">
              <code>{`// Decorative icon (default)
<Icon name="Star" size="md" />

// Informative icon with label
<Icon name="AlertTriangle" size="md" decorative={false} label="Warning: Important message" />`}</code>
            </pre>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default IconShowcase;
