import React from 'react';
import PropTypes from 'prop-types';
import * as LucideIcons from 'lucide-react';

/**
 * Icon component wrapper for Lucide icons
 * Provides consistent sizing, theming, and accessibility features
 * 
 * @example
 * <Icon name="Home" size="md" className="text-primary" />
 * <Icon name="AlertCircle" size="lg" className="text-red-500" decorative={false} label="Warning" />
 */
const Icon = ({
  name,
  size = 'md',
  className = '',
  color,
  strokeWidth,
  decorative = true,
  label,
  ...props
}) => {
  // Get the icon component from Lucide
  const IconComponent = LucideIcons[name];

  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in Lucide icons`);
    return null;
  }

  // Size mappings
  const sizeMap = {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 32,
    '2xl': 40,
    '3xl': 48,
  };

  const iconSize = typeof size === 'number' ? size : sizeMap[size] || sizeMap.md;

  // Generate human-readable label from icon name if no label provided
  const generateLabel = (iconName) => {
    return iconName
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
      .trim();
  };

  // Enhanced accessibility props
  const a11yProps = decorative
    ? {
        'aria-hidden': true,
        role: 'presentation'
      }
    : {
        role: 'img',
        'aria-label': label || generateLabel(name),
        // Add title for additional context on hover
        title: label || generateLabel(name),
      };

  return (
    <IconComponent
      size={iconSize}
      className={className}
      color={color}
      strokeWidth={strokeWidth}
      {...a11yProps}
      {...props}
    />
  );
};

Icon.propTypes = {
  /** Name of the Lucide icon (e.g., 'Home', 'User', 'Settings') */
  name: PropTypes.string.isRequired,
  /** Predefined size or custom number */
  size: PropTypes.oneOfType([
    PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl']),
    PropTypes.number,
  ]),
  /** Additional CSS classes */
  className: PropTypes.string,
  /** Icon color (defaults to currentColor) */
  color: PropTypes.string,
  /** Stroke width for the icon */
  strokeWidth: PropTypes.number,
  /** Whether the icon is purely decorative (true) or informative (false) */
  decorative: PropTypes.bool,
  /** Accessibility label for informative icons */
  label: PropTypes.string,
};

export default Icon;
