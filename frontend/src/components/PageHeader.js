import React from 'react';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import Breadcrumb from './Breadcrumb';
import { useNavigation } from '../contexts/NavigationContext';
import Button from './Button';

const PageHeader = ({ 
  title, 
  description, 
  showBreadcrumbs = true, 
  showBackButton = false,
  actions = null,
  className = '',
  variant = 'default' // 'default', 'minimal', 'hero'
}) => {
  const { canGoBack, goBack, isNavigating } = useNavigation();

  const variantClasses = {
    default: 'bg-surface-primary border-b border-border-default',
    minimal: 'bg-transparent',
    hero: 'bg-gradient-to-r from-primary/5 to-secondary-lavender/5 border-b border-border-default'
  };

  const titleClasses = {
    default: 'heading-2 text-text-primary',
    minimal: 'heading-3 text-text-primary',
    hero: 'heading-1 text-text-primary'
  };

  const descriptionClasses = {
    default: 'body-base text-text-secondary mt-1',
    minimal: 'body-small text-text-secondary mt-0.5',
    hero: 'body-large text-text-secondary mt-2'
  };

  return (
    <header className={`${variantClasses[variant]} ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-4 sm:py-6">
          {/* Breadcrumbs */}
          {showBreadcrumbs && (
            <div className="mb-4">
              <Breadcrumb />
            </div>
          )}

          {/* Header content */}
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4 min-w-0 flex-1">
              {/* Back button */}
              {showBackButton && canGoBack && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={goBack}
                  disabled={isNavigating}
                  className="flex-shrink-0 mt-1"
                  aria-label="Go back to previous page"
                >
                  <ArrowLeftIcon className="w-4 h-4" />
                </Button>
              )}

              {/* Title and description */}
              <div className="min-w-0 flex-1">
                {title && (
                  <h1 className={titleClasses[variant]}>
                    {title}
                  </h1>
                )}
                {description && (
                  <p className={descriptionClasses[variant]}>
                    {description}
                  </p>
                )}
              </div>
            </div>

            {/* Actions */}
            {actions && (
              <div className="flex items-center space-x-3 flex-shrink-0 ml-4">
                {actions}
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

// Preset variants for common page types
export const DashboardHeader = (props) => (
  <PageHeader variant="hero" showBreadcrumbs={true} {...props} />
);

export const FormHeader = (props) => (
  <PageHeader variant="default" showBreadcrumbs={true} showBackButton={true} {...props} />
);

export const ModalHeader = (props) => (
  <PageHeader variant="minimal" showBreadcrumbs={false} {...props} />
);

export default PageHeader;
