import React from 'react';
import { render, screen } from '../../__tests__/utils/testUtils';
import { axe } from 'jest-axe';
import { Card, CardHeader, CardContent, CardFooter, Container, Section, Modal } from '../Card';

describe('Card Components', () => {
  describe('Card', () => {
    it('renders card with default props', () => {
      render(<Card>Card content</Card>);
      const card = screen.getByText('Card content');
      expect(card).toBeInTheDocument();
      expect(card).toHaveClass('bg-white', 'rounded-lg', 'shadow-md');
    });

    it('applies custom className', () => {
      render(<Card className="custom-class">Content</Card>);
      const card = screen.getByText('Content').closest('.custom-class');
      expect(card).toBeInTheDocument();
    });

    it('renders different variants correctly', () => {
      const variants = ['default', 'outlined', 'elevated', 'flat'];
      
      variants.forEach(variant => {
        const { rerender } = render(<Card variant={variant}>Content</Card>);
        const card = screen.getByText('Content').parentElement;
        
        switch (variant) {
          case 'outlined':
            expect(card).toHaveClass('border', 'border-gray-200');
            break;
          case 'elevated':
            expect(card).toHaveClass('shadow-lg');
            break;
          case 'flat':
            expect(card).toHaveClass('shadow-none');
            break;
          default:
            expect(card).toHaveClass('shadow-md');
        }
        
        rerender(<div />); // Clear for next iteration
      });
    });

    it('applies different padding classes', () => {
      const { rerender } = render(<Card padding="sm">Content</Card>);
      let card = screen.getByText('Content').parentElement;
      expect(card).toHaveClass('p-4');
      
      rerender(<Card padding="md">Content</Card>);
      card = screen.getByText('Content').parentElement;
      expect(card).toHaveClass('p-6');
      
      rerender(<Card padding="lg">Content</Card>);
      card = screen.getByText('Content').parentElement;
      expect(card).toHaveClass('p-8');
    });

    it('applies different elevation classes', () => {
      const { rerender } = render(<Card elevation="sm">Content</Card>);
      let card = screen.getByText('Content').parentElement;
      expect(card).toHaveClass('shadow-sm');
      
      rerender(<Card elevation="md">Content</Card>);
      card = screen.getByText('Content').parentElement;
      expect(card).toHaveClass('shadow-md');
      
      rerender(<Card elevation="lg">Content</Card>);
      card = screen.getByText('Content').parentElement;
      expect(card).toHaveClass('shadow-lg');
    });

    it('handles hover effects', () => {
      render(<Card hover>Content</Card>);
      const card = screen.getByText('Content').parentElement;
      expect(card).toHaveClass('hover:shadow-lg', 'transition-shadow', 'duration-200');
    });

    it('should not have accessibility violations', async () => {
      const { container } = render(<Card>Accessible card content</Card>);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('CardHeader', () => {
    it('renders header with title', () => {
      render(<CardHeader title="Card Title" />);
      expect(screen.getByText('Card Title')).toBeInTheDocument();
      expect(screen.getByText('Card Title')).toHaveClass('text-lg', 'font-semibold');
    });

    it('renders with subtitle', () => {
      render(<CardHeader title="Title" subtitle="Subtitle" />);
      expect(screen.getByText('Title')).toBeInTheDocument();
      expect(screen.getByText('Subtitle')).toBeInTheDocument();
      expect(screen.getByText('Subtitle')).toHaveClass('text-sm', 'text-gray-600');
    });

    it('renders with actions', () => {
      const actions = <button>Action</button>;
      render(<CardHeader title="Title" actions={actions} />);
      expect(screen.getByRole('button', { name: 'Action' })).toBeInTheDocument();
    });

    it('applies custom className', () => {
      render(<CardHeader title="Title" className="custom-header" />);
      const header = screen.getByText('Title').closest('.custom-header');
      expect(header).toBeInTheDocument();
    });
  });

  describe('CardContent', () => {
    it('renders content with children', () => {
      render(<CardContent><p>Content paragraph</p></CardContent>);
      expect(screen.getByText('Content paragraph')).toBeInTheDocument();
    });

    it('applies custom className', () => {
      render(<CardContent className="custom-content">Content</CardContent>);
      const content = screen.getByText('Content').closest('.custom-content');
      expect(content).toBeInTheDocument();
    });
  });

  describe('CardFooter', () => {
    it('renders footer with children', () => {
      render(<CardFooter><button>Footer Button</button></CardFooter>);
      expect(screen.getByRole('button', { name: 'Footer Button' })).toBeInTheDocument();
    });

    it('applies custom className', () => {
      render(<CardFooter className="custom-footer">Footer</CardFooter>);
      const footer = screen.getByText('Footer').closest('.custom-footer');
      expect(footer).toBeInTheDocument();
    });
  });

  describe('Container', () => {
    it('renders with default props', () => {
      render(<Container>Container content</Container>);
      const container = screen.getByText('Container content');
      expect(container).toBeInTheDocument();
      expect(container).toHaveClass('container', 'mx-auto', 'px-4');
    });

    it('applies size variants correctly', () => {
      const { rerender } = render(<Container size="sm">Content</Container>);
      let container = screen.getByText('Content').parentElement;
      expect(container).toHaveClass('max-w-sm');
      
      rerender(<Container size="md">Content</Container>);
      container = screen.getByText('Content').parentElement;
      expect(container).toHaveClass('max-w-md');
      
      rerender(<Container size="lg">Content</Container>);
      container = screen.getByText('Content').parentElement;
      expect(container).toHaveClass('max-w-4xl');
    });

    it('handles fluid containers', () => {
      render(<Container fluid>Content</Container>);
      const container = screen.getByText('Content').parentElement;
      expect(container).toHaveClass('w-full');
      expect(container).not.toHaveClass('container');
    });
  });

  describe('Section', () => {
    it('renders with default props', () => {
      render(<Section>Section content</Section>);
      const section = screen.getByText('Section content');
      expect(section).toBeInTheDocument();
      expect(section).toHaveClass('py-8');
    });

    it('applies spacing variants correctly', () => {
      const { rerender } = render(<Section spacing="sm">Content</Section>);
      let section = screen.getByText('Content').parentElement;
      expect(section).toHaveClass('py-4');
      
      rerender(<Section spacing="md">Content</Section>);
      section = screen.getByText('Content').parentElement;
      expect(section).toHaveClass('py-8');
      
      rerender(<Section spacing="lg">Content</Section>);
      section = screen.getByText('Content').parentElement;
      expect(section).toHaveClass('py-16');
    });

    it('applies background variants correctly', () => {
      const variants = ['transparent', 'white', 'gray'];
      
      variants.forEach(variant => {
        const { rerender } = render(<Section background={variant}>Content</Section>);
        const section = screen.getByText('Content').parentElement;
        
        switch (variant) {
          case 'white':
            expect(section).toHaveClass('bg-white');
            break;
          case 'gray':
            expect(section).toHaveClass('bg-gray-50');
            break;
          default:
            expect(section).toHaveClass('bg-transparent');
        }
        
        rerender(<div />);
      });
    });
  });

  describe('Modal', () => {
    it('renders when open', () => {
      render(<Modal isOpen title="Modal Title">Modal content</Modal>);
      expect(screen.getByText('Modal Title')).toBeInTheDocument();
      expect(screen.getByText('Modal content')).toBeInTheDocument();
    });

    it('does not render when closed', () => {
      render(<Modal isOpen={false} title="Modal Title">Modal content</Modal>);
      expect(screen.queryByText('Modal Title')).not.toBeInTheDocument();
      expect(screen.queryByText('Modal content')).not.toBeInTheDocument();
    });

    it('calls onClose when backdrop is clicked', async () => {
      const handleClose = jest.fn();
      render(<Modal isOpen onClose={handleClose} title="Modal">Content</Modal>);
      
      const backdrop = screen.getByTestId('modal-backdrop');
      await userEvent.click(backdrop);
      
      expect(handleClose).toHaveBeenCalled();
    });

    it('calls onClose when close button is clicked', async () => {
      const handleClose = jest.fn();
      render(<Modal isOpen onClose={handleClose} title="Modal">Content</Modal>);
      
      const closeButton = screen.getByLabelText('Close modal');
      await userEvent.click(closeButton);
      
      expect(handleClose).toHaveBeenCalled();
    });

    it('applies different size classes', () => {
      const { rerender } = render(<Modal isOpen size="sm" title="Modal">Content</Modal>);
      let modal = screen.getByRole('dialog');
      expect(modal).toHaveClass('max-w-md');
      
      rerender(<Modal isOpen size="md" title="Modal">Content</Modal>);
      modal = screen.getByRole('dialog');
      expect(modal).toHaveClass('max-w-lg');
      
      rerender(<Modal isOpen size="lg" title="Modal">Content</Modal>);
      modal = screen.getByRole('dialog');
      expect(modal).toHaveClass('max-w-2xl');
    });

    it('prevents body scroll when open', () => {
      render(<Modal isOpen title="Modal">Content</Modal>);
      expect(document.body.style.overflow).toBe('hidden');
    });

    it('restores body scroll when closed', () => {
      const { rerender } = render(<Modal isOpen title="Modal">Content</Modal>);
      expect(document.body.style.overflow).toBe('hidden');
      
      rerender(<Modal isOpen={false} title="Modal">Content</Modal>);
      expect(document.body.style.overflow).toBe('');
    });

    it('has proper ARIA attributes', () => {
      render(<Modal isOpen title="Modal Title">Content</Modal>);
      const modal = screen.getByRole('dialog');
      expect(modal).toHaveAttribute('aria-modal', 'true');
      expect(modal).toHaveAttribute('aria-labelledby');
    });

    it('should not have accessibility violations', async () => {
      const { container } = render(<Modal isOpen title="Accessible Modal">Content</Modal>);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Integration Tests', () => {
    it('renders complete card structure correctly', () => {
      render(
        <Card>
          <CardHeader title="Test Card" subtitle="Test subtitle" />
          <CardContent>
            <p>This is the card content</p>
          </CardContent>
          <CardFooter>
            <button>Footer Action</button>
          </CardFooter>
        </Card>
      );
      
      expect(screen.getByText('Test Card')).toBeInTheDocument();
      expect(screen.getByText('Test subtitle')).toBeInTheDocument();
      expect(screen.getByText('This is the card content')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Footer Action' })).toBeInTheDocument();
    });

    it('renders nested containers correctly', () => {
      render(
        <Container>
          <Section>
            <Card>
              <CardContent>Nested content</CardContent>
            </Card>
          </Section>
        </Container>
      );
      
      expect(screen.getByText('Nested content')).toBeInTheDocument();
    });
  });

  describe('Visual Regression', () => {
    it('matches snapshot for card variants', () => {
      const variants = ['default', 'outlined', 'elevated', 'flat'];
      
      variants.forEach(variant => {
        const { container } = render(<Card variant={variant}>Card content</Card>);
        expect(container.firstChild).toMatchSnapshot(`card-${variant}`);
      });
    });

    it('matches snapshot for modal sizes', () => {
      const sizes = ['sm', 'md', 'lg'];
      
      sizes.forEach(size => {
        const { container } = render(<Modal isOpen size={size} title="Modal">Content</Modal>);
        expect(container.firstChild).toMatchSnapshot(`modal-${size}`);
      });
    });
  });
});