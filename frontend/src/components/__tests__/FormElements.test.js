import React from 'react';
import { render, screen, fireEvent, waitFor } from '../../__tests__/utils/testUtils';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { Input, Select, Checkbox, Radio, Textarea } from '../FormElements';

describe('Form Elements', () => {
  const user = userEvent.setup();

  describe('Input Component', () => {
    it('renders input with label', () => {
      render(<Input label="Test Input" />);
      expect(screen.getByLabelText('Test Input')).toBeInTheDocument();
      expect(screen.getByText('Test Input')).toBeInTheDocument();
    });

    it('renders with placeholder', () => {
      render(<Input placeholder="Enter text here" />);
      expect(screen.getByPlaceholderText('Enter text here')).toBeInTheDocument();
    });

    it('handles value changes', async () => {
      const handleChange = jest.fn();
      render(<Input label="Test Input" onChange={handleChange} />);
      
      const input = screen.getByLabelText('Test Input');
      await user.type(input, 'test value');
      
      expect(handleChange).toHaveBeenCalled();
      expect(input.value).toBe('test value');
    });

    it('shows error state correctly', () => {
      render(<Input label="Test Input" error="This field is required" />);
      
      const input = screen.getByLabelText('Test Input');
      const errorMessage = screen.getByText('This field is required');
      
      expect(input).toHaveClass('border-red-500');
      expect(errorMessage).toBeInTheDocument();
      expect(errorMessage).toHaveClass('text-red-600');
    });

    it('shows helper text', () => {
      render(<Input label="Test Input" helperText="Enter your full name" />);
      expect(screen.getByText('Enter your full name')).toBeInTheDocument();
    });

    it('handles disabled state', () => {
      render(<Input label="Test Input" disabled />);
      const input = screen.getByLabelText('Test Input');
      expect(input).toBeDisabled();
      expect(input).toHaveClass('bg-gray-100', 'cursor-not-allowed');
    });

    it('handles required state', () => {
      render(<Input label="Test Input" required />);
      const input = screen.getByLabelText('Test Input *');
      expect(input).toBeRequired();
    });

    it('applies different variants correctly', () => {
      const { rerender } = render(<Input label="Test" variant="standard" />);
      let input = screen.getByLabelText('Test');
      expect(input).toHaveClass('border-gray-300');
      
      rerender(<Input label="Test" variant="filled" />);
      input = screen.getByLabelText('Test');
      expect(input).toHaveClass('bg-gray-50');
      
      rerender(<Input label="Test" variant="outlined" />);
      input = screen.getByLabelText('Test');
      expect(input).toHaveClass('border-2');
    });

    it('handles focus and blur events', async () => {
      const handleFocus = jest.fn();
      const handleBlur = jest.fn();
      render(<Input label="Test Input" onFocus={handleFocus} onBlur={handleBlur} />);
      
      const input = screen.getByLabelText('Test Input');
      
      await user.click(input);
      expect(handleFocus).toHaveBeenCalled();
      
      await user.tab();
      expect(handleBlur).toHaveBeenCalled();
    });

    it('should not have accessibility violations', async () => {
      const { container } = render(<Input label="Accessible Input" />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Select Component', () => {
    const options = [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
      { value: 'option3', label: 'Option 3' }
    ];

    it('renders select with label and options', () => {
      render(<Select label="Test Select" options={options} />);
      
      expect(screen.getByLabelText('Test Select')).toBeInTheDocument();
      expect(screen.getByText('Option 1')).toBeInTheDocument();
      expect(screen.getByText('Option 2')).toBeInTheDocument();
      expect(screen.getByText('Option 3')).toBeInTheDocument();
    });

    it('handles value changes', async () => {
      const handleChange = jest.fn();
      render(<Select label="Test Select" options={options} onChange={handleChange} />);
      
      const select = screen.getByLabelText('Test Select');
      await user.selectOptions(select, 'option2');
      
      expect(handleChange).toHaveBeenCalled();
      expect(select.value).toBe('option2');
    });

    it('shows placeholder option', () => {
      render(<Select label="Test Select" options={options} placeholder="Choose an option" />);
      expect(screen.getByText('Choose an option')).toBeInTheDocument();
    });

    it('handles disabled state', () => {
      render(<Select label="Test Select" options={options} disabled />);
      const select = screen.getByLabelText('Test Select');
      expect(select).toBeDisabled();
    });

    it('shows error state correctly', () => {
      render(<Select label="Test Select" options={options} error="Please select an option" />);
      
      const select = screen.getByLabelText('Test Select');
      const errorMessage = screen.getByText('Please select an option');
      
      expect(select).toHaveClass('border-red-500');
      expect(errorMessage).toBeInTheDocument();
    });

    it('should not have accessibility violations', async () => {
      const { container } = render(<Select label="Accessible Select" options={options} />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Checkbox Component', () => {
    it('renders checkbox with label', () => {
      render(<Checkbox label="Test Checkbox" />);
      expect(screen.getByLabelText('Test Checkbox')).toBeInTheDocument();
      expect(screen.getByText('Test Checkbox')).toBeInTheDocument();
    });

    it('handles checked state changes', async () => {
      const handleChange = jest.fn();
      render(<Checkbox label="Test Checkbox" onChange={handleChange} />);
      
      const checkbox = screen.getByLabelText('Test Checkbox');
      await user.click(checkbox);
      
      expect(handleChange).toHaveBeenCalled();
      expect(checkbox).toBeChecked();
    });

    it('renders in checked state', () => {
      render(<Checkbox label="Test Checkbox" checked />);
      const checkbox = screen.getByLabelText('Test Checkbox');
      expect(checkbox).toBeChecked();
    });

    it('handles disabled state', () => {
      render(<Checkbox label="Test Checkbox" disabled />);
      const checkbox = screen.getByLabelText('Test Checkbox');
      expect(checkbox).toBeDisabled();
    });

    it('shows error state correctly', () => {
      render(<Checkbox label="Test Checkbox" error="This field is required" />);
      
      const checkbox = screen.getByLabelText('Test Checkbox');
      const errorMessage = screen.getByText('This field is required');
      
      expect(checkbox.parentElement).toHaveClass('border-red-500');
      expect(errorMessage).toBeInTheDocument();
    });

    it('applies different variants correctly', () => {
      const { rerender } = render(<Checkbox label="Test" variant="standard" />);
      let container = screen.getByLabelText('Test').parentElement;
      expect(container).toHaveClass('border-gray-300');
      
      rerender(<Checkbox label="Test" variant="filled" />);
      container = screen.getByLabelText('Test').parentElement;
      expect(container).toHaveClass('bg-gray-50');
    });

    it('should not have accessibility violations', async () => {
      const { container } = render(<Checkbox label="Accessible Checkbox" />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Radio Component', () => {
    it('renders radio with label', () => {
      render(<Radio label="Test Radio" name="test-group" value="test" />);
      expect(screen.getByLabelText('Test Radio')).toBeInTheDocument();
      expect(screen.getByText('Test Radio')).toBeInTheDocument();
    });

    it('handles selection changes', async () => {
      const handleChange = jest.fn();
      render(
        <div>
          <Radio label="Option 1" name="test-group" value="option1" onChange={handleChange} />
          <Radio label="Option 2" name="test-group" value="option2" onChange={handleChange} />
        </div>
      );
      
      const radio1 = screen.getByLabelText('Option 1');
      const radio2 = screen.getByLabelText('Option 2');
      
      await user.click(radio1);
      expect(handleChange).toHaveBeenCalled();
      expect(radio1).toBeChecked();
      expect(radio2).not.toBeChecked();
      
      await user.click(radio2);
      expect(radio1).not.toBeChecked();
      expect(radio2).toBeChecked();
    });

    it('renders in checked state', () => {
      render(<Radio label="Test Radio" name="test-group" value="test" checked />);
      const radio = screen.getByLabelText('Test Radio');
      expect(radio).toBeChecked();
    });

    it('handles disabled state', () => {
      render(<Radio label="Test Radio" name="test-group" value="test" disabled />);
      const radio = screen.getByLabelText('Test Radio');
      expect(radio).toBeDisabled();
    });

    it('shows error state correctly', () => {
      render(<Radio label="Test Radio" name="test-group" value="test" error="Please select an option" />);
      
      const radio = screen.getByLabelText('Test Radio');
      const errorMessage = screen.getByText('Please select an option');
      
      expect(radio.parentElement).toHaveClass('border-red-500');
      expect(errorMessage).toBeInTheDocument();
    });

    it('should not have accessibility violations', async () => {
      const { container } = render(<Radio label="Accessible Radio" name="test-group" value="test" />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Textarea Component', () => {
    it('renders textarea with label', () => {
      render(<Textarea label="Test Textarea" />);
      expect(screen.getByLabelText('Test Textarea')).toBeInTheDocument();
      expect(screen.getByText('Test Textarea')).toBeInTheDocument();
    });

    it('renders with placeholder', () => {
      render(<Textarea placeholder="Enter your message here" />);
      expect(screen.getByPlaceholderText('Enter your message here')).toBeInTheDocument();
    });

    it('handles value changes', async () => {
      const handleChange = jest.fn();
      render(<Textarea label="Test Textarea" onChange={handleChange} />);
      
      const textarea = screen.getByLabelText('Test Textarea');
      await user.type(textarea, 'test message');
      
      expect(handleChange).toHaveBeenCalled();
      expect(textarea.value).toBe('test message');
    });

    it('shows error state correctly', () => {
      render(<Textarea label="Test Textarea" error="This field is required" />);
      
      const textarea = screen.getByLabelText('Test Textarea');
      const errorMessage = screen.getByText('This field is required');
      
      expect(textarea).toHaveClass('border-red-500');
      expect(errorMessage).toBeInTheDocument();
    });

    it('shows helper text', () => {
      render(<Textarea label="Test Textarea" helperText="Maximum 500 characters" />);
      expect(screen.getByText('Maximum 500 characters')).toBeInTheDocument();
    });

    it('handles disabled state', () => {
      render(<Textarea label="Test Textarea" disabled />);
      const textarea = screen.getByLabelText('Test Textarea');
      expect(textarea).toBeDisabled();
      expect(textarea).toHaveClass('bg-gray-100', 'cursor-not-allowed');
    });

    it('handles required state', () => {
      render(<Textarea label="Test Textarea" required />);
      const textarea = screen.getByLabelText('Test Textarea *');
      expect(textarea).toBeRequired();
    });

    it('applies custom rows', () => {
      render(<Textarea label="Test Textarea" rows={10} />);
      const textarea = screen.getByLabelText('Test Textarea');
      expect(textarea).toHaveAttribute('rows', '10');
    });

    it('handles resize property', () => {
      render(<Textarea label="Test Textarea" resize={false} />);
      const textarea = screen.getByLabelText('Test Textarea');
      expect(textarea).toHaveClass('resize-none');
    });

    it('should not have accessibility violations', async () => {
      const { container } = render(<Textarea label="Accessible Textarea" />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Form Integration', () => {
    it('works together in a form', async () => {
      const handleSubmit = jest.fn(e => e.preventDefault());
      
      render(
        <form onSubmit={handleSubmit}>
          <Input label="Name" name="name" required />
          <Select 
            label="Country" 
            name="country" 
            options={[
              { value: 'us', label: 'United States' },
              { value: 'ca', label: 'Canada' }
            ]} 
          />
          <Checkbox label="Subscribe to newsletter" name="newsletter" />
          <Radio label="Male" name="gender" value="male" />
          <Radio label="Female" name="gender" value="female" />
          <Textarea label="Message" name="message" />
          <button type="submit">Submit</button>
        </form>
      );
      
      // Fill out the form
      await user.type(screen.getByLabelText('Name *'), 'John Doe');
      await user.selectOptions(screen.getByLabelText('Country'), 'us');
      await user.click(screen.getByLabelText('Subscribe to newsletter'));
      await user.click(screen.getByLabelText('Male'));
      await user.type(screen.getByLabelText('Message'), 'Hello world');
      
      // Submit the form
      await user.click(screen.getByRole('button', { name: 'Submit' }));
      
      expect(handleSubmit).toHaveBeenCalled();
    });

    it('validates required fields', async () => {
      render(
        <form>
          <Input label="Name" required />
          <button type="submit">Submit</button>
        </form>
      );
      
      const submitButton = screen.getByRole('button', { name: 'Submit' });
      await user.click(submitButton);
      
      const nameInput = screen.getByLabelText('Name *');
      expect(nameInput).toBeInvalid();
    });
  });

  describe('Performance', () => {
    it('renders form elements within acceptable time', () => {
      const startTime = performance.now();
      
      render(
        <div>
          <Input label="Input" />
          <Select label="Select" options={[{ value: '1', label: 'Option 1' }]} />
          <Checkbox label="Checkbox" />
          <Radio label="Radio" name="radio" value="1" />
          <Textarea label="Textarea" />
        </div>
      );
      
      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('handles rapid input changes efficiently', async () => {
      const handleChange = jest.fn();
      render(<Input label="Performance Test" onChange={handleChange} />);
      
      const input = screen.getByLabelText('Performance Test');
      
      // Simulate rapid typing
      const startTime = performance.now();
      await user.type(input, 'rapid typing test');
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(1000);
      expect(handleChange).toHaveBeenCalled();
    });
  });

  describe('Visual Regression', () => {
    it('matches snapshots for all form elements', () => {
      const options = [{ value: '1', label: 'Option 1' }];
      
      const { container: inputContainer } = render(<Input label="Input" />);
      expect(inputContainer.firstChild).toMatchSnapshot('form-input');
      
      const { container: selectContainer } = render(<Select label="Select" options={options} />);
      expect(selectContainer.firstChild).toMatchSnapshot('form-select');
      
      const { container: checkboxContainer } = render(<Checkbox label="Checkbox" />);
      expect(checkboxContainer.firstChild).toMatchSnapshot('form-checkbox');
      
      const { container: radioContainer } = render(<Radio label="Radio" name="test" value="1" />);
      expect(radioContainer.firstChild).toMatchSnapshot('form-radio');
      
      const { container: textareaContainer } = render(<Textarea label="Textarea" />);
      expect(textareaContainer.firstChild).toMatchSnapshot('form-textarea');
    });

    it('matches snapshots for error states', () => {
      const { container: inputContainer } = render(<Input label="Input" error="Error message" />);
      expect(inputContainer.firstChild).toMatchSnapshot('form-input-error');
      
      const { container: selectContainer } = render(<Select label="Select" options={[]} error="Error message" />);
      expect(selectContainer.firstChild).toMatchSnapshot('form-select-error');
    });
  });
});