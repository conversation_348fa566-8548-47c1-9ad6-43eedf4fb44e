import React from 'react';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import Breadcrumb from '../Breadcrumb';

// Mock react-router-dom useLocation
const mockUseLocation = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useLocation: () => mockUseLocation()
}));

// Test wrapper with Router
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('Breadcrumb Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders home breadcrumb correctly', () => {
    mockUseLocation.mockReturnValue({ pathname: '/' });
    
    render(
      <TestWrapper>
        <Breadcrumb showHome={true} />
      </TestWrapper>
    );

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByLabelText('Breadcrumb navigation')).toBeInTheDocument();
  });

  it('renders dashboard breadcrumb with parent', () => {
    mockUseLocation.mockReturnValue({ pathname: '/dashboard' });
    
    render(
      <TestWrapper>
        <Breadcrumb />
      </TestWrapper>
    );

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByLabelText('Breadcrumb navigation')).toBeInTheDocument();
  });

  it('renders nested breadcrumb correctly', () => {
    mockUseLocation.mockReturnValue({ pathname: '/anomaly-detection' });
    
    render(
      <TestWrapper>
        <Breadcrumb />
      </TestWrapper>
    );

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Reports')).toBeInTheDocument();
    expect(screen.getByText('Anomaly Detection')).toBeInTheDocument();
  });

  it('does not render on home page by default', () => {
    mockUseLocation.mockReturnValue({ pathname: '/' });
    
    render(
      <TestWrapper>
        <Breadcrumb showHome={false} />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Breadcrumb navigation')).not.toBeInTheDocument();
  });

  it('handles unknown routes gracefully', () => {
    mockUseLocation.mockReturnValue({ pathname: '/unknown-route' });
    
    render(
      <TestWrapper>
        <Breadcrumb />
      </TestWrapper>
    );

    expect(screen.queryByLabelText('Breadcrumb navigation')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    mockUseLocation.mockReturnValue({ pathname: '/dashboard' });
    
    render(
      <TestWrapper>
        <Breadcrumb className="custom-class" />
      </TestWrapper>
    );

    const nav = screen.getByLabelText('Breadcrumb navigation');
    expect(nav).toHaveClass('custom-class');
  });

  it('marks current page with aria-current', () => {
    mockUseLocation.mockReturnValue({ pathname: '/dashboard' });
    
    render(
      <TestWrapper>
        <Breadcrumb />
      </TestWrapper>
    );

    const currentPage = screen.getByText('Dashboard');
    expect(currentPage).toHaveAttribute('aria-current', 'page');
  });

  it('renders links for parent pages', () => {
    mockUseLocation.mockReturnValue({ pathname: '/dashboard' });
    
    render(
      <TestWrapper>
        <Breadcrumb />
      </TestWrapper>
    );

    const homeLink = screen.getByRole('link', { name: /home/<USER>
    expect(homeLink).toHaveAttribute('href', '/');
  });
});
