import React from 'react';
import Card from './Card';
import { Stack, Inline, Box, Center } from './Spacing';
import { Grid, Flex, Container, Section } from './Layout';

/**
 * Example layout patterns demonstrating the 8pt spacing system
 * These patterns can be used as templates throughout the application
 */

// Hero Section Pattern
export const HeroSection = ({ title, subtitle, children }) => (
  <Section className="py-20 md:py-24 lg:py-32">
    <Container>
      <Center maxWidth="large">
        <Stack spacing="large" align="center">
          <div className="text-center">
            <h1 className="text-responsive-h1 mb-4">{title}</h1>
            {subtitle && (
              <p className="text-responsive-body-lg text-text-secondary">
                {subtitle}
              </p>
            )}
          </div>
          {children}
        </Stack>
      </Center>
    </Container>
  </Section>
);

// Card Grid Pattern
export const CardGrid = ({ cards, columns = 3 }) => (
  <Grid cols={columns} gap="medium">
    {cards.map((card, index) => (
      <Card key={index} variant="elevated" padding="medium">
        {card}
      </Card>
    ))}
  </Grid>
);

// Dashboard Layout Pattern
export const DashboardLayout = ({ sidebar, content }) => (
  <Container className="py-8">
    <div className="grid grid-cols-12 gap-8">
      <aside className="col-span-12 lg:col-span-3">
        <Stack spacing="medium">
          {sidebar}
        </Stack>
      </aside>
      <main className="col-span-12 lg:col-span-9">
        <Stack spacing="large">
          {content}
        </Stack>
      </main>
    </div>
  </Container>
);

// Feature Section Pattern
export const FeatureSection = ({ features }) => (
  <Section className="py-16">
    <Container>
      <Grid cols={3} gap="large">
        {features.map((feature, index) => (
          <Stack key={index} spacing="medium" align="center">
            <div className="w-16 h-16 rounded-2xl bg-primary/10 flex items-center justify-center">
              {feature.icon}
            </div>
            <div className="text-center">
              <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
              <p className="text-text-secondary">{feature.description}</p>
            </div>
          </Stack>
        ))}
      </Grid>
    </Container>
  </Section>
);

// Form Layout Pattern
export const FormLayout = ({ title, description, children, actions }) => (
  <Box padding="large">
    <Stack spacing="large">
      {(title || description) && (
        <div>
          {title && <h2 className="text-2xl font-bold mb-2">{title}</h2>}
          {description && (
            <p className="text-text-secondary">{description}</p>
          )}
        </div>
      )}
      
      <Stack spacing="medium">
        {children}
      </Stack>
      
      {actions && (
        <Inline spacing="medium" className="pt-4 border-t">
          {actions}
        </Inline>
      )}
    </Stack>
  </Box>
);

// Stats Grid Pattern
export const StatsGrid = ({ stats }) => (
  <Grid cols={4} gap="medium">
    {stats.map((stat, index) => (
      <Card key={index} padding="medium" className="text-center">
        <div className="text-3xl font-bold text-primary mb-2">
          {stat.value}
        </div>
        <div className="text-sm text-text-secondary">
          {stat.label}
        </div>
      </Card>
    ))}
  </Grid>
);

// Content With Sidebar Pattern
export const ContentWithSidebar = ({ content, sidebar, sidebarPosition = 'right' }) => (
  <div className={`grid grid-cols-12 gap-8 ${
    sidebarPosition === 'left' ? 'flex-row-reverse' : ''
  }`}>
    <div className="col-span-12 md:col-span-8">
      {content}
    </div>
    <div className="col-span-12 md:col-span-4">
      <div className="sticky top-8">
        {sidebar}
      </div>
    </div>
  </div>
);

// Modal Content Pattern
export const ModalContent = ({ title, content, actions }) => (
  <Stack spacing="medium">
    {title && (
      <h3 className="text-xl font-semibold pb-4 border-b">
        {title}
      </h3>
    )}
    
    <div className="py-4">
      {content}
    </div>
    
    {actions && (
      <Flex justify="end" gap="medium" className="pt-4 border-t">
        {actions}
      </Flex>
    )}
  </Stack>
);

// List Pattern with consistent spacing
export const ListPattern = ({ items, divided = false }) => (
  <Stack spacing={divided ? "none" : "small"}>
    {items.map((item, index) => (
      <div
        key={index}
        className={`
          ${divided ? 'py-4 border-b last:border-b-0' : ''}
        `}
      >
        {item}
      </div>
    ))}
  </Stack>
);

// Responsive Two Column Pattern
export const TwoColumnPattern = ({ left, right, gap = "large" }) => (
  <div className={`grid grid-cols-1 md:grid-cols-2 gap-${gap === 'large' ? '8' : gap === 'medium' ? '6' : '4'}`}>
    <div>{left}</div>
    <div>{right}</div>
  </div>
);

// Page Header Pattern
export const PageHeader = ({ title, description, actions }) => (
  <div className="mb-8">
    <Flex justify="between" items="start" gap="large">
      <div>
        <h1 className="text-3xl font-bold mb-2">{title}</h1>
        {description && (
          <p className="text-text-secondary max-w-2xl">
            {description}
          </p>
        )}
      </div>
      {actions && (
        <Inline spacing="medium">
          {actions}
        </Inline>
      )}
    </Flex>
  </div>
);

// Empty State Pattern
export const EmptyState = ({ icon, title, description, action }) => (
  <Center>
    <Box padding="2xl" className="text-center">
      <Stack spacing="medium" align="center">
        {icon && (
          <div className="w-16 h-16 rounded-full bg-background-secondary flex items-center justify-center mb-4">
            {icon}
          </div>
        )}
        <div>
          <h3 className="text-lg font-semibold mb-2">{title}</h3>
          {description && (
            <p className="text-text-secondary max-w-md">
              {description}
            </p>
          )}
        </div>
        {action}
      </Stack>
    </Box>
  </Center>
);

export default {
  HeroSection,
  CardGrid,
  DashboardLayout,
  FeatureSection,
  FormLayout,
  StatsGrid,
  ContentWithSidebar,
  ModalContent,
  ListPattern,
  TwoColumnPattern,
  PageHeader,
  EmptyState,
};
