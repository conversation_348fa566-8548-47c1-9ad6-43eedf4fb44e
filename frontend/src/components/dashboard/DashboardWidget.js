import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Icon from '../modern/Icon';

const DashboardWidget = ({ 
  title, 
  subtitle, 
  children, 
  icon, 
  iconColor = 'primary',
  tooltip,
  action,
  variant = 'default',
  className = '',
  isLoading = false,
  isEmpty = false,
  emptyState,
  error = null,
  errorState
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const variants = {
    default: 'bg-white/80 backdrop-blur-sm border border-gray-200/50',
    glass: 'bg-white/60 backdrop-blur-md border border-gray-200/30',
    floating: 'bg-white shadow-xl border-0',
    gradient: 'bg-gradient-to-br from-white to-gray-50 border border-gray-200/30'
  };

  const iconColorClasses = {
    primary: 'text-primary',
    secondary: 'text-secondary-lavender',
    positive: 'text-emotion-positive',
    negative: 'text-emotion-negative',
    warning: 'text-accent-yellow'
  };

  if (error && errorState) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`rounded-2xl p-6 ${variants[variant]} ${className}`}
      >
        <div className="flex flex-col items-center justify-center h-full min-h-[200px] text-center">
          <div className="w-16 h-16 mb-4 bg-emotion-negative/10 rounded-full flex items-center justify-center">
            <Icon name="AlertTriangle" size="lg" className="text-emotion-negative" />
          </div>
          {errorState}
        </div>
      </motion.div>
    );
  }

  if (isEmpty && emptyState) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`rounded-2xl p-6 ${variants[variant]} ${className}`}
      >
        <div className="flex flex-col items-center justify-center h-full min-h-[200px] text-center">
          {emptyState}
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2, transition: { duration: 0.2 } }}
      className={`rounded-2xl p-6 transition-all duration-300 hover:shadow-lg ${variants[variant]} ${className} relative`}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start space-x-3">
          {icon && (
            <div className={`p-2 rounded-lg bg-gradient-to-br from-${iconColor}/10 to-${iconColor}/5`}>
              <Icon name={icon} className={iconColorClasses[iconColor]} />
            </div>
          )}
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {tooltip && (
            <div className="relative">
              <button
                onMouseEnter={() => setShowTooltip(true)}
                onMouseLeave={() => setShowTooltip(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <Icon name="HelpCircle" size="sm" />
              </button>
              {showTooltip && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="absolute right-0 top-8 w-64 p-3 bg-gray-900 text-white text-sm rounded-lg shadow-xl z-10"
                >
                  <div className="absolute -top-2 right-2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900"></div>
                  {tooltip}
                </motion.div>
              )}
            </div>
          )}
          {action}
        </div>
      </div>

      {/* Content */}
      <div className="relative">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary/30 border-t-primary"></div>
          </div>
        ) : (
          children
        )}
      </div>
    </motion.div>
  );
};

export default DashboardWidget;
