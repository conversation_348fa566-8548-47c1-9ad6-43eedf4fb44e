import React, { useEffect, useState } from 'react';
import { motion, animate } from 'framer-motion';

const AnimatedCounter = ({ 
  value, 
  duration = 1.5, 
  prefix = '', 
  suffix = '',
  decimals = 0,
  className = '',
  formatNumber = true
}) => {
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    const controls = animate(displayValue, value, {
      duration,
      onUpdate: (v) => setDisplayValue(v)
    });

    return controls.stop;
  }, [value, duration, displayValue]);

  const formattedValue = formatNumber 
    ? new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
      }).format(displayValue)
    : displayValue.toFixed(decimals);

  return (
    <motion.span 
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      {prefix}{formattedValue}{suffix}
    </motion.span>
  );
};

export default AnimatedCounter;
