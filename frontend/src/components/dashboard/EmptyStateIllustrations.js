import React from 'react';
import { motion } from 'framer-motion';

export const NoDataIllustration = ({ className = '' }) => (
  <motion.svg
    className={`w-48 h-48 ${className}`}
    viewBox="0 0 200 200"
    initial={{ opacity: 0, scale: 0.8 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.5 }}
  >
    <circle cx="100" cy="100" r="80" fill="#f3f4f6" />
    <path
      d="M70 80 Q100 60 130 80"
      stroke="#9ca3af"
      strokeWidth="3"
      fill="none"
      strokeLinecap="round"
    />
    <circle cx="75" cy="75" r="5" fill="#9ca3af" />
    <circle cx="125" cy="75" r="5" fill="#9ca3af" />
    <path
      d="M80 120 Q100 105 120 120"
      stroke="#9ca3af"
      strokeWidth="3"
      fill="none"
      strokeLinecap="round"
    />
    <motion.path
      d="M50 50 L60 60 M150 50 L140 60 M50 150 L60 140 M150 150 L140 140"
      stroke="#e5e7eb"
      strokeWidth="2"
      strokeLinecap="round"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 1, delay: 0.5 }}
    />
  </motion.svg>
);

export const NoRelationshipsIllustration = ({ className = '' }) => (
  <motion.svg
    className={`w-48 h-48 ${className}`}
    viewBox="0 0 200 200"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ duration: 0.5 }}
  >
    <motion.circle
      cx="60"
      cy="80"
      r="25"
      fill="#e0e7ff"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.3, delay: 0.1 }}
    />
    <motion.circle
      cx="140"
      cy="80"
      r="25"
      fill="#ddd6fe"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.3, delay: 0.2 }}
    />
    <motion.circle
      cx="100"
      cy="140"
      r="25"
      fill="#fce7f3"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.3, delay: 0.3 }}
    />
    <motion.path
      d="M85 80 L115 80 M100 105 L100 115 M75 125 L85 115 M115 115 L125 125"
      stroke="#9ca3af"
      strokeWidth="2"
      strokeLinecap="round"
      strokeDasharray="5 5"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 1, delay: 0.5 }}
    />
  </motion.svg>
);

export const NoMessagesIllustration = ({ className = '' }) => (
  <motion.svg
    className={`w-48 h-48 ${className}`}
    viewBox="0 0 200 200"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
  >
    <motion.rect
      x="40"
      y="60"
      width="120"
      height="80"
      rx="10"
      fill="#f3f4f6"
      initial={{ scale: 0.8 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.3 }}
    />
    <motion.path
      d="M40 70 L100 100 L160 70"
      stroke="#9ca3af"
      strokeWidth="2"
      fill="none"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    />
    <motion.circle
      cx="100"
      cy="100"
      r="30"
      fill="#e5e7eb"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.3, delay: 0.5 }}
    />
    <motion.path
      d="M85 100 L95 110 L115 90"
      stroke="#6366f1"
      strokeWidth="3"
      fill="none"
      strokeLinecap="round"
      strokeLinejoin="round"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.5, delay: 0.7 }}
    />
  </motion.svg>
);

export const ErrorIllustration = ({ className = '' }) => (
  <motion.svg
    className={`w-48 h-48 ${className}`}
    viewBox="0 0 200 200"
    initial={{ opacity: 0, rotate: -10 }}
    animate={{ opacity: 1, rotate: 0 }}
    transition={{ duration: 0.5 }}
  >
    <motion.circle
      cx="100"
      cy="100"
      r="80"
      fill="#fee2e2"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.3 }}
    />
    <motion.path
      d="M100 40 L100 110"
      stroke="#ef4444"
      strokeWidth="8"
      strokeLinecap="round"
      initial={{ pathLength: 0 }}
      animate={{ pathLength: 1 }}
      transition={{ duration: 0.3, delay: 0.2 }}
    />
    <motion.circle
      cx="100"
      cy="140"
      r="5"
      fill="#ef4444"
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.2, delay: 0.5 }}
    />
  </motion.svg>
);
