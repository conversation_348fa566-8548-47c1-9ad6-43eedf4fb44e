import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import Icon from '../modern/Icon';

const FAB = ({ actions = [] }) => {
  const [isOpen, setIsOpen] = useState(false);

  const defaultActions = [
    {
      icon: 'MessageSquare',
      label: 'Analyze Message',
      to: '/message-analyzer',
      color: 'primary'
    },
    {
      icon: 'Users',
      label: 'Add Relationship',
      to: '/relationships',
      color: 'secondary'
    },
    {
      icon: 'TrendingUp',
      label: 'View Growth',
      to: '/growth-center',
      color: 'positive'
    },
    {
      icon: 'FileText',
      label: 'Generate Report',
      to: '/reports',
      color: 'warning'
    }
  ];

  const actionItems = actions.length > 0 ? actions : defaultActions;

  const colorMap = {
    primary: 'bg-primary hover:bg-primary-dark',
    secondary: 'bg-secondary-lavender hover:bg-secondary-violet',
    positive: 'bg-emotion-positive hover:bg-emotion-positive/90',
    warning: 'bg-accent-yellow hover:bg-accent-yellow/90'
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute bottom-16 right-0 space-y-3"
          >
            {actionItems.map((action, index) => (
              <motion.div
                key={action.label}
                initial={{ opacity: 0, x: 20, y: 20 }}
                animate={{ 
                  opacity: 1, 
                  x: 0, 
                  y: 0,
                  transition: { delay: index * 0.05 }
                }}
                exit={{ 
                  opacity: 0, 
                  x: 20, 
                  y: 20,
                  transition: { delay: (actionItems.length - index - 1) * 0.05 }
                }}
                className="flex items-center justify-end"
              >
                <span className="mr-3 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg shadow-lg whitespace-nowrap">
                  {action.label}
                </span>
                <Link
                  to={action.to}
                  className={`w-12 h-12 rounded-full ${colorMap[action.color]} text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white/50`}
                  onClick={() => setIsOpen(false)}
                  aria-label={action.label}
                  title={action.label}
                >
                  <Icon name={action.icon} size="sm" decorative={true} />
                </Link>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="w-14 h-14 rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        animate={{ rotate: isOpen ? 45 : 0 }}
        aria-label={isOpen ? "Close quick actions menu" : "Open quick actions menu"}
        aria-expanded={isOpen}
        title={isOpen ? "Close quick actions menu" : "Open quick actions menu"}
      >
        <Icon name="Plus" size="lg" decorative={true} />
      </motion.button>
    </div>
  );
};

export default FAB;
