import React, { useEffect, useState } from 'react';
import { motion, animate } from 'framer-motion';
import AnimatedCounter from './AnimatedCounter';

const ProgressRing = ({ 
  value, 
  size = 120, 
  strokeWidth = 10,
  color = 'primary',
  backgroundColor = '#e5e7eb',
  showPercentage = true,
  label = '',
  duration = 1.5,
  className = ''
}) => {
  const [progress, setProgress] = useState(0);
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const offset = circumference - (progress / 100) * circumference;

  useEffect(() => {
    const controls = animate(0, value, {
      duration,
      onUpdate: (v) => setProgress(v)
    });

    return controls.stop;
  }, [value, duration]);

  const colorMap = {
    primary: 'url(#primaryGradient)',
    secondary: 'url(#secondaryGradient)',
    positive: '#10b981',
    negative: '#ef4444',
    warning: '#f59e0b'
  };

  const getColor = () => colorMap[color] || color;

  return (
    <div className={`relative inline-flex flex-col items-center ${className}`}>
      <svg width={size} height={size} className="transform -rotate-90">
        <defs>
          <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#6366f1" />
            <stop offset="100%" stopColor="#818cf8" />
          </linearGradient>
          <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#a78bfa" />
            <stop offset="100%" stopColor="#c084fc" />
          </linearGradient>
        </defs>
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="none"
        />
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={getColor()}
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round"
          initial={{ strokeDasharray: circumference, strokeDashoffset: circumference }}
          animate={{ strokeDashoffset: offset }}
          transition={{ duration, ease: "easeOut" }}
        />
      </svg>
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        {showPercentage && (
          <div className="text-2xl font-bold text-gray-900">
            <AnimatedCounter value={progress} suffix="%" />
          </div>
        )}
        {label && (
          <p className="text-xs text-gray-600 mt-1">{label}</p>
        )}
      </div>
    </div>
  );
};

export default ProgressRing;
