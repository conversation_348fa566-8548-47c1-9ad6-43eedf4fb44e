import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import Icon from './Icon';

const ThemeToggle = ({ className = '', showLabel = false }) => {
  const { theme, toggleTheme, isDark } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={`
        inline-flex items-center justify-center
        w-10 h-10 rounded-lg
        surface-primary border border-default
        hover:surface-secondary hover:border-medium
        focus:outline-none focus:ring-2 focus:border-focus
        transition-all duration-200 ease-in-out
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} theme`}
      title={`Switch to ${isDark ? 'light' : 'dark'} theme`}
    >
      <Icon 
        name={isDark ? 'Sun' : 'Moon'} 
        size="sm" 
        className="text-primary transition-transform duration-200 ease-in-out"
        decorative={true}
      />
      {showLabel && (
        <span className="ml-2 text-sm font-medium text-primary">
          {isDark ? 'Light' : 'Dark'}
        </span>
      )}
    </button>
  );
};

export default ThemeToggle;
