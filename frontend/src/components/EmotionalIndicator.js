import React from 'react';
import PropTypes from 'prop-types';
import { getEmotionColor, getHealthScoreColor } from '../utils/ChartColors';

/**
 * EmotionalIndicator component for displaying emotional states
 */
const EmotionalIndicator = ({
  type = 'badge',
  emotion = 'neutral',
  intensity = 'medium',
  label,
  showLabel = true,
  size = 'medium',
  className = '',
  ...props
}) => {
  // Get the base color for the emotion
  const baseColor = typeof emotion === 'string' 
    ? getEmotionColor(emotion) 
    : getHealthScoreColor(emotion);
  
  // Intensity variations
  const getIntensityStyles = () => {
    switch (intensity) {
      case 'low':
        return {
          backgroundColor: `${baseColor}33`, // 20% opacity
          borderColor: `${baseColor}66`,     // 40% opacity
          color: '#666666'
        };
      case 'medium':
        return {
          backgroundColor: `${baseColor}66`, // 40% opacity
          borderColor: `${baseColor}99`,     // 60% opacity
          color: '#333333'
        };
      case 'high':
        return {
          backgroundColor: `${baseColor}99`, // 60% opacity
          borderColor: baseColor,
          color: '#111111'
        };
      case 'very-high':
        return {
          backgroundColor: baseColor,
          borderColor: baseColor,
          color: '#FFFFFF'
        };
      default:
        return {
          backgroundColor: `${baseColor}66`, // 40% opacity
          borderColor: `${baseColor}99`,     // 60% opacity
          color: '#333333'
        };
    }
  };
  
  // Size variations
  const sizeClasses = {
    small: 'text-xs py-0.5 px-2',
    medium: 'text-sm py-1 px-3',
    large: 'text-base py-1.5 px-4'
  };
  
  // Get the appropriate label text
  const getLabelText = () => {
    if (label) return label;
    
    if (typeof emotion === 'number') {
      // For health scores
      if (emotion >= 80) return 'Excellent';
      if (emotion >= 60) return 'Good';
      if (emotion >= 40) return 'Neutral';
      if (emotion >= 20) return 'Concerning';
      return 'Critical';
    }
    
    // For named emotions
    switch (emotion) {
      case 'positive': return 'Positive';
      case 'negative': return 'Negative';
      case 'neutral': return 'Neutral';
      default: return emotion.charAt(0).toUpperCase() + emotion.slice(1);
    }
  };
  
  // Render different types of indicators
  switch (type) {
    case 'badge':
      const intensityStyles = getIntensityStyles();
      return (
        <span
          className={`
            inline-flex items-center rounded-full border
            font-medium ${sizeClasses[size]} ${className}
          `}
          style={{
            backgroundColor: intensityStyles.backgroundColor,
            borderColor: intensityStyles.borderColor,
            color: intensityStyles.color
          }}
          {...props}
        >
          {showLabel && getLabelText()}
        </span>
      );
      
    case 'dot':
      return (
        <div className={`inline-flex items-center ${className}`} {...props}>
          <span
            className={`
              inline-block rounded-full
              ${size === 'small' ? 'h-2 w-2' : size === 'large' ? 'h-4 w-4' : 'h-3 w-3'}
            `}
            style={{ backgroundColor: baseColor }}
            aria-hidden="true"
          />
          {showLabel && (
            <span className={`ml-2 ${sizeClasses[size]}`}>
              {getLabelText()}
            </span>
          )}
        </div>
      );
      
    case 'flag':
      return (
        <div 
          className={`
            inline-flex items-center border-l-4 
            ${size === 'small' ? 'pl-2 py-1' : size === 'large' ? 'pl-4 py-3' : 'pl-3 py-2'}
            ${className}
          `}
          style={{ borderLeftColor: baseColor }}
          {...props}
        >
          {showLabel && (
            <span className={sizeClasses[size]}>
              {getLabelText()}
            </span>
          )}
        </div>
      );
      
    case 'bar':
      return (
        <div className={`inline-block ${className}`} {...props}>
          {showLabel && (
            <div className="mb-1 flex justify-between">
              <span className={`text-text-secondary ${sizeClasses[size]}`}>
                {getLabelText()}
              </span>
              {typeof emotion === 'number' && (
                <span className={`font-medium ${sizeClasses[size]}`}>
                  {emotion}%
                </span>
              )}
            </div>
          )}
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="h-2.5 rounded-full" 
              style={{ 
                width: typeof emotion === 'number' ? `${emotion}%` : '50%',
                backgroundColor: baseColor 
              }}
              aria-valuenow={typeof emotion === 'number' ? emotion : 50}
              aria-valuemin="0"
              aria-valuemax="100"
              role="progressbar"
            />
          </div>
        </div>
      );
      
    case 'circle':
      // Calculate the circumference and offset for the circle
      const radius = size === 'small' ? 12 : size === 'large' ? 24 : 18;
      const circumference = 2 * Math.PI * radius;
      const offset = typeof emotion === 'number' 
        ? circumference - (emotion / 100) * circumference
        : circumference / 2;
      
      return (
        <div className={`inline-flex flex-col items-center ${className}`} {...props}>
          <svg 
            className={`
              ${size === 'small' ? 'w-8 h-8' : size === 'large' ? 'w-16 h-16' : 'w-12 h-12'}
              transform -rotate-90
            `}
            viewBox={`0 0 ${radius * 2 + 8} ${radius * 2 + 8}`}
          >
            <circle
              cx={radius + 4}
              cy={radius + 4}
              r={radius}
              stroke="#e6e6e6"
              strokeWidth="4"
              fill="none"
            />
            <circle
              cx={radius + 4}
              cy={radius + 4}
              r={radius}
              stroke={baseColor}
              strokeWidth="4"
              fill="none"
              strokeDasharray={circumference}
              strokeDashoffset={offset}
              strokeLinecap="round"
            />
          </svg>
          {showLabel && (
            <span className={`mt-2 ${sizeClasses[size]}`}>
              {getLabelText()}
            </span>
          )}
          {typeof emotion === 'number' && (
            <span className={`font-bold ${size === 'small' ? 'text-sm' : size === 'large' ? 'text-xl' : 'text-base'}`}>
              {emotion}%
            </span>
          )}
        </div>
      );
      
    default:
      return null;
  }
};

EmotionalIndicator.propTypes = {
  type: PropTypes.oneOf(['badge', 'dot', 'flag', 'bar', 'circle']),
  emotion: PropTypes.oneOfType([
    PropTypes.oneOf(['positive', 'neutral', 'negative']),
    PropTypes.string,
    PropTypes.number // For health scores (0-100)
  ]),
  intensity: PropTypes.oneOf(['low', 'medium', 'high', 'very-high']),
  label: PropTypes.string,
  showLabel: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  className: PropTypes.string,
};

/**
 * SentimentBadge component - a specialized version of EmotionalIndicator for sentiment
 */
export const SentimentBadge = ({ sentiment, ...props }) => (
  <EmotionalIndicator
    type="badge"
    emotion={sentiment}
    {...props}
  />
);

SentimentBadge.propTypes = {
  sentiment: PropTypes.oneOf(['positive', 'neutral', 'negative']),
};

/**
 * HealthScore component - a specialized version of EmotionalIndicator for health scores
 */
export const HealthScore = ({ score, ...props }) => (
  <EmotionalIndicator
    type="circle"
    emotion={score}
    {...props}
  />
);

HealthScore.propTypes = {
  score: PropTypes.number,
};

/**
 * FlagIndicator component - a specialized version of EmotionalIndicator for flags
 */
export const FlagIndicator = ({ flagType, ...props }) => (
  <EmotionalIndicator
    type="flag"
    emotion={flagType}
    {...props}
  />
);

FlagIndicator.propTypes = {
  flagType: PropTypes.string,
};

export default EmotionalIndicator;