import React, { useState } from 'react';
import PropTypes from 'prop-types';

/**
 * Modernized NavBar component with enhanced styling and mobile responsiveness
 */
const NavBar = ({
  brand,
  items = [],
  variant = 'default',
  position = 'static',
  className = '',
  rightContent,
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Position variations
  const positionClasses = {
    static: 'static',
    fixed: 'fixed top-0 left-0 right-0 z-40',
    sticky: 'sticky top-0 z-40',
  };

  // Variant styles
  const variantClasses = {
    default: 'bg-white border-b border-gray-200 shadow-sm',
    glass: 'bg-glass-white backdrop-blur-xl border-b border-white/30',
    dark: 'bg-gray-900 border-b border-gray-800',
    gradient: 'bg-gradient-primary border-b border-primary-600/20',
  };

  const textColorClasses = {
    default: 'text-gray-700 hover:text-primary-600',
    glass: 'text-gray-700 hover:text-primary-600',
    dark: 'text-gray-200 hover:text-white',
    gradient: 'text-white hover:text-white/80',
  };

  return (
    <nav
      className={`
        ${positionClasses[position]}
        ${variantClasses[variant]}
        transition-all duration-300
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      {...props}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Brand */}
          <div className="flex items-center">
            {brand && (
              <div className="flex-shrink-0 flex items-center">
                {typeof brand === 'string' ? (
                  <span className="text-xl font-bold">{brand}</span>
                ) : (
                  brand
                )}
              </div>
            )}

            {/* Desktop Navigation */}
            <div className="hidden md:ml-6 md:flex md:space-x-8">
              {items.map((item, index) => (
                <a
                  key={index}
                  href={item.href}
                  className={`
                    inline-flex items-center px-1 pt-1 text-sm font-medium
                    transition-colors duration-200
                    ${textColorClasses[variant]}
                    ${item.active ? 'border-b-2 border-primary-500' : ''}
                  `.trim().replace(/\s+/g, ' ')}
                  onClick={item.onClick}
                >
                  {item.icon && <span className="mr-2">{item.icon}</span>}
                  {item.label}
                </a>
              ))}
            </div>
          </div>

          {/* Right content */}
          {rightContent && (
            <div className="hidden md:ml-6 md:flex md:items-center">
              {rightContent}
            </div>
          )}

          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <button
              type="button"
              className={`
                inline-flex items-center justify-center p-2 rounded-md
                transition-colors duration-200
                ${textColorClasses[variant]}
                focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500
              `.trim().replace(/\s+/g, ' ')}
              aria-controls="mobile-menu"
              aria-expanded={isOpen}
              onClick={() => setIsOpen(!isOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {!isOpen ? (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isOpen && (
        <div className="md:hidden" id="mobile-menu">
          <div className="pt-2 pb-3 space-y-1">
            {items.map((item, index) => (
              <a
                key={index}
                href={item.href}
                className={`
                  block pl-3 pr-4 py-2 text-base font-medium
                  transition-colors duration-200
                  ${textColorClasses[variant]}
                  ${item.active ? 'border-l-4 border-primary-500 bg-primary-50' : ''}
                `.trim().replace(/\s+/g, ' ')}
                onClick={item.onClick}
              >
                {item.icon && <span className="mr-2 inline-block">{item.icon}</span>}
                {item.label}
              </a>
            ))}
          </div>
          {rightContent && (
            <div className="pt-4 pb-3 border-t border-gray-200">
              <div className="px-4">
                {rightContent}
              </div>
            </div>
          )}
        </div>
      )}
    </nav>
  );
};

NavBar.propTypes = {
  brand: PropTypes.node,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      href: PropTypes.string,
      onClick: PropTypes.func,
      active: PropTypes.bool,
      icon: PropTypes.node,
    })
  ),
  variant: PropTypes.oneOf(['default', 'glass', 'dark', 'gradient']),
  position: PropTypes.oneOf(['static', 'fixed', 'sticky']),
  className: PropTypes.string,
  rightContent: PropTypes.node,
};

export default NavBar;
