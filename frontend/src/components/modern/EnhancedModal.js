import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import Icon from './Icon';

/**
 * Enhanced Modal component with swipe-to-dismiss gesture support
 */
const EnhancedModal = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'medium',
  variant = 'default',
  className = '',
  footer,
  enableSwipeToClose = true,
  ...props
}) => {
  const modalRef = useRef(null);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState(0);

  // Lock body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle touch/swipe events for mobile
  const handleTouchStart = (e) => {
    if (!enableSwipeToClose) return;
    setTouchStart(e.targetTouches[0].clientY);
    setIsDragging(true);
  };

  const handleTouchMove = (e) => {
    if (!enableSwipeToClose || !touchStart) return;
    const currentTouch = e.targetTouches[0].clientY;
    const diff = currentTouch - touchStart;
    
    // Only allow downward swipes
    if (diff > 0) {
      setDragOffset(diff);
      setTouchEnd(currentTouch);
    }
  };

  const handleTouchEnd = () => {
    if (!enableSwipeToClose || !touchStart || !touchEnd) return;
    
    const swipeDistance = touchEnd - touchStart;
    const swipeThreshold = 100; // Minimum swipe distance to close

    if (swipeDistance > swipeThreshold) {
      onClose();
    }

    // Reset
    setTouchStart(null);
    setTouchEnd(null);
    setIsDragging(false);
    setDragOffset(0);
  };

  if (!isOpen) return null;

  // Size variations
  const sizeClasses = {
    small: 'max-w-md',
    medium: 'max-w-lg',
    large: 'max-w-2xl',
    xlarge: 'max-w-4xl',
    full: 'max-w-7xl'
  };

  // Variant styles
  const variantClasses = {
    default: 'bg-white',
    glass: 'bg-glass-white backdrop-blur-xl border border-white/30',
    dark: 'bg-gray-800 text-white',
  };

  // Mobile-specific classes
  const mobileClasses = `
    sm:rounded-2xl rounded-t-2xl
    ${isDragging ? 'transition-none' : 'transition-all duration-300'}
  `.trim();

  return (
    <div
      className="fixed inset-0 z-50 overflow-y-auto modal-mobile-optimized"
      aria-labelledby={title ? 'modal-title' : undefined}
      role="dialog"
      aria-modal="true"
      {...props}
    >
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fade-in"
        aria-hidden="true"
        onClick={onClose}
        style={{
          opacity: isDragging ? 0.3 : 1,
          transition: isDragging ? 'none' : 'opacity 0.3s'
        }}
      />

      {/* Modal container */}
      <div className="flex min-h-screen items-end sm:items-center justify-center p-0 sm:p-4 text-center">
        <div
          ref={modalRef}
          className={`
            relative transform overflow-hidden text-left 
            shadow-2xl animate-slide-up
            w-full sm:my-8 ${sizeClasses[size]} 
            ${variantClasses[variant]}
            ${mobileClasses}
            ${className}
          `.trim().replace(/\s+/g, ' ')}
          style={{
            transform: `translateY(${dragOffset}px)`,
            maxHeight: '90vh'
          }}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {/* Swipe handle for mobile */}
          {enableSwipeToClose && (
            <div className="sm:hidden py-2">
              <div className="modal-swipe-handle" />
            </div>
          )}

          {/* Header */}
          <div className="px-4 sm:px-6 pt-4 sm:pt-6 pb-4 border-b border-gray-200 modal-header">
            {/* Close button with 48x48px touch target */}
            <button
              type="button"
              className="absolute top-2 right-2 sm:top-4 sm:right-4 text-gray-400 hover:text-gray-600 transition-colors touch-target-sm rounded-full hover:bg-gray-100"
              onClick={onClose}
              aria-label="Close"
            >
              <Icon name="X" size="md" />
            </button>

            {title && (
              <h3 className="text-lg sm:text-xl font-semibold leading-6 pr-12" id="modal-title">
                {title}
              </h3>
            )}
          </div>

          {/* Modal content */}
          <div className="px-4 sm:px-6 py-4 overflow-y-auto modal-body max-h-[60vh] swipeable-y">
            {children}
          </div>

          {/* Footer */}
          {footer && (
            <div className="px-4 sm:px-6 py-4 border-t border-gray-200 bg-gray-50 modal-footer safe-bottom">
              {footer}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

EnhancedModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string,
  children: PropTypes.node.isRequired,
  size: PropTypes.oneOf(['small', 'medium', 'large', 'xlarge', 'full']),
  variant: PropTypes.oneOf(['default', 'glass', 'dark']),
  className: PropTypes.string,
  footer: PropTypes.node,
  enableSwipeToClose: PropTypes.bool,
};

export default EnhancedModal;
