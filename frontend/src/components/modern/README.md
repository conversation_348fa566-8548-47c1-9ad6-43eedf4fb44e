# Modern Component Library Documentation

This modern component library features enhanced design tokens, depth effects, multiple states, and various style variants. All components are built with accessibility in mind and follow a consistent design system.

## Design Principles

- **Consistent Spacing**: Using an 8pt grid system
- **Enhanced Shadows**: Multiple elevation levels (soft, elevated, floating, glow)
- **Smooth Transitions**: All interactive elements have smooth animations
- **Accessibility First**: Proper ARIA labels, keyboard navigation, and focus states
- **Mobile Responsive**: All components work seamlessly on mobile devices

## Components

### Button

A versatile button component with multiple variants and states.

**Props:**
- `variant`: 'primary' | 'secondary' | 'ghost' | 'danger' | 'glass' (default: 'primary')
- `size`: 'small' | 'medium' | 'large' (default: 'medium')
- `disabled`: boolean (default: false)
- `loading`: boolean (default: false)
- `fullWidth`: boolean (default: false)
- `icon`: ReactNode (optional)
- `onClick`: function
- `type`: 'button' | 'submit' | 'reset' (default: 'button')

**States:**
- Normal
- Hover (slight shadow increase)
- Active (scale down effect)
- Disabled (reduced opacity)
- Loading (spinner overlay)

**Example:**
```jsx
import { Button } from './components/modern';

<Button variant="primary" size="medium" onClick={handleClick}>
  Click me
</Button>

<Button variant="glass" icon={<IconComponent />} loading>
  Loading...
</Button>
```

### Card

A flexible container component with elevation and hover effects.

**Props:**
- `variant`: 'default' | 'elevated' | 'interactive' | 'glass' (default: 'default')
- `padding`: 'none' | 'small' | 'medium' | 'large' (default: 'medium')
- `elevation`: 'none' | 'low' | 'medium' | 'high' | 'glow' (default: 'medium')
- `hover`: boolean (default: false)
- `interactive`: boolean (default: false)

**Example:**
```jsx
import { Card } from './components/modern';

<Card variant="elevated" padding="large" hover>
  <h3>Card Title</h3>
  <p>Card content goes here</p>
</Card>
```

### Form Elements

#### Input

Modern input field with floating label support.

**Props:**
- `variant`: 'default' | 'glass' | 'floating' (default: 'default')
- `type`: standard HTML input types (default: 'text')
- `label`: string
- `placeholder`: string
- `value`: string | number
- `onChange`: function
- `disabled`: boolean
- `required`: boolean
- `error`: string

**Example:**
```jsx
import { Input } from './components/modern';

<Input
  id="email"
  type="email"
  label="Email Address"
  variant="floating"
  value={email}
  onChange={(e) => setEmail(e.target.value)}
  required
/>
```

#### Select

Styled select dropdown with custom arrow.

**Props:**
- `variant`: 'default' | 'glass' | 'modern' (default: 'default')
- `options`: Array<{value: string | number, label: string}>
- `label`: string
- `value`: string | number
- `onChange`: function
- `disabled`: boolean
- `required`: boolean
- `error`: string

#### Checkbox

Enhanced checkbox with animations.

**Props:**
- `variant`: 'default' | 'glass' | 'modern' (default: 'default')
- `label`: string
- `checked`: boolean
- `onChange`: function
- `disabled`: boolean
- `required`: boolean
- `error`: string

#### Textarea

Multi-line text input with consistent styling.

**Props:**
- `variant`: 'default' | 'glass' (default: 'default')
- `rows`: number (default: 4)
- `label`: string
- `placeholder`: string
- `value`: string
- `onChange`: function
- `disabled`: boolean
- `required`: boolean
- `error`: string

### Modal

Accessible modal dialog with backdrop blur.

**Props:**
- `isOpen`: boolean (required)
- `onClose`: function (required)
- `title`: string
- `size`: 'small' | 'medium' | 'large' | 'xlarge' | 'full' (default: 'medium')
- `variant`: 'default' | 'glass' | 'dark' (default: 'default')
- `footer`: ReactNode

**Features:**
- Body scroll lock when open
- Click outside to close
- Smooth fade-in and slide-up animations
- Responsive sizing

**Example:**
```jsx
import { Modal, Button } from './components/modern';

const [isOpen, setIsOpen] = useState(false);

<Modal
  isOpen={isOpen}
  onClose={() => setIsOpen(false)}
  title="Confirm Action"
  footer={
    <>
      <Button variant="ghost" onClick={() => setIsOpen(false)}>Cancel</Button>
      <Button variant="primary" onClick={handleConfirm}>Confirm</Button>
    </>
  }
>
  <p>Are you sure you want to proceed?</p>
</Modal>
```

### Toggle

Modern toggle switch with multiple variants.

**Props:**
- `checked`: boolean
- `onChange`: function (required)
- `label`: string
- `disabled`: boolean
- `size`: 'small' | 'medium' | 'large' (default: 'medium')
- `variant`: 'default' | 'gradient' | 'glass' | 'dark' (default: 'default')
- `labelPosition`: 'left' | 'right' (default: 'right')

**Example:**
```jsx
import { Toggle } from './components/modern';

const [enabled, setEnabled] = useState(false);

<Toggle
  checked={enabled}
  onChange={setEnabled}
  label="Enable notifications"
  variant="gradient"
/>
```

### NavBar

Responsive navigation bar with mobile menu.

**Props:**
- `brand`: ReactNode | string
- `items`: Array<{label: string, href?: string, onClick?: function, active?: boolean, icon?: ReactNode}>
- `variant`: 'default' | 'glass' | 'dark' | 'gradient' (default: 'default')
- `position`: 'static' | 'fixed' | 'sticky' (default: 'static')
- `rightContent`: ReactNode

**Features:**
- Mobile hamburger menu
- Active state indicators
- Support for icons in nav items
- Customizable right section (user menu, buttons, etc.)

**Example:**
```jsx
import { NavBar, Button } from './components/modern';

const navItems = [
  { label: 'Home', href: '/', active: true },
  { label: 'About', href: '/about' },
  { label: 'Services', href: '/services' },
  { label: 'Contact', href: '/contact' },
];

<NavBar
  brand="MyApp"
  items={navItems}
  variant="glass"
  position="sticky"
  rightContent={
    <Button variant="primary" size="small">Sign In</Button>
  }
/>
```

## Styling Guidelines

### Colors
- Primary: Teal gradient (#2A9D8F)
- Secondary: Purple to coral gradient
- Glass effects: White with opacity
- State colors: Success (green), Warning (amber), Error (red)

### Shadows
- `shadow-sm`: Subtle shadow for flat elements
- `shadow-md`: Default shadow for cards and buttons
- `shadow-lg`: Elevated shadow for hover states
- `shadow-xl`: High elevation for modals
- `shadow-glow`: Colored glow effect

### Animations
- All transitions use 200-300ms duration
- Hover effects include slight scale or translation
- Loading states include smooth spinners
- Modals use fade-in and slide-up effects

## Accessibility

- All interactive elements have proper ARIA labels
- Focus states are clearly visible
- Keyboard navigation is fully supported
- Color contrast meets WCAG AA standards
- Screen reader announcements for state changes

## Browser Support

- Chrome (last 2 versions)
- Firefox (last 2 versions)
- Safari (last 2 versions)
- Edge (last 2 versions)
- Mobile browsers (iOS Safari, Chrome Android)

## Future Enhancements

- Dark mode support
- Additional component variants
- Animation preferences respect
- Theming system
- More icon integrations
