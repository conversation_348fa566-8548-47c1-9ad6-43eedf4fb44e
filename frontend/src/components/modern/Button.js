import React from 'react';
import PropTypes from 'prop-types';
import Icon from './Icon';

/**
 * Modernized Button component with enhanced styles
 */
const Button = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  fullWidth = false,
  className = '',
  onClick,
  type = 'button',
  loading = false,
  icon = null,
  ...props
}) => {
  const baseClasses = `
    inline-flex items-center justify-center rounded-lg font-medium
    transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-offset-2
    active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed
    disabled:transform-none shadow-md
    ${loading ? 'cursor-wait' : ''}
  `.trim().replace(/\s+/g, ' ');

  const sizeClasses = {
    small: 'px-4 py-3 text-sm min-h-[48px]',
    medium: 'px-5 py-3.5 text-base min-h-[48px]',
    large: 'px-6 py-4 text-lg min-h-[56px]'
  };

  const variantClasses = {
    primary: `
      bg-gradient-primary text-white
      hover:shadow-lg
      focus:ring-primary-500
    `.trim().replace(/\s+/g, ' '),

    secondary: `
      bg-gradient-secondary text-white
      hover:bg-background-subtle hover:shadow-lg
      focus:ring-secondary-500
    `.trim().replace(/\s+/g, ' '),

    ghost: `
      bg-transparent text-primary
      hover:bg-primary/10
      focus:ring-primary-500
    `.trim().replace(/\s+/g, ' '),

    danger: `
      bg-gradient-to-r from-red-500 to-red-700 text-white
      hover:shadow-lg
      focus:ring-red-500
    `.trim().replace(/\s+/g, ' '),

    glass: `
      bg-glass-white backdrop-blur-lg text-white
      hover:shadow-glow
      focus:ring-white
    `.trim().replace(/\s+/g, ' '),
  };

  const buttonClasses = [
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    fullWidth ? 'w-full' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <button
      type={type}
      className={buttonClasses}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      <div className={`flex items-center justify-center gap-2 ${loading ? 'opacity-0' : ''}`}>
        {icon && <span className="flex-shrink-0">{icon}</span>}
        {children}
      </div>
    </button>
  );
};

Button.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf([
    'primary',
    'secondary',
    'ghost',
    'danger',
    'glass'
  ]),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  disabled: PropTypes.bool,
  fullWidth: PropTypes.bool,
  className: PropTypes.string,
  onClick: PropTypes.func,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  loading: PropTypes.bool,
  icon: PropTypes.node,
};

export default Button;
