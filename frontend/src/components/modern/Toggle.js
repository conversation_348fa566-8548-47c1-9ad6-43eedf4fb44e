import React from 'react';
import PropTypes from 'prop-types';

/**
 * Modernized Toggle component with enhanced animations and variants
 */
const Toggle = ({
  checked = false,
  onChange,
  label,
  disabled = false,
  size = 'medium',
  variant = 'default',
  labelPosition = 'right',
  className = '',
  ...props
}) => {
  // Size variations with proper touch targets (minimum 44px)
  const sizeClasses = {
    small: {
      track: 'h-6 w-11', // Increased from h-5 w-9 for better touch targets
      thumb: 'h-5 w-5',  // Increased from h-4 w-4
      translate: 'translate-x-5', // Adjusted for new track width
      touchArea: 'min-h-[44px] min-w-[44px] p-2', // Ensure 44px touch area
    },
    medium: {
      track: 'h-7 w-12', // Slightly increased for better proportions
      thumb: 'h-6 w-6',  // Increased from h-5 w-5
      translate: 'translate-x-5',
      touchArea: 'min-h-[48px] min-w-[48px] p-2',
    },
    large: {
      track: 'h-8 w-14',
      thumb: 'h-7 w-7', // Increased from h-6 w-6
      translate: 'translate-x-6',
      touchArea: 'min-h-[56px] min-w-[56px] p-3',
    },
  };

  // Variant styles
  const variantClasses = {
    default: {
      track: `
        ${checked ? 'bg-primary-500' : 'bg-gray-300'}
        ${disabled ? 'opacity-50' : 'hover:shadow-md'}
      `,
      thumb: 'bg-white shadow-sm',
    },
    gradient: {
      track: `
        ${checked ? 'bg-gradient-primary' : 'bg-gray-300'}
        ${disabled ? 'opacity-50' : 'hover:shadow-md'}
      `,
      thumb: 'bg-white shadow-sm',
    },
    glass: {
      track: `
        ${checked ? 'bg-glass-primary backdrop-blur-md' : 'bg-glass-white backdrop-blur-md'}
        border ${checked ? 'border-primary-500/30' : 'border-white/30'}
        ${disabled ? 'opacity-50' : 'hover:shadow-glow-primary'}
      `,
      thumb: 'bg-white shadow-lg',
    },
    dark: {
      track: `
        ${checked ? 'bg-gray-900' : 'bg-gray-600'}
        ${disabled ? 'opacity-50' : 'hover:shadow-md'}
      `,
      thumb: 'bg-white shadow-sm',
    },
  };

  const handleChange = () => {
    if (!disabled) {
      onChange(!checked);
    }
  };

  const toggleElement = (
    <button
      type="button"
      role="switch"
      aria-checked={checked}
      disabled={disabled}
      onClick={handleChange}
      className={`
        relative inline-flex items-center justify-center rounded-full
        transition-all duration-300 ease-in-out
        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500
        ${sizeClasses[size].touchArea}
        ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}
      `.trim().replace(/\s+/g, ' ')}
      {...props}
    >
      <div
        className={`
          relative inline-flex items-center rounded-full
          ${sizeClasses[size].track}
          ${variantClasses[variant].track}
        `.trim().replace(/\s+/g, ' ')}
      >
        <span
          className={`
            inline-block transform rounded-full
            transition-all duration-300 ease-in-out
            ${sizeClasses[size].thumb}
            ${variantClasses[variant].thumb}
            ${checked ? sizeClasses[size].translate : 'translate-x-0.5'}
          `.trim().replace(/\s+/g, ' ')}
        />
      </div>
    </button>
  );

  if (!label) {
    return toggleElement;
  }

  return (
    <label
      className={`
        inline-flex items-center gap-3
        ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
        ${className}
      `.trim().replace(/\s+/g, ' ')}
    >
      {labelPosition === 'left' && (
        <span className="text-sm font-medium text-gray-700">{label}</span>
      )}
      {toggleElement}
      {labelPosition === 'right' && (
        <span className="text-sm font-medium text-gray-700">{label}</span>
      )}
    </label>
  );
};

Toggle.propTypes = {
  checked: PropTypes.bool,
  onChange: PropTypes.func.isRequired,
  label: PropTypes.string,
  disabled: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  variant: PropTypes.oneOf(['default', 'gradient', 'glass', 'dark']),
  labelPosition: PropTypes.oneOf(['left', 'right']),
  className: PropTypes.string,
};

export default Toggle;
