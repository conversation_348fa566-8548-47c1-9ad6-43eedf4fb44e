import React from 'react';
import PropTypes from 'prop-types';

/**
 * Modernized Card component with improved design tokens and effects
 */
const Card = ({
  children,
  variant = 'default',
  padding = 'medium',
  elevation = 'medium',
  className = '',
  hover = false,
  interactive = false,
  ...props
}) => {
  const baseClasses = `
    rounded-xl overflow-hidden transition-all duration-300 ease-out
    ${hover || interactive ? 'hover:-translate-y-1 hover:shadow-lg' : ''}
    ${interactive ? 'cursor-pointer transform active:scale-95' : ''}
  `.trim().replace(/\s+/g, ' ');

  const paddingClasses = {
    none: '',
    small: 'p-2', // 8px
    medium: 'p-4',
    large: 'p-6'
  };

  const elevationClasses = {
    none: '',
    low: 'shadow-sm',
    medium: 'shadow-md',
    high: 'shadow-lg',
    glow: 'shadow-glow'
  };

  const variantClasses = {
    default: `
      bg-white border border-gray-200
      hover:shadow-lg
    `.trim().replace(/\s+/g, ' '),

    elevated: `
      bg-gradient-secondary border border-gray-200 shadow-lg
      hover:shadow-xl
    `.trim().replace(/\s+/g, ' '),

    interactive: `
      bg-white border border-gray-200
      hover:bg-gray-100 hover:shadow-md
    `.trim().replace(/\s+/g, ' '),

    glass: `
      bg-glass-white backdrop-blur-lg border border-white/30
      hover:bg-white/30 hover:border-white/40 shadow-lg
    `.trim().replace(/\s+/g, ' '),
  };

  const cardClasses = [
    baseClasses,
    paddingClasses[padding],
    elevationClasses[elevation],
    variantClasses[variant],
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={cardClasses} {...props}>
      {children}
    </div>
  );
};

Card.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf([
    'default',
    'elevated',
    'interactive',
    'glass'
  ]),
  padding: PropTypes.oneOf(['none', 'small', 'medium', 'large']),
  elevation: PropTypes.oneOf(['none', 'low', 'medium', 'high', 'glow']),
  className: PropTypes.string,
  hover: PropTypes.bool,
  interactive: PropTypes.bool,
};

export default Card;
