import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import Icon from './Icon';

/**
 * Modernized Modal component with enhanced animations and styling
 */
const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'medium',
  variant = 'default',
  className = '',
  footer,
  ...props
}) => {
  // Lock body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  // Size variations
  const sizeClasses = {
    small: 'max-w-md',
    medium: 'max-w-lg',
    large: 'max-w-2xl',
    xlarge: 'max-w-4xl',
    full: 'max-w-7xl'
  };

  // Variant styles
  const variantClasses = {
    default: 'bg-white',
    glass: 'bg-glass-white backdrop-blur-xl border border-white/30',
    dark: 'bg-gray-800 text-white',
  };

  return (
    <div
      className="fixed inset-0 z-50 overflow-y-auto"
      aria-labelledby={title ? 'modal-title' : undefined}
      role="dialog"
      aria-modal="true"
      {...props}
    >
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity animate-fade-in"
        aria-hidden="true"
        onClick={onClose}
      />

      {/* Modal container */}
      <div className="flex min-h-screen items-center justify-center p-4 text-center sm:p-0">
        <div
          className={`
            relative transform overflow-hidden rounded-2xl text-left 
            shadow-2xl transition-all animate-slide-up
            sm:my-8 w-full ${sizeClasses[size]} 
            ${variantClasses[variant]}
            ${className}
          `.trim().replace(/\s+/g, ' ')}
        >
          {/* Header */}
          <div className="px-6 pt-6 pb-4 border-b border-gray-200">
            {/* Close button */}
            <button
              type="button"
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
              onClick={onClose}
              aria-label="Close"
            >
              <Icon name="X" size="md" />
            </button>

            {title && (
              <h3 className="text-xl font-semibold leading-6 pr-8" id="modal-title">
                {title}
              </h3>
            )}
          </div>

          {/* Modal content */}
          <div className="px-6 py-4 max-h-[60vh] overflow-y-auto">
            {children}
          </div>

          {/* Footer */}
          {footer && (
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
              {footer}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

Modal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string,
  children: PropTypes.node.isRequired,
  size: PropTypes.oneOf(['small', 'medium', 'large', 'xlarge', 'full']),
  variant: PropTypes.oneOf(['default', 'glass', 'dark']),
  className: PropTypes.string,
  footer: PropTypes.node,
};

export default Modal;
