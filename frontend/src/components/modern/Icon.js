import React from 'react';
import PropTypes from 'prop-types';
import * as LucideIcons from 'lucide-react';

/**
 * Modern Icon component wrapper for Lucide icons
 * Provides consistent sizing, theming, and accessibility features
 * 
 * @example
 * <Icon name="Home" size="md" className="text-primary" />
 * <Icon name="AlertCircle" size="lg" className="text-red-500" decorative={false} label="Warning" />
 * <Icon name="ChevronRight" size="sm" variant="muted" />
 */
const Icon = ({
  name,
  size = 'md',
  className = '',
  color,
  strokeWidth,
  decorative = true,
  label,
  variant,
  animate,
  ...props
}) => {
  // Get the icon component from Lucide
  const IconComponent = LucideIcons[name];

  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in Lucide icons`);
    return null;
  }

  // Size mappings with more granular control
  const sizeMap = {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 32,
    '2xl': 40,
    '3xl': 48,
    '4xl': 64,
  };

  const iconSize = typeof size === 'number' ? size : sizeMap[size] || sizeMap.md;

  // Variant color mappings for quick theming
  const variantClasses = {
    primary: 'text-primary',
    secondary: 'text-secondary-lavender',
    success: 'text-emotion-positive',
    warning: 'text-accent-yellow',
    danger: 'text-emotion-negative',
    info: 'text-blue-500',
    muted: 'text-gray-400',
    white: 'text-white',
  };

  // Animation classes
  const animationClasses = {
    spin: 'animate-spin',
    pulse: 'animate-pulse',
    bounce: 'animate-bounce',
    ping: 'animate-ping',
  };

  // Build className
  const iconClasses = [
    variant && variantClasses[variant],
    animate && animationClasses[animate],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  // Accessibility props
  const a11yProps = decorative
    ? { 'aria-hidden': true }
    : {
        role: 'img',
        'aria-label': label || name.replace(/([A-Z])/g, ' $1').trim(),
      };

  return (
    <IconComponent
      size={iconSize}
      className={iconClasses}
      color={color}
      strokeWidth={strokeWidth}
      {...a11yProps}
      {...props}
    />
  );
};

Icon.propTypes = {
  /** Name of the Lucide icon (e.g., 'Home', 'User', 'Settings') */
  name: PropTypes.string.isRequired,
  /** Predefined size or custom number */
  size: PropTypes.oneOfType([
    PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl', '4xl']),
    PropTypes.number,
  ]),
  /** Additional CSS classes */
  className: PropTypes.string,
  /** Icon color (overrides variant and className) */
  color: PropTypes.string,
  /** Stroke width for the icon */
  strokeWidth: PropTypes.number,
  /** Whether the icon is purely decorative (true) or informative (false) */
  decorative: PropTypes.bool,
  /** Accessibility label for informative icons */
  label: PropTypes.string,
  /** Predefined color variant */
  variant: PropTypes.oneOf([
    'primary',
    'secondary',
    'success',
    'warning',
    'danger',
    'info',
    'muted',
    'white',
  ]),
  /** Animation preset */
  animate: PropTypes.oneOf(['spin', 'pulse', 'bounce', 'ping']),
};

export default Icon;
