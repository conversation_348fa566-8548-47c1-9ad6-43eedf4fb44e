import React from 'react';
import PropTypes from 'prop-types';

const Button = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  ripple = true,
  onClick,
  className = '',
  ...props
}) => {
  // Variant styles
  const variantStyles = {
    primary: 'bg-primary text-white hover:bg-primary-hover active:bg-primary-active disabled:bg-primary-disabled',
    secondary: 'bg-secondary-lavender text-white hover:bg-secondary-lavender-hover active:bg-secondary-lavender-active',
    outline: 'border-2 border-primary text-primary hover:bg-primary hover:text-white',
    ghost: 'text-primary hover:bg-primary/10 active:bg-primary/20',
    danger: 'bg-state-error text-white hover:bg-state-error-dark active:bg-state-error-dark',
  };

  // Size styles
  const sizeStyles = {
    small: 'px-3 py-1.5 text-sm',
    medium: 'px-4 py-2 text-base',
    large: 'px-6 py-3 text-lg',
  };

  // Base button classes with motion
  const baseClasses = `
    relative inline-flex items-center justify-center
    font-medium rounded-lg
    transition-standard
    hover-lift active-scale
    focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary
    disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:transform-none
    ${ripple && !disabled ? 'ripple' : ''}
    ${variantStyles[variant]}
    ${sizeStyles[size]}
    ${className}
  `;

  return (
    <button
      className={baseClasses}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {/* Loading spinner */}
      {loading && (
        <span className="absolute inset-0 flex items-center justify-center">
          <span className="spinner text-current" />
        </span>
      )}
      
      {/* Button content */}
      <span className={`${loading ? 'opacity-0' : ''} flex items-center gap-2`}>
        {children}
      </span>
    </button>
  );
};

Button.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf(['primary', 'secondary', 'outline', 'ghost', 'danger']),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  ripple: PropTypes.bool,
  onClick: PropTypes.func,
  className: PropTypes.string,
};

export default Button;
