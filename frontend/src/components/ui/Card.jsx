import React from 'react';
import PropTypes from 'prop-types';

const Card = ({
  children,
  variant = 'elevated',
  interactive = false,
  onClick,
  className = '',
  ...props
}) => {
  // Variant styles
  const variantStyles = {
    elevated: 'bg-background-elevated shadow-elevated',
    outlined: 'border border-border-primary',
    filled: 'bg-background-secondary',
    glass: 'bg-glass-white backdrop-blur-md border border-glass-white-20',
  };

  // Interactive styles
  const interactiveStyles = interactive ? `
    cursor-pointer
    transition-standard
    hover-lift hover:shadow-floating
    active-scale
    focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary
  ` : '';

  const baseClasses = `
    relative rounded-xl p-6
    ${variantStyles[variant]}
    ${interactiveStyles}
    ${className}
  `;

  const Component = onClick ? 'button' : 'div';

  return (
    <Component
      className={baseClasses}
      onClick={onClick}
      {...props}
    >
      <div className="relative z-10">
        {children}
      </div>
      
      {/* Optional animated gradient border */}
      {variant === 'glass' && (
        <div className="absolute inset-0 rounded-xl bg-gradient-primary opacity-20 blur-xl animate-pulse-opacity" />
      )}
    </Component>
  );
};

Card.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf(['elevated', 'outlined', 'filled', 'glass']),
  interactive: PropTypes.bool,
  onClick: PropTypes.func,
  className: PropTypes.string,
};

export default Card;
