import React from 'react';
import PropTypes from 'prop-types';

/**
 * Enhanced <PERSON>ton component with modern animations and styles
 */
const Button = ({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  fullWidth = false,
  className = '',
  onClick,
  type = 'button',
  loading = false,
  icon = null,
  ...props
}) => {
  // Enhanced base classes with consistent behavior and improved focus indicators
  const baseClasses = `
    inline-flex items-center justify-center rounded-lg font-medium
    transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-opacity-75
    focus-visible:ring-4 focus-visible:ring-offset-2
    active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed
    disabled:transform-none disabled:focus:ring-0
    ${loading ? 'cursor-wait' : ''}
  `.trim().replace(/\s+/g, ' ');

  // Improved size variations with 48px minimum touch targets
  const sizeClasses = {
    small: 'px-4 py-3 text-sm min-h-[48px]',
    medium: 'px-5 py-3.5 text-base min-h-[48px]',
    large: 'px-6 py-4 text-lg min-h-[56px]'
  };

  // Enhanced variant system with semantic color tokens and improved accessibility
  const variantClasses = {
    primary: `
      surface-primary bg-primary hover:bg-primary-hover text-inverse border-0
      focus:ring-primary focus-visible:ring-primary shadow-sm hover:shadow-md
    `.trim().replace(/\s+/g, ' '),

    secondary: `
      surface-primary border border-default text-primary
      hover:surface-secondary hover:border-medium
      focus:ring-primary focus-visible:ring-primary shadow-sm hover:shadow-md
    `.trim().replace(/\s+/g, ' '),

    ghost: `
      bg-transparent text-primary hover:surface-secondary
      border-0 focus:ring-primary focus-visible:ring-primary
    `.trim().replace(/\s+/g, ' '),

    danger: `
      bg-error hover:bg-error-hover text-inverse border-0
      focus:ring-error focus-visible:ring-error shadow-sm hover:shadow-md
    `.trim().replace(/\s+/g, ' '),

    glass: `
      glass-surface backdrop-blur-md border
      hover:glass-surface:hover shadow-lg
      focus:border-focus focus-visible:border-focus
    `.trim().replace(/\s+/g, ' '),
  };

  // Combine all classes
  const buttonClasses = [
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    fullWidth ? 'w-full' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <button
      type={type}
      className={buttonClasses}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      <div className={`flex items-center justify-center gap-2 ${loading ? 'opacity-0' : ''}`}>
        {icon && <span className="flex-shrink-0">{icon}</span>}
        {children}
      </div>
    </button>
  );
};

Button.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf([
    'primary',
    'secondary',
    'ghost',
    'danger',
    'glass'
  ]),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  disabled: PropTypes.bool,
  fullWidth: PropTypes.bool,
  className: PropTypes.string,
  onClick: PropTypes.func,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  loading: PropTypes.bool,
  icon: PropTypes.node,
};

export default Button;