import React from 'react';
import { motion } from 'framer-motion';

const Stepper = ({ steps, currentStep, onStepClick, orientation = 'horizontal' }) => {
  const isHorizontal = orientation === 'horizontal';
  
  return (
    <div className={`stepper ${isHorizontal ? 'flex items-center justify-between' : 'flex flex-col space-y-4'} w-full`}>
      {steps.map((step, index) => {
        const isActive = index === currentStep;
        const isCompleted = index < currentStep;
        const isClickable = onStepClick && (isCompleted || index === currentStep);
        
        return (
          <div
            key={index}
            className={`stepper-item ${isHorizontal ? 'flex-1' : 'w-full'} ${index < steps.length - 1 ? (isHorizontal ? 'flex items-center' : '') : ''}`}
          >
            <div className={`flex items-center ${isHorizontal ? '' : 'mb-2'}`}>
              <motion.button
                onClick={() => isClickable && onStepClick(index)}
                disabled={!isClickable}
                className={`stepper-circle relative z-10 w-12 h-12 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${
                  isClickable ? 'cursor-pointer' : 'cursor-default'
                } ${
                  isActive
                    ? 'bg-gradient-to-r from-primary to-primary-dark text-white shadow-glow ring-4 ring-primary/30'
                    : isCompleted
                    ? 'bg-primary text-white hover:shadow-glow'
                    : 'bg-surface border-2 border-surface-light text-text-secondary'
                }`}
                whileHover={isClickable ? { scale: 1.1 } : {}}
                whileTap={isClickable ? { scale: 0.95 } : {}}
              >
                {isCompleted ? (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  index + 1
                )}
              </motion.button>
              
              {isHorizontal && index < steps.length - 1 && (
                <div className="flex-1 h-0.5 mx-4 relative">
                  <div className="absolute inset-0 bg-surface-light rounded-full"></div>
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary to-primary-dark rounded-full"
                    initial={{ width: '0%' }}
                    animate={{ width: isCompleted ? '100%' : '0%' }}
                    transition={{ duration: 0.5, ease: 'easeInOut' }}
                  />
                </div>
              )}
            </div>
            
            <div className={`${isHorizontal ? 'text-center mt-2' : 'ml-16'}`}>
              <h3 className={`font-semibold text-sm ${
                isActive ? 'text-primary' : isCompleted ? 'text-text-primary' : 'text-text-secondary'
              }`}>
                {step.title}
              </h3>
              {step.description && (
                <p className="text-xs text-text-secondary mt-1">{step.description}</p>
              )}
            </div>
            
            {!isHorizontal && index < steps.length - 1 && (
              <div className="ml-6 w-0.5 h-8 relative">
                <div className="absolute inset-0 bg-surface-light rounded-full"></div>
                <motion.div
                  className="absolute inset-0 bg-gradient-to-b from-primary to-primary-dark rounded-full"
                  initial={{ height: '0%' }}
                  animate={{ height: isCompleted ? '100%' : '0%' }}
                  transition={{ duration: 0.5, ease: 'easeInOut' }}
                />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default Stepper;
