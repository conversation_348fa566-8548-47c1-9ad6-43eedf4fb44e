import React from 'react';
import PropTypes from 'prop-types';

/**
 * Layout component that implements mobile-first principles
 */
const Layout = ({
  children,
  className = '',
  ...props
}) => {
  return (
    <div 
      className={`
        w-full mx-auto px-4 
        sm:px-6 
        md:px-8 
        lg:px-12 
        xl:px-16 
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
};

Layout.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
};

/**
 * Grid component for responsive grid layouts
 */
export const Grid = ({
  children,
  cols = 1,
  gap = 'medium',
  className = '',
  ...props
}) => {
  // Gap variations aligned with 8pt spacing system
  const gapClasses = {
    none: 'gap-0',
    small: 'gap-2',     // 8px
    medium: 'gap-4 sm:gap-6',    // 16px → 24px
    large: 'gap-6 sm:gap-8',     // 24px → 32px
    xlarge: 'gap-8 sm:gap-12'    // 32px → 48px
  };
  
  // Responsive column variations - mobile first approach
  // Start with 1 column on mobile, then increase based on cols prop
  const colClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
    6: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6',
  };
  
  return (
    <div 
      className={`
        grid ${colClasses[cols]} ${gapClasses[gap]} ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
};

Grid.propTypes = {
  children: PropTypes.node.isRequired,
  cols: PropTypes.oneOf([1, 2, 3, 4, 5, 6]),
  gap: PropTypes.oneOf(['none', 'small', 'medium', 'large']),
  className: PropTypes.string,
};

/**
 * Flex component for responsive flex layouts
 */
export const Flex = ({
  children,
  direction = 'row',
  wrap = false,
  justify = 'start',
  items = 'start',
  gap = 'medium',
  className = '',
  ...props
}) => {
  // Direction variations with mobile-first approach
  const directionClasses = {
    row: 'flex-col sm:flex-row',
    column: 'flex-col',
    'row-reverse': 'flex-col-reverse sm:flex-row-reverse',
    'column-reverse': 'flex-col-reverse',
  };
  
  // Wrap variations
  const wrapClasses = wrap ? 'flex-wrap' : 'flex-nowrap';
  
  // Justify variations
  const justifyClasses = {
    start: 'justify-start',
    end: 'justify-end',
    center: 'justify-center',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };
  
  // Items variations
  const itemsClasses = {
    start: 'items-start',
    end: 'items-end',
    center: 'items-center',
    baseline: 'items-baseline',
    stretch: 'items-stretch',
  };
  
  // Gap variations aligned with 8pt spacing system
  const gapClasses = {
    none: 'gap-0',
    small: 'gap-2',              // 8px
    medium: 'gap-4 sm:gap-6',    // 16px → 24px
    large: 'gap-6 sm:gap-8',     // 24px → 32px
    xlarge: 'gap-8 sm:gap-12'    // 32px → 48px
  };
  
  return (
    <div 
      className={`
        flex ${directionClasses[direction]} ${wrapClasses} 
        ${justifyClasses[justify]} ${itemsClasses[items]} 
        ${gapClasses[gap]} ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
};

Flex.propTypes = {
  children: PropTypes.node.isRequired,
  direction: PropTypes.oneOf(['row', 'column', 'row-reverse', 'column-reverse']),
  wrap: PropTypes.bool,
  justify: PropTypes.oneOf(['start', 'end', 'center', 'between', 'around', 'evenly']),
  items: PropTypes.oneOf(['start', 'end', 'center', 'baseline', 'stretch']),
  gap: PropTypes.oneOf(['none', 'small', 'medium', 'large']),
  className: PropTypes.string,
};

/**
 * Responsive component that shows different content based on screen size
 */
export const Responsive = ({
  children,
  showOn = 'all',
  hideOn = 'none',
  className = '',
  ...props
}) => {
  // Visibility classes based on screen size
  const showClasses = {
    all: '',
    mobile: 'hidden sm:block',
    tablet: 'hidden md:block',
    desktop: 'hidden lg:block',
    wide: 'hidden xl:block',
  };
  
  const hideClasses = {
    none: '',
    mobile: 'sm:hidden',
    tablet: 'md:hidden',
    desktop: 'lg:hidden',
    wide: 'xl:hidden',
  };
  
  return (
    <div 
      className={`
        ${showClasses[showOn]} ${hideClasses[hideOn]} ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
};

Responsive.propTypes = {
  children: PropTypes.node.isRequired,
  showOn: PropTypes.oneOf(['all', 'mobile', 'tablet', 'desktop', 'wide']),
  hideOn: PropTypes.oneOf(['none', 'mobile', 'tablet', 'desktop', 'wide']),
  className: PropTypes.string,
};

export default Layout;