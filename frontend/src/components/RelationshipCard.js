import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Card from './Card';

const TabButton = ({ label, isActive, onClick, icon }) => (
  <motion.button
    className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 flex items-center gap-2 ${
      isActive
        ? 'bg-primary text-white shadow-soft'
        : 'text-text-secondary hover:text-text-primary hover:bg-surface-light'
    }`}
    onClick={onClick}
    whileHover={{ scale: 1.05 }}
    whileTap={{ scale: 0.95 }}
  >
    {icon && <span className="w-4 h-4">{icon}</span>}
    {label}
  </motion.button>
);

const RelationshipCard = ({ relationship, onAnalyze, onEdit, onDelete }) => {
  const [activeTab, setActiveTab] = useState('overview');
  
  const tabs = [
    { 
      id: 'overview', 
      label: 'Overview',
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      )
    },
    { 
      id: 'history', 
      label: 'History',
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    { 
      id: 'insights', 
      label: 'Insights',
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      )
    },
    { 
      id: 'actions', 
      label: 'Actions',
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      )
    }
  ];
  
  const getHealthColor = (score) => {
    if (score > 75) return 'from-emotion-positive to-emotion-positive/80';
    if (score > 50) return 'from-secondary to-secondary/80';
    return 'from-emotion-negative to-emotion-negative/80';
  };
  
  const getSentimentIcon = (sentiment) => {
    switch (sentiment) {
      case 'positive':
        return '😊';
      case 'negative':
        return '😔';
      default:
        return '😐';
    }
  };
  
  return (
    <Card variant="glass" hover="lift" className="relationship-card">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <motion.div
            className={`w-16 h-16 rounded-full flex items-center justify-center font-bold text-white shadow-soft bg-gradient-to-br ${getHealthColor(relationship.health_score)}`}
            whileHover={{ scale: 1.1, rotate: 5 }}
          >
            {relationship.health_score}
          </motion.div>
          <div>
            <h3 className="text-xl font-semibold text-text-primary">{relationship.name}</h3>
            <p className="text-text-secondary">{relationship.type}</p>
          </div>
        </div>
        <div className="text-2xl">{getSentimentIcon(relationship.sentiment)}</div>
      </div>
      
      {/* Tabs */}
      <div className="flex gap-2 mb-6 overflow-x-auto pb-2">
        {tabs.map((tab) => (
          <TabButton
            key={tab.id}
            label={tab.label}
            icon={tab.icon}
            isActive={activeTab === tab.id}
            onClick={() => setActiveTab(tab.id)}
          />
        ))}
      </div>
      
      {/* Tab Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
          className="tab-content"
        >
          {activeTab === 'overview' && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Card variant="glass" className="bg-primary/5 border-primary/20">
                  <h4 className="text-sm font-medium text-text-secondary mb-1">Last Contact</h4>
                  <p className="text-lg font-semibold text-primary">
                    {new Date(relationship.last_contact).toLocaleDateString()}
                  </p>
                </Card>
                <Card variant="glass" className="bg-secondary/5 border-secondary/20">
                  <h4 className="text-sm font-medium text-text-secondary mb-1">Total Analyses</h4>
                  <p className="text-lg font-semibold text-secondary">
                    {relationship.analysis_count || 0}
                  </p>
                </Card>
              </div>
              
              {relationship.notes && (
                <Card variant="glass" className="bg-surface/50">
                  <h4 className="text-sm font-medium text-text-secondary mb-2">Notes</h4>
                  <p className="text-text-primary">{relationship.notes}</p>
                </Card>
              )}
              
              {/* Recent Flags */}
              {relationship.recent_flags && relationship.recent_flags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-text-secondary mb-2">Recent Flags</h4>
                  <div className="flex flex-wrap gap-2">
                    {relationship.recent_flags.map((flag, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-emotion-negative/20 text-emotion-negative text-xs rounded-full border border-emotion-negative/30"
                      >
                        {flag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          
          {activeTab === 'history' && (
            <div className="space-y-3">
              <p className="text-text-secondary text-sm mb-4">
                Communication history and analysis timeline
              </p>
              {relationship.history && relationship.history.length > 0 ? (
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {relationship.history.map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-start gap-3"
                    >
                      <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                      <div className="flex-1">
                        <p className="text-sm text-text-primary">{item.summary}</p>
                        <p className="text-xs text-text-secondary mt-1">
                          {new Date(item.date).toLocaleDateString()}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <Card variant="glass" className="bg-surface/20 text-center py-8">
                  <p className="text-text-secondary">No history available yet</p>
                </Card>
              )}
            </div>
          )}
          
          {activeTab === 'insights' && (
            <div className="space-y-4">
              <Card variant="glass" className="bg-primary/5 border-primary/20">
                <h4 className="text-sm font-medium text-text-secondary mb-2">Communication Pattern</h4>
                <p className="text-text-primary">
                  {relationship.insights?.pattern || 'Analyzing communication patterns...'}
                </p>
              </Card>
              
              <Card variant="glass" className="bg-secondary/5 border-secondary/20">
                <h4 className="text-sm font-medium text-text-secondary mb-2">Suggested Actions</h4>
                <ul className="space-y-2">
                  {(relationship.insights?.suggestions || ['Continue monitoring conversations']).map((suggestion, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-1.5 h-1.5 bg-secondary rounded-full mt-1.5 flex-shrink-0"></div>
                      <span className="text-sm text-text-primary">{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </Card>
            </div>
          )}
          
          {activeTab === 'actions' && (
            <div className="space-y-3">
              <button
                onClick={() => onAnalyze(relationship)}
                className="w-full px-4 py-3 bg-gradient-to-r from-primary to-primary-dark text-white rounded-lg font-medium hover:shadow-glow transition-all duration-300 flex items-center justify-center gap-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Quick Analyze
              </button>
              
              <button
                onClick={() => onEdit(relationship)}
                className="w-full px-4 py-3 bg-surface border border-surface-light text-text-primary rounded-lg font-medium hover:bg-surface-light transition-all duration-300 flex items-center justify-center gap-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Details
              </button>
              
              <button
                onClick={() => onDelete(relationship)}
                className="w-full px-4 py-3 bg-emotion-negative/10 border border-emotion-negative/30 text-emotion-negative rounded-lg font-medium hover:bg-emotion-negative/20 transition-all duration-300 flex items-center justify-center gap-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Delete
              </button>
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    </Card>
  );
};

export default RelationshipCard;
