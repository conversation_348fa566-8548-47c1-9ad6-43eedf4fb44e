import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';

// Route configuration for breadcrumb generation
const routeConfig = {
  '/': { label: 'Home', icon: HomeIcon },
  '/dashboard': { label: 'Dashboard', parent: '/' },
  '/message-analyzer': { label: 'Message Analyzer', parent: '/' },
  '/relationships': { label: 'Relationships', parent: '/' },
  '/growth-center': { label: 'Growth Center', parent: '/' },
  '/reports': { label: 'Reports', parent: '/' },
  '/anomaly-detection': { label: 'Anomaly Detection', parent: '/reports' },
  '/typography': { label: 'Typography', parent: '/components' },
  '/contrast-test': { label: 'Contrast Test', parent: '/components' },
  '/components': { label: 'Components', parent: '/' },
  '/colors': { label: 'Colors', parent: '/components' },
  '/mobile-test': { label: 'Mobile Test', parent: '/components' },
  '/dashboard-showcase': { label: 'Dashboard Showcase', parent: '/components' },
  '/test': { label: 'Test Page', parent: '/components' }
};

const Breadcrumb = ({ className = '', showHome = true }) => {
  const location = useLocation();
  const currentPath = location.pathname;

  // Generate breadcrumb trail
  const generateBreadcrumbs = (path) => {
    const breadcrumbs = [];
    let currentRoute = routeConfig[path];
    let currentPath = path;

    // Build breadcrumb trail by following parent relationships
    while (currentRoute) {
      breadcrumbs.unshift({
        path: currentPath,
        label: currentRoute.label,
        icon: currentRoute.icon
      });

      if (currentRoute.parent && currentRoute.parent !== currentPath) {
        currentPath = currentRoute.parent;
        currentRoute = routeConfig[currentPath];
      } else {
        break;
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs(currentPath);

  // Don't show breadcrumbs on home page unless explicitly requested
  if (currentPath === '/' && !showHome) {
    return null;
  }

  // Don't show if no route configuration found
  if (breadcrumbs.length === 0) {
    return null;
  }

  return (
    <nav 
      className={`flex items-center space-x-2 text-sm ${className}`}
      aria-label="Breadcrumb navigation"
    >
      <ol className="flex items-center space-x-2">
        {breadcrumbs.map((crumb, index) => {
          const isLast = index === breadcrumbs.length - 1;
          const Icon = crumb.icon;

          return (
            <li key={crumb.path} className="flex items-center">
              {index > 0 && (
                <ChevronRightIcon 
                  className="w-4 h-4 text-text-tertiary mx-2" 
                  aria-hidden="true"
                />
              )}
              
              {isLast ? (
                <span 
                  className="flex items-center text-text-primary font-medium"
                  aria-current="page"
                >
                  {Icon && <Icon className="w-4 h-4 mr-1.5" aria-hidden="true" />}
                  {crumb.label}
                </span>
              ) : (
                <Link
                  to={crumb.path}
                  className="flex items-center text-text-secondary hover:text-primary transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-md px-1 py-0.5"
                >
                  {Icon && <Icon className="w-4 h-4 mr-1.5" aria-hidden="true" />}
                  {crumb.label}
                </Link>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default Breadcrumb;
