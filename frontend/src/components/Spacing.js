import React from 'react';
import PropTypes from 'prop-types';

/**
 * Spacer component for consistent vertical/horizontal spacing
 * Based on 8pt spacing system
 */
export const Spacer = ({ 
  size = 'medium',
  direction = 'vertical',
  className = '',
  ...props 
}) => {
  // Spacing sizes aligned with 8pt system
  const sizes = {
    xs: 'h-1 w-1',      // 4px
    small: 'h-2 w-2',   // 8px
    medium: 'h-4 w-4',  // 16px
    large: 'h-6 w-6',   // 24px
    xl: 'h-8 w-8',      // 32px
    '2xl': 'h-12 w-12', // 48px
    '3xl': 'h-16 w-16', // 64px
  };

  const spacerClasses = direction === 'vertical' 
    ? `${sizes[size].split(' ')[0]} w-full`
    : `${sizes[size].split(' ')[1]} h-full`;

  return <div className={`${spacerClasses} ${className}`} {...props} />;
};

Spacer.propTypes = {
  size: PropTypes.oneOf(['xs', 'small', 'medium', 'large', 'xl', '2xl', '3xl']),
  direction: PropTypes.oneOf(['vertical', 'horizontal']),
  className: PropTypes.string,
};

/**
 * Stack component for consistent vertical spacing between children
 */
export const Stack = ({ 
  children,
  spacing = 'medium',
  align = 'stretch',
  className = '',
  ...props 
}) => {
  // Stack spacing using Tailwind's space utilities
  const spacingClasses = {
    none: 'space-y-0',
    xs: 'space-y-1',     // 4px
    small: 'space-y-2',  // 8px
    medium: 'space-y-4', // 16px
    large: 'space-y-6',  // 24px
    xl: 'space-y-8',     // 32px
    '2xl': 'space-y-12', // 48px
    '3xl': 'space-y-16', // 64px
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  return (
    <div 
      className={`flex flex-col ${spacingClasses[spacing]} ${alignClasses[align]} ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};

Stack.propTypes = {
  children: PropTypes.node.isRequired,
  spacing: PropTypes.oneOf(['none', 'xs', 'small', 'medium', 'large', 'xl', '2xl', '3xl']),
  align: PropTypes.oneOf(['start', 'center', 'end', 'stretch']),
  className: PropTypes.string,
};

/**
 * Inline component for consistent horizontal spacing between children
 */
export const Inline = ({ 
  children,
  spacing = 'medium',
  align = 'center',
  wrap = true,
  className = '',
  ...props 
}) => {
  // Inline spacing using Tailwind's space utilities
  const spacingClasses = {
    none: 'space-x-0',
    xs: 'space-x-1',     // 4px
    small: 'space-x-2',  // 8px
    medium: 'space-x-4', // 16px
    large: 'space-x-6',  // 24px
    xl: 'space-x-8',     // 32px
    '2xl': 'space-x-12', // 48px
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    baseline: 'items-baseline',
    stretch: 'items-stretch',
  };

  const wrapClass = wrap ? 'flex-wrap' : '';

  // When wrapping, we need gap instead of space-x for proper spacing
  const gapClasses = {
    none: 'gap-0',
    xs: 'gap-1',     // 4px
    small: 'gap-2',  // 8px
    medium: 'gap-4', // 16px
    large: 'gap-6',  // 24px
    xl: 'gap-8',     // 32px
    '2xl': 'gap-12', // 48px
  };

  return (
    <div 
      className={`flex ${wrap ? gapClasses[spacing] : spacingClasses[spacing]} ${alignClasses[align]} ${wrapClass} ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};

Inline.propTypes = {
  children: PropTypes.node.isRequired,
  spacing: PropTypes.oneOf(['none', 'xs', 'small', 'medium', 'large', 'xl', '2xl']),
  align: PropTypes.oneOf(['start', 'center', 'end', 'baseline', 'stretch']),
  wrap: PropTypes.bool,
  className: PropTypes.string,
};

/**
 * Box component with consistent padding
 */
export const Box = ({ 
  children,
  padding = 'medium',
  className = '',
  ...props 
}) => {
  // Padding sizes aligned with 8pt system
  const paddingClasses = {
    none: 'p-0',
    xs: 'p-1',       // 4px
    small: 'p-2',    // 8px
    medium: 'p-4',   // 16px
    large: 'p-6',    // 24px
    xl: 'p-8',       // 32px
    '2xl': 'p-12',   // 48px
    '3xl': 'p-16',   // 64px
  };

  return (
    <div className={`${paddingClasses[padding]} ${className}`} {...props}>
      {children}
    </div>
  );
};

Box.propTypes = {
  children: PropTypes.node.isRequired,
  padding: PropTypes.oneOf(['none', 'xs', 'small', 'medium', 'large', 'xl', '2xl', '3xl']),
  className: PropTypes.string,
};

/**
 * Center component for centering content
 */
export const Center = ({ 
  children,
  maxWidth = 'default',
  className = '',
  ...props 
}) => {
  const maxWidthClasses = {
    small: 'max-w-md',    // 448px
    medium: 'max-w-lg',   // 512px
    default: 'max-w-2xl', // 672px
    large: 'max-w-4xl',   // 896px
    xl: 'max-w-6xl',      // 1152px
    full: 'max-w-full',
  };

  return (
    <div className={`mx-auto ${maxWidthClasses[maxWidth]} ${className}`} {...props}>
      {children}
    </div>
  );
};

Center.propTypes = {
  children: PropTypes.node.isRequired,
  maxWidth: PropTypes.oneOf(['small', 'medium', 'default', 'large', 'xl', 'full']),
  className: PropTypes.string,
};

// Export all components
export default {
  Spacer,
  Stack,
  Inline,
  Box,
  Center,
};
