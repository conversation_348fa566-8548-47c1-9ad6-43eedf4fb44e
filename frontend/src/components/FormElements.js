import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Icon from './Icon';

/**
 * Enhanced Input component with floating labels and modern animations
 */
export const Input = ({
  id,
  label,
  type = 'text',
  placeholder,
  value,
  onChange,
  disabled = false,
  required = false,
  error,
  className = '',
  variant = 'default',
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const hasValue = value && value.length > 0;
  const isFloating = isFocused || hasValue;

  const handleFocus = (e) => {
    setIsFocused(true);
    if (props.onFocus) props.onFocus(e);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    if (props.onBlur) props.onBlur(e);
  };

  // Variant styles
  const variantClasses = {
    default: `
      bg-white border border-border-light
      focus:border-primary focus:ring-primary/20
    `,
    glass: `
      bg-glass-white backdrop-blur-md border border-white/30
      focus:border-primary focus:ring-primary/20 focus:bg-white/80
    `,
    floating: `
      bg-white border border-border-light
      focus:border-primary focus:ring-primary/20
    `
  };

  return (
    <div className="mb-4">
      <div className="relative">
        <input
          id={id}
          type={type}
          value={value}
          onChange={onChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          required={required}
          placeholder={variant === 'floating' ? '' : placeholder}
          className={`
            w-full px-4 py-2 rounded-xl transition-all duration-300 ease-out
            ${variantClasses[variant]}
            ${error ? 'border-error ring-error/20 bg-error-subtle' : ''}
            ${disabled ? 'bg-gray-100 opacity-60 cursor-not-allowed' : ''}
            ${isFocused ? 'transform scale-[1.02] shadow-md' : 'shadow-sm'}
            focus:outline-none focus:ring-2
            ${error ? 'pr-10' : ''}
            ${className}
          `.trim().replace(/\s+/g, ' ')}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? `${id}-error` : undefined}
          {...props}
        />

        {/* Error Icon */}
        {error && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <Icon
              name="AlertCircle"
              size="sm"
              className="text-error"
              decorative={false}
              label="Error"
            />
          </div>
        )}

        {/* Floating Label */}
        {label && variant === 'floating' && (
          <label
            htmlFor={id}
            className={`
              absolute left-4 transition-all duration-300 ease-out pointer-events-none
              ${isFloating 
                ? 'top-2 text-xs text-primary font-medium' 
                : 'top-3 text-base text-gray-500'
              }
              ${error ? 'text-error' : ''}
            `.trim().replace(/\s+/g, ' ')}
          >
            {label}
            {required && <span className="text-error ml-1" aria-label="required">*</span>}
          </label>
        )}
        
        {/* Traditional Label */}
        {label && variant !== 'floating' && (
          <label 
            htmlFor={id} 
            className="block text-sm font-medium text-text-primary mb-2"
          >
            {label}
            {required && <span className="text-error ml-1" aria-label="required">*</span>}
          </label>
        )}
      </div>
      {error && (
        <div id={`${id}-error`} className="mt-2 flex items-start gap-2">
          <Icon
            name="AlertTriangle"
            size="xs"
            className="text-error mt-0.5 flex-shrink-0"
            decorative={false}
            label="Error"
          />
          <p className="text-sm text-error font-medium">
            {error}
          </p>
        </div>
      )}
    </div>
  );
};

Input.propTypes = {
  id: PropTypes.string.isRequired,
  label: PropTypes.string,
  type: PropTypes.string,
  placeholder: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  error: PropTypes.string,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'glass', 'floating']),
};

/**
 * Enhanced Select component with modern styling and animations
 */
export const Select = ({
  id,
  label,
  options,
  value,
  onChange,
  disabled = false,
  required = false,
  error,
  className = '',
  variant = 'default',
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = (e) => {
    setIsFocused(true);
    if (props.onFocus) props.onFocus(e);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    if (props.onBlur) props.onBlur(e);
  };

  // Variant styles
  const variantClasses = {
    default: `
      bg-white border border-border-light
      focus:border-primary focus:ring-primary/20
    `,
    glass: `
      bg-glass-white backdrop-blur-md border border-white/30
      focus:border-primary focus:ring-primary/20 focus:bg-white/80
    `,
    modern: `
      bg-gradient-to-r from-white to-background-subtle border border-border-light
      focus:border-primary focus:ring-primary/20 focus:from-white focus:to-white
    `
  };

  return (
    <div className="mb-4">
      {label && (
        <label 
          htmlFor={id} 
          className="block text-sm font-semibold text-text-primary mb-2"
        >
          {label}
          {required && <span className="text-emotion-negative ml-1">*</span>}
        </label>
      )}
      <div className="relative group">
        <select
          id={id}
          value={value}
          onChange={onChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          required={required}
          className={`
            appearance-none w-full px-4 py-2 rounded-xl pr-12 transition-all duration-300 ease-out
            ${variantClasses[variant]}
            ${error ? 'border-emotion-negative ring-emotion-negative/20' : ''}
            ${disabled ? 'bg-background-subtle opacity-60 cursor-not-allowed' : ''}
            ${isFocused ? 'transform scale-[1.02] shadow-elevated' : 'shadow-soft'}
            focus:outline-none focus:ring-2
            ${className}
          `.trim().replace(/\s+/g, ' ')}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? `${id}-error` : undefined}
          {...props}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        <div className={`
          pointer-events-none absolute inset-y-0 right-0 flex items-center px-4 
          transition-all duration-300 ease-out
          ${isFocused ? 'text-primary rotate-180' : 'text-text-secondary'}
        `}>
          <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      </div>
      {error && (
        <p id={`${id}-error`} className="mt-2 text-sm text-emotion-negative flex items-center gap-1">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
};

Select.propTypes = {
  id: PropTypes.string.isRequired,
  label: PropTypes.string,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      label: PropTypes.string.isRequired,
    })
  ).isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  error: PropTypes.string,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'glass', 'modern']),
};

/**
 * Enhanced Checkbox component with modern styling and animations
 */
export const Checkbox = ({
  id,
  label,
  checked,
  onChange,
  disabled = false,
  required = false,
  error,
  className = '',
  variant = 'default',
  ...props
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Variant styles
  const variantClasses = {
    default: `
      border-border-light text-primary
      focus:ring-primary/50 checked:bg-primary checked:border-primary
    `,
    glass: `
      border-white/30 bg-glass-white backdrop-blur-sm text-primary
      focus:ring-primary/50 checked:bg-primary/90 checked:border-primary
    `,
    modern: `
      border-border-light text-primary
      focus:ring-primary/50 checked:bg-gradient-to-br checked:from-primary checked:to-primary-dark
      checked:border-primary-dark
    `
  };

  return (
    <div className="mb-4">
      <div 
        className="flex items-start group"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex items-center h-5">
          <input
            id={id}
            type="checkbox"
            checked={checked}
            onChange={onChange}
            disabled={disabled}
            required={required}
            className={`
              h-5 w-5 rounded-lg transition-all duration-300 ease-out
              ${variantClasses[variant]}
              ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
              ${error ? 'border-emotion-negative' : ''}
              ${isHovered && !disabled ? 'scale-110 shadow-soft' : ''}
              focus:ring-2 focus:ring-offset-2
              ${className}
            `.trim().replace(/\s+/g, ' ')}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={error ? `${id}-error` : undefined}
            {...props}
          />
        </div>
        <div className="ml-3 text-sm">
          {label && (
            <label 
              htmlFor={id} 
              className={`
                font-medium cursor-pointer transition-all duration-200
                ${disabled ? 'text-text-secondary' : 'text-text-primary group-hover:text-primary'}
                ${isHovered && !disabled ? 'transform translate-x-1' : ''}
              `.trim().replace(/\s+/g, ' ')}
            >
              {label}
              {required && <span className="text-emotion-negative ml-1">*</span>}
            </label>
          )}
        </div>
      </div>
      {error && (
        <p id={`${id}-error`} className="mt-2 text-sm text-emotion-negative flex items-center gap-1">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
};

Checkbox.propTypes = {
  id: PropTypes.string.isRequired,
  label: PropTypes.string,
  checked: PropTypes.bool,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  error: PropTypes.string,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'glass', 'modern']),
};

/**
 * Enhanced Radio component with modern styling and animations
 */
export const Radio = ({
  id,
  name,
  label,
  value,
  checked,
  onChange,
  disabled = false,
  required = false,
  error,
  className = '',
  variant = 'default',
  ...props
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Variant styles
  const variantClasses = {
    default: `
      border-border-light text-primary
      focus:ring-primary/50 checked:bg-primary checked:border-primary
    `,
    glass: `
      border-white/30 bg-glass-white backdrop-blur-sm text-primary
      focus:ring-primary/50 checked:bg-primary/90 checked:border-primary
    `,
    modern: `
      border-border-light text-primary
      focus:ring-primary/50 checked:bg-gradient-to-br checked:from-primary checked:to-primary-dark
      checked:border-primary-dark
    `
  };

  return (
    <div className="mb-2">
      <div 
        className="flex items-center group"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <input
          id={id}
          name={name}
          type="radio"
          value={value}
          checked={checked}
          onChange={onChange}
          disabled={disabled}
          required={required}
          className={`
            h-5 w-5 transition-all duration-300 ease-out cursor-pointer
            ${variantClasses[variant]}
            ${disabled ? 'opacity-60 cursor-not-allowed' : ''}
            ${error ? 'border-emotion-negative' : ''}
            ${isHovered && !disabled ? 'scale-110 shadow-soft' : ''}
            focus:ring-2 focus:ring-offset-2
            ${className}
          `.trim().replace(/\s+/g, ' ')}
          aria-invalid={error ? 'true' : 'false'}
          {...props}
        />
        <label 
          htmlFor={id} 
          className={`
            ml-3 block text-sm font-medium cursor-pointer transition-all duration-200
            ${disabled ? 'text-text-secondary' : 'text-text-primary group-hover:text-primary'}
            ${isHovered && !disabled ? 'transform translate-x-1' : ''}
          `.trim().replace(/\s+/g, ' ')}
        >
          {label}
        </label>
      </div>
    </div>
  );
};

Radio.propTypes = {
  id: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  label: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  checked: PropTypes.bool,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  error: PropTypes.string,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'glass', 'modern']),
};

/**
 * Enhanced Textarea component with modern styling and animations
 */
export const Textarea = ({
  id,
  label,
  placeholder,
  value,
  onChange,
  rows = 4,
  disabled = false,
  required = false,
  error,
  className = '',
  variant = 'default',
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = (e) => {
    setIsFocused(true);
    if (props.onFocus) props.onFocus(e);
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    if (props.onBlur) props.onBlur(e);
  };

  // Variant styles
  const variantClasses = {
    default: `
      bg-white border border-border-light
      focus:border-primary focus:ring-primary/20
    `,
    glass: `
      bg-glass-white backdrop-blur-md border border-white/30
      focus:border-primary focus:ring-primary/20 focus:bg-white/80
    `
  };

  return (
    <div className="mb-4">
      {label && (
        <label 
          htmlFor={id} 
          className="block text-sm font-medium text-text-primary mb-2"
        >
          {label}
          {required && <span className="text-emotion-negative ml-1">*</span>}
        </label>
      )}
      <textarea
        id={id}
        value={value}
        onChange={onChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        rows={rows}
        disabled={disabled}
        required={required}
        placeholder={placeholder}
        className={`
          w-full px-4 py-2 rounded-xl transition-all duration-300 ease-out resize-none
          ${variantClasses[variant]}
          ${error ? 'border-emotion-negative ring-emotion-negative/20' : ''}
          ${disabled ? 'bg-background-subtle opacity-60 cursor-not-allowed' : ''}
          ${isFocused ? 'transform scale-[1.02] shadow-elevated' : 'shadow-soft'}
          focus:outline-none focus:ring-2
          ${className}
        `.trim().replace(/\s+/g, ' ')}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={error ? `${id}-error` : undefined}
        {...props}
      />
      {error && (
        <p id={`${id}-error`} className="mt-2 text-sm text-emotion-negative flex items-center gap-1">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
};

Textarea.propTypes = {
  id: PropTypes.string.isRequired,
  label: PropTypes.string,
  placeholder: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
  rows: PropTypes.number,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  error: PropTypes.string,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'glass']),
};