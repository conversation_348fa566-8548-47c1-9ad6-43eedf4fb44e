import React, { useState } from 'react';
import Button from '../components/Button';
import EnhancedModal from '../components/modern/EnhancedModal';
import ResponsiveImage from '../components/ResponsiveImage';
import Card from '../components/Card';
import { Link } from 'react-router-dom';

const MobileTestPage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSwipeModalOpen, setIsSwipeModalOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with mobile navigation */}
      <header className="bg-white shadow-sm sticky top-0 z-40 safe-top">
        <div className="px-mobile py-4">
          <div className="flex items-center justify-between">
            <h1 className="fluid-h4 font-bold text-primary-500">Mobile Test</h1>
            <button className="icon-btn-touch rounded-full hover:bg-gray-100">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="px-mobile py-mobile">
        {/* Fluid Typography Section */}
        <section className="mb-8">
          <h2 className="fluid-h2 mb-4">Fluid Typography</h2>
          <p className="fluid-body mb-4">
            This text automatically scales based on viewport size using the clamp() function. 
            Resize your browser or test on different devices to see the effect.
          </p>
          <p className="fluid-body-sm text-gray-600">
            Small body text also scales proportionally while maintaining readability.
          </p>
        </section>

        {/* Touch-Optimized Buttons */}
        <section className="mb-8">
          <h2 className="fluid-h3 mb-4">48×48px Touch Targets</h2>
          <div className="btn-group-mobile">
            <Button variant="primary" size="medium">
              Primary Action
            </Button>
            <Button variant="secondary" size="medium">
              Secondary Action
            </Button>
            <Button variant="ghost" size="small">
              Ghost Button
            </Button>
          </div>
        </section>

        {/* Responsive Grid */}
        <section className="mb-8">
          <h2 className="fluid-h3 mb-4">Responsive Grid</h2>
          <div className="grid-mobile">
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <Card key={item} className="card-touch-optimized">
                <h3 className="fluid-h5 mb-2">Card {item}</h3>
                <p className="fluid-body-sm">
                  This grid adapts from 1 column on mobile to 3 columns on desktop.
                </p>
              </Card>
            ))}
          </div>
        </section>

        {/* Responsive Images */}
        <section className="mb-8">
          <h2 className="fluid-h3 mb-4">Responsive Images with srcset</h2>
          <ResponsiveImage
            src="/api/placeholder/1280/720"
            srcSet="/api/placeholder/320/180 320w,
                    /api/placeholder/640/360 640w,
                    /api/placeholder/768/432 768w,
                    /api/placeholder/1024/576 1024w,
                    /api/placeholder/1280/720 1280w"
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 80vw, 60vw"
            alt="Responsive image example"
            aspectRatio="16/9"
            className="mb-4 rounded-lg overflow-hidden"
          />
          <p className="fluid-body-sm text-gray-600">
            Images load the appropriate size based on device and viewport.
          </p>
        </section>

        {/* Touch-Friendly Forms */}
        <section className="mb-8">
          <h2 className="fluid-h3 mb-4">Touch-Optimized Forms</h2>
          <form className="form-touch-spacing">
            <div>
              <label className="block fluid-body-sm font-medium mb-2">
                Text Input
              </label>
              <input
                type="text"
                className="input-touch-optimized border border-gray-300 rounded-lg"
                placeholder="Enter text..."
              />
            </div>
            
            <div>
              <label className="block fluid-body-sm font-medium mb-2">
                Select Input
              </label>
              <select className="select-touch-optimized border border-gray-300 rounded-lg appearance-none">
                <option>Option 1</option>
                <option>Option 2</option>
                <option>Option 3</option>
              </select>
            </div>

            <div className="checkbox-touch-optimized">
              <input type="checkbox" id="checkbox1" />
              <label htmlFor="checkbox1" className="fluid-body">
                I agree to the terms
              </label>
            </div>
          </form>
        </section>

        {/* Swipeable List */}
        <section className="mb-8">
          <h2 className="fluid-h3 mb-4">Swipeable Content</h2>
          <div className="swipeable-x flex gap-4 pb-4">
            {[1, 2, 3, 4, 5].map((item) => (
              <div key={item} className="flex-none snap-start">
                <Card className="w-64 p-4">
                  <h3 className="fluid-h5">Swipe Item {item}</h3>
                  <p className="fluid-body-sm mt-2">
                    Swipe horizontally to see more items
                  </p>
                </Card>
              </div>
            ))}
          </div>
        </section>

        {/* Modal Triggers */}
        <section className="mb-8">
          <h2 className="fluid-h3 mb-4">Mobile Modals</h2>
          <div className="stack-mobile">
            <Button
              variant="primary"
              onClick={() => setIsModalOpen(true)}
            >
              Open Standard Modal
            </Button>
            <Button
              variant="secondary"
              onClick={() => setIsSwipeModalOpen(true)}
            >
              Open Swipe-to-Dismiss Modal
            </Button>
          </div>
        </section>
      </main>

      {/* Bottom Navigation (Mobile Only) */}
      <nav className="bottom-nav sm:hidden">
        <Link to="/" className="bottom-nav-item">
          <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <span className="text-xs">Home</span>
        </Link>
        <Link to="/dashboard" className="bottom-nav-item">
          <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <span className="text-xs">Dashboard</span>
        </Link>
        <Link to="/message-analyzer" className="bottom-nav-item">
          <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
          <span className="text-xs">Analyze</span>
        </Link>
        <Link to="/profile" className="bottom-nav-item">
          <svg className="w-6 h-6 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <span className="text-xs">Profile</span>
        </Link>
      </nav>

      {/* Standard Modal */}
      <EnhancedModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Standard Modal"
        enableSwipeToClose={false}
        footer={
          <div className="flex gap-3">
            <Button
              variant="secondary"
              onClick={() => setIsModalOpen(false)}
              className="flex-1 sm:flex-none"
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={() => setIsModalOpen(false)}
              className="flex-1 sm:flex-none"
            >
              Confirm
            </Button>
          </div>
        }
      >
        <p className="fluid-body mb-4">
          This is a standard modal without swipe-to-dismiss. On mobile, it takes up the full screen for better usability.
        </p>
        <p className="fluid-body-sm text-gray-600">
          The close button and footer buttons have 48×48px touch targets for easy interaction.
        </p>
      </EnhancedModal>

      {/* Swipe-to-Dismiss Modal */}
      <EnhancedModal
        isOpen={isSwipeModalOpen}
        onClose={() => setIsSwipeModalOpen(false)}
        title="Swipe-to-Dismiss Modal"
        enableSwipeToClose={true}
      >
        <div className="space-y-4">
          <p className="fluid-body">
            This modal can be dismissed by swiping down on mobile devices. Try it!
          </p>
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="fluid-body-sm text-blue-800">
              <strong>Tip:</strong> Touch the gray handle at the top and swipe down to close.
            </p>
          </div>
          <p className="fluid-body-sm text-gray-600">
            This gesture makes it easier to dismiss modals on mobile without reaching for the close button.
          </p>
        </div>
      </EnhancedModal>
    </div>
  );
};

export default MobileTestPage;
