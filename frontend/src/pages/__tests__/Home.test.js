import React from 'react';
import { render, screen, waitFor } from '../../__tests__/utils/testUtils';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { server } from '../../__mocks__/server';
import { http, HttpResponse } from 'msw';
import Home from '../Home';

describe('Home Page', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    // Reset localStorage before each test
    localStorage.clear();
  });

  describe('Basic Rendering', () => {
    it('renders the home page correctly', () => {
      render(<Home />);
      
      expect(screen.getByText(/Welcome to My ÆI/i)).toBeInTheDocument();
      expect(screen.getByText(/Emotional Intelligence & Relationship Clarity/i)).toBeInTheDocument();
    });

    it('displays the main features section', () => {
      render(<Home />);
      
      expect(screen.getByText(/Message Analysis/i)).toBeInTheDocument();
      expect(screen.getByText(/Relationship Dashboard/i)).toBeInTheDocument();
      expect(screen.getByText(/Growth Center/i)).toBeInTheDocument();
    });

    it('shows call-to-action buttons', () => {
      render(<Home />);
      
      expect(screen.getByRole('button', { name: /Get Started/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Learn More/i })).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    it('navigates to message analyzer when Get Started is clicked', async () => {
      render(<Home />);
      
      const getStartedButton = screen.getByRole('button', { name: /Get Started/i });
      await user.click(getStartedButton);
      
      // Since we're mocking navigation, we'd check if the navigation function was called
      // In a real test, we might check the URL or rendered component
    });

    it('provides navigation links to all main sections', () => {
      render(<Home />);
      
      expect(screen.getByRole('link', { name: /Dashboard/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /Message Analyzer/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /Relationships/i })).toBeInTheDocument();
    });
  });

  describe('User Authentication State', () => {
    it('shows different content for authenticated users', () => {
      // Mock authenticated state
      jest.mock('@auth0/auth0-react', () => ({
        useAuth0: () => ({
          isLoading: false,
          isAuthenticated: true,
          user: {
            name: 'John Doe',
            email: '<EMAIL>'
          },
          loginWithRedirect: jest.fn(),
          logout: jest.fn(),
        }),
      }));

      render(<Home />);
      
      // Check for personalized welcome message
      expect(screen.getByText(/Welcome back/i)).toBeInTheDocument();
    });

    it('shows login prompt for unauthenticated users', () => {
      render(<Home />);
      
      expect(screen.getByText(/Sign up to get started/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Sign Up/i })).toBeInTheDocument();
    });
  });

  describe('Feature Highlights', () => {
    it('displays feature cards with proper information', () => {
      render(<Home />);
      
      // Check for feature cards
      const features = [
        'Real-time message sentiment analysis',
        'Track relationship health over time',
        'Personalized growth recommendations',
        'Privacy-first approach'
      ];
      
      features.forEach(feature => {
        expect(screen.getByText(new RegExp(feature, 'i'))).toBeInTheDocument();
      });
    });

    it('shows testimonials or social proof', () => {
      render(<Home />);
      
      // Look for testimonial section
      expect(screen.getByText(/What our users say/i)).toBeInTheDocument();
    });
  });

  describe('Interactive Elements', () => {
    it('handles demo video play button', async () => {
      render(<Home />);
      
      const playButton = screen.getByRole('button', { name: /Watch Demo/i });
      await user.click(playButton);
      
      // Check if video modal or player appears
      expect(screen.getByTestId('demo-video')).toBeInTheDocument();
    });

    it('handles newsletter signup', async () => {
      render(<Home />);
      
      const emailInput = screen.getByPlaceholderText(/Enter your email/i);
      const subscribeButton = screen.getByRole('button', { name: /Subscribe/i });
      
      await user.type(emailInput, '<EMAIL>');
      await user.click(subscribeButton);
      
      await waitFor(() => {
        expect(screen.getByText(/Thank you for subscribing/i)).toBeInTheDocument();
      });
    });

    it('validates email input in newsletter signup', async () => {
      render(<Home />);
      
      const emailInput = screen.getByPlaceholderText(/Enter your email/i);
      const subscribeButton = screen.getByRole('button', { name: /Subscribe/i });
      
      await user.type(emailInput, 'invalid-email');
      await user.click(subscribeButton);
      
      expect(screen.getByText(/Please enter a valid email/i)).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('adapts layout for mobile devices', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      render(<Home />);
      
      // Check if mobile-specific classes are applied
      const heroSection = screen.getByTestId('hero-section');
      expect(heroSection).toHaveClass('flex-col', 'md:flex-row');
    });

    it('shows mobile menu toggle on small screens', () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      render(<Home />);
      
      expect(screen.getByLabelText(/Toggle mobile menu/i)).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('renders within acceptable time limits', () => {
      const startTime = performance.now();
      render(<Home />);
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('lazy loads images', () => {
      render(<Home />);
      
      const images = screen.getAllByRole('img');
      images.forEach(img => {
        expect(img).toHaveAttribute('loading', 'lazy');
      });
    });
  });

  describe('SEO and Meta Tags', () => {
    it('sets proper page title', () => {
      render(<Home />);
      
      expect(document.title).toContain('My ÆI - Emotional Intelligence');
    });

    it('includes proper meta descriptions', () => {
      render(<Home />);
      
      const metaDescription = document.querySelector('meta[name="description"]');
      expect(metaDescription).toHaveAttribute('content', expect.stringContaining('emotional intelligence'));
    });
  });

  describe('Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(<Home />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper heading hierarchy', () => {
      render(<Home />);
      
      const h1 = screen.getByRole('heading', { level: 1 });
      const h2s = screen.getAllByRole('heading', { level: 2 });
      
      expect(h1).toBeInTheDocument();
      expect(h2s.length).toBeGreaterThan(0);
    });

    it('has proper alt text for images', () => {
      render(<Home />);
      
      const images = screen.getAllByRole('img');
      images.forEach(img => {
        expect(img).toHaveAttribute('alt');
        expect(img.getAttribute('alt')).not.toBe('');
      });
    });

    it('supports keyboard navigation', async () => {
      render(<Home />);
      
      const interactiveElements = [
        ...screen.getAllByRole('button'),
        ...screen.getAllByRole('link')
      ];
      
      // Tab through all interactive elements
      for (const element of interactiveElements) {
        await user.tab();
        if (document.activeElement === element) {
          expect(element).toHaveFocus();
        }
      }
    });
  });

  describe('Error Handling', () => {
    it('handles API errors gracefully', async () => {
      // Mock API error
      server.use(
        http.get('/api/testimonials', () => {
          return new HttpResponse(null, { status: 500 });
        })
      );
      
      render(<Home />);
      
      await waitFor(() => {
        expect(screen.getByText(/Unable to load testimonials/i)).toBeInTheDocument();
      });
    });

    it('shows fallback content when features fail to load', async () => {
      // Mock network failure
      server.use(
        http.get('/api/features', () => {
          return HttpResponse.error();
        })
      );
      
      render(<Home />);
      
      await waitFor(() => {
        expect(screen.getByText(/Features temporarily unavailable/i)).toBeInTheDocument();
      });
    });
  });

  describe('Privacy and Settings', () => {
    it('shows privacy banner for first-time visitors', () => {
      render(<Home />);
      
      expect(screen.getByText(/Privacy Notice/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Accept & Continue/i })).toBeInTheDocument();
    });

    it('hides privacy banner after acceptance', async () => {
      render(<Home />);
      
      const acceptButton = screen.getByRole('button', { name: /Accept & Continue/i });
      await user.click(acceptButton);
      
      expect(screen.queryByText(/Privacy Notice/i)).not.toBeInTheDocument();
      expect(localStorage.getItem('privacyAccepted')).toBe('true');
    });

    it('respects user motion preferences', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
        })),
      });
      
      render(<Home />);
      
      const animatedElements = screen.getAllByTestId(/animated-/);
      animatedElements.forEach(element => {
        expect(element).toHaveClass('motion-reduce:transform-none');
      });
    });
  });

  describe('Visual Regression', () => {
    it('matches snapshot for the complete home page', () => {
      const { container } = render(<Home />);
      expect(container.firstChild).toMatchSnapshot('home-page-complete');
    });

    it('matches snapshot for hero section', () => {
      const { container } = render(<Home />);
      const heroSection = container.querySelector('[data-testid="hero-section"]');
      expect(heroSection).toMatchSnapshot('home-page-hero');
    });

    it('matches snapshot for features section', () => {
      const { container } = render(<Home />);
      const featuresSection = container.querySelector('[data-testid="features-section"]');
      expect(featuresSection).toMatchSnapshot('home-page-features');
    });
  });

  describe('Integration with Context', () => {
    it('respects faith mode settings', () => {
      // Mock faith mode enabled
      jest.mock('../../contexts/FaithContext', () => ({
        useFaith: () => ({
          faithModeEnabled: true,
          toggleFaithMode: jest.fn(),
        }),
      }));
      
      render(<Home />);
      
      expect(screen.getByText(/spiritual growth/i)).toBeInTheDocument();
    });

    it('respects font preferences', () => {
      // Mock font preference
      jest.mock('../../contexts/FontContext', () => ({
        useFontPreference: () => ({
          fontPreference: 'dyslexic',
        }),
      }));
      
      render(<Home />);
      
      const body = document.body;
      expect(body).toHaveClass('font-dyslexic');
    });
  });
});