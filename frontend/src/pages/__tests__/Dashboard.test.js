import React from 'react';
import { render, screen, waitFor } from '../../__tests__/utils/testUtils';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { server } from '../../__mocks__/server';
import { http, HttpResponse } from 'msw';
import Dashboard from '../Dashboard';

describe('Dashboard Page', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    // Reset any runtime handlers
    server.resetHandlers();
  });

  describe('Data Loading', () => {
    it('displays loading state initially', () => {
      render(<Dashboard />);
      expect(screen.getByText(/Loading dashboard/i)).toBeInTheDocument();
    });

    it('loads and displays dashboard data successfully', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('5')).toBeInTheDocument(); // total_relationships
        expect(screen.getByText('23')).toBeInTheDocument(); // total_analyses
      });
      
      expect(screen.getByText(/Total Relationships/i)).toBeInTheDocument();
      expect(screen.getByText(/Total Analyses/i)).toBeInTheDocument();
    });

    it('displays recent activity', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByText(/Message analyzed with Sarah/i)).toBeInTheDocument();
      });
    });

    it('shows relationship health scores', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Sarah')).toBeInTheDocument();
        expect(screen.getByText('85')).toBeInTheDocument();
        expect(screen.getByText('John')).toBeInTheDocument();
        expect(screen.getByText('92')).toBeInTheDocument();
      });
    });
  });

  describe('Charts and Visualizations', () => {
    it('renders sentiment timeline chart', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByTestId('sentiment-timeline-chart')).toBeInTheDocument();
      });
    });

    it('renders flag counts chart', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByTestId('flag-counts-chart')).toBeInTheDocument();
      });
    });

    it('renders relationship health chart', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByTestId('relationship-health-chart')).toBeInTheDocument();
      });
    });

    it('displays chart tooltips on hover', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        const chartElement = screen.getByTestId('sentiment-timeline-chart');
        expect(chartElement).toBeInTheDocument();
      });
      
      // Simulate hover to show tooltip
      const chartBar = screen.getByTestId('chart-bar-0');
      await user.hover(chartBar);
      
      await waitFor(() => {
        expect(screen.getByText(/Sentiment: 0.8/i)).toBeInTheDocument();
      });
    });
  });

  describe('Filters and Controls', () => {
    it('allows filtering by date range', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByLabelText(/Date range/i)).toBeInTheDocument();
      });
      
      const dateFilter = screen.getByLabelText(/Date range/i);
      await user.selectOptions(dateFilter, 'last-week');
      
      // Check if data is updated
      await waitFor(() => {
        expect(screen.getByText(/Last Week/i)).toBeInTheDocument();
      });
    });

    it('allows filtering by relationship', async () => {
      render(<Dashboard />);
      
      await waitFor(async () => {
        const relationshipFilter = screen.getByLabelText(/Filter by relationship/i);
        await user.selectOptions(relationshipFilter, 'Sarah');
      });
      
      // Check if data is filtered
      await waitFor(() => {
        expect(screen.getByText(/Showing data for Sarah/i)).toBeInTheDocument();
      });
    });

    it('resets filters when clear button is clicked', async () => {
      render(<Dashboard />);
      
      await waitFor(async () => {
        const clearButton = screen.getByRole('button', { name: /Clear filters/i });
        await user.click(clearButton);
      });
      
      await waitFor(() => {
        expect(screen.getByText(/All data/i)).toBeInTheDocument();
      });
    });
  });

  describe('Quick Actions', () => {
    it('provides quick action to analyze new message', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        const analyzeButton = screen.getByRole('button', { name: /Analyze New Message/i });
        expect(analyzeButton).toBeInTheDocument();
      });
    });

    it('provides quick action to add new relationship', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        const addButton = screen.getByRole('button', { name: /Add Relationship/i });
        expect(addButton).toBeInTheDocument();
      });
    });

    it('navigates to detailed pages when quick actions are clicked', async () => {
      render(<Dashboard />);
      
      await waitFor(async () => {
        const analyzeButton = screen.getByRole('button', { name: /Analyze New Message/i });
        await user.click(analyzeButton);
      });
      
      // Check if navigation occurred (mocked)
      expect(screen.getByTestId('message-analyzer-page')).toBeInTheDocument();
    });
  });

  describe('Real-time Updates', () => {
    it('updates data when new analysis is completed', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('23')).toBeInTheDocument(); // initial count
      });
      
      // Simulate real-time update
      server.use(
        http.get('/api/dashboard', () => {
          return HttpResponse.json({
            ...mockDashboardData,
            total_analyses: 24 // updated count
          });
        })
      );
      
      // Trigger refresh
      const refreshButton = screen.getByRole('button', { name: /Refresh/i });
      await user.click(refreshButton);
      
      await waitFor(() => {
        expect(screen.getByText('24')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('displays error message when data fails to load', async () => {
      server.use(
        http.get('/api/dashboard', () => {
          return new HttpResponse(null, { status: 500 });
        })
      );
      
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByText(/Failed to load dashboard data/i)).toBeInTheDocument();
      });
    });

    it('provides retry option when loading fails', async () => {
      server.use(
        http.get('/api/dashboard', () => {
          return new HttpResponse(null, { status: 500 });
        })
      );
      
      render(<Dashboard />);
      
      await waitFor(() => {
        const retryButton = screen.getByRole('button', { name: /Retry/i });
        expect(retryButton).toBeInTheDocument();
      });
    });

    it('handles partial data loading gracefully', async () => {
      server.use(
        http.get('/api/dashboard', () => {
          return HttpResponse.json({
            total_relationships: 5,
            total_analyses: 23,
            // Missing other fields
          });
        })
      );
      
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('5')).toBeInTheDocument();
        expect(screen.getByText('23')).toBeInTheDocument();
        expect(screen.getByText(/Some data unavailable/i)).toBeInTheDocument();
      });
    });
  });

  describe('Performance', () => {
    it('renders dashboard within acceptable time', () => {
      const startTime = performance.now();
      render(<Dashboard />);
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(200);
    });

    it('efficiently handles large datasets', async () => {
      // Mock large dataset
      const largeTimeline = Array.from({ length: 1000 }, (_, i) => [
        `2024-01-${String(i % 30 + 1).padStart(2, '0')}`,
        Math.random()
      ]);
      
      server.use(
        http.get('/api/dashboard', () => {
          return HttpResponse.json({
            ...mockDashboardData,
            sentiment_timeline: largeTimeline
          });
        })
      );
      
      const startTime = performance.now();
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByTestId('sentiment-timeline-chart')).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(2000);
    });

    it('implements virtual scrolling for large lists', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        const activityList = screen.getByTestId('recent-activity-list');
        expect(activityList).toHaveAttribute('data-virtualized', 'true');
      });
    });
  });

  describe('Responsive Design', () => {
    it('adapts layout for mobile screens', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      render(<Dashboard />);
      
      const mainGrid = screen.getByTestId('dashboard-grid');
      expect(mainGrid).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3');
    });

    it('shows mobile-optimized charts', async () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      render(<Dashboard />);
      
      await waitFor(() => {
        const chart = screen.getByTestId('sentiment-timeline-chart');
        expect(chart).toHaveClass('mobile-optimized');
      });
    });
  });

  describe('Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByText(/Total Relationships/i)).toBeInTheDocument();
      });
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('provides screen reader friendly chart descriptions', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByLabelText(/Sentiment timeline showing/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/Flag counts distribution/i)).toBeInTheDocument();
      });
    });

    it('supports keyboard navigation for interactive elements', async () => {
      render(<Dashboard />);
      
      await waitFor(async () => {
        const interactiveElements = screen.getAllByRole('button');
        
        for (const element of interactiveElements) {
          await user.tab();
          if (document.activeElement === element) {
            expect(element).toHaveFocus();
          }
        }
      });
    });

    it('provides proper ARIA labels for charts', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        const chart = screen.getByTestId('sentiment-timeline-chart');
        expect(chart).toHaveAttribute('role', 'img');
        expect(chart).toHaveAttribute('aria-label');
      });
    });
  });

  describe('Data Export', () => {
    it('allows exporting dashboard data as PDF', async () => {
      render(<Dashboard />);
      
      await waitFor(async () => {
        const exportButton = screen.getByRole('button', { name: /Export PDF/i });
        await user.click(exportButton);
      });
      
      expect(screen.getByText(/Generating PDF/i)).toBeInTheDocument();
    });

    it('allows exporting data as CSV', async () => {
      render(<Dashboard />);
      
      await waitFor(async () => {
        const exportButton = screen.getByRole('button', { name: /Export CSV/i });
        await user.click(exportButton);
      });
      
      // Check if download is triggered
      expect(window.URL.createObjectURL).toHaveBeenCalled();
    });
  });

  describe('Personalization', () => {
    it('allows customizing dashboard layout', async () => {
      render(<Dashboard />);
      
      await waitFor(async () => {
        const customizeButton = screen.getByRole('button', { name: /Customize Layout/i });
        await user.click(customizeButton);
      });
      
      expect(screen.getByText(/Drag to reorder widgets/i)).toBeInTheDocument();
    });

    it('saves user preferences', async () => {
      render(<Dashboard />);
      
      await waitFor(() => {
        const widget = screen.getByTestId('sentiment-widget');
        // Simulate drag and drop to reorder
        fireEvent.dragStart(widget);
        fireEvent.drop(screen.getByTestId('dashboard-grid'));
      });
      
      // Check if preferences are saved
      expect(localStorage.getItem('dashboardLayout')).toBeTruthy();
    });
  });

  describe('Integration with Other Components', () => {
    it('integrates properly with faith mode', () => {
      // Mock faith mode enabled
      jest.mock('../../contexts/FaithContext', () => ({
        useFaith: () => ({
          faithModeEnabled: true,
          getFaithContent: jest.fn(() => 'Spiritual insight content'),
        }),
      }));
      
      render(<Dashboard />);
      
      expect(screen.getByText(/Spiritual insight content/i)).toBeInTheDocument();
    });

    it('respects motion preferences', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
        })),
      });
      
      render(<Dashboard />);
      
      const animatedElements = screen.getAllByTestId(/animated-/);
      animatedElements.forEach(element => {
        expect(element).toHaveClass('motion-reduce:transition-none');
      });
    });
  });

  describe('Visual Regression', () => {
    it('matches snapshot for complete dashboard', async () => {
      const { container } = render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByText(/Total Relationships/i)).toBeInTheDocument();
      });
      
      expect(container.firstChild).toMatchSnapshot('dashboard-complete');
    });

    it('matches snapshot for loading state', () => {
      server.use(
        http.get('/api/dashboard', () => {
          return new Promise(() => {}); // Never resolves to test loading state
        })
      );
      
      const { container } = render(<Dashboard />);
      expect(container.firstChild).toMatchSnapshot('dashboard-loading');
    });

    it('matches snapshot for error state', async () => {
      server.use(
        http.get('/api/dashboard', () => {
          return new HttpResponse(null, { status: 500 });
        })
      );
      
      const { container } = render(<Dashboard />);
      
      await waitFor(() => {
        expect(screen.getByText(/Failed to load/i)).toBeInTheDocument();
      });
      
      expect(container.firstChild).toMatchSnapshot('dashboard-error');
    });
  });
});