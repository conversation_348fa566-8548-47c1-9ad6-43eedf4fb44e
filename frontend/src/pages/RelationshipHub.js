import React, { useState, useEffect } from 'react';
import { useAppContext } from '../contexts/AppContext';
import { useFaith } from '../contexts/FaithContext';
import Card from '../components/Card';
import Button from '../components/Button';
import { Input, Textarea, Select } from '../components/FormElements';
import axios from 'axios';
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  PieChart, Pie, Cell, BarChart, Bar, Legend
} from 'recharts';

const RelationshipHub = () => {
  const { relationships, setRelationships, analyzeMessage } = useAppContext();
  const { faithModeEnabled, getFaithContent } = useFaith();
  
  const [selectedRelationship, setSelectedRelationship] = useState(null);
  const [relationshipHistory, setRelationshipHistory] = useState(null);
  const [showNewForm, setShowNewForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [error, setError] = useState(null);
  
  const [newRelationship, setNewRelationship] = useState({
    name: '',
    type: 'Friend',
    notes: ''
  });
  
  const [quickMessage, setQuickMessage] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Fetch relationships from API
  const fetchRelationships = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await axios.get(`${process.env.REACT_APP_BACKEND_URL}/api/relationships`);
      setRelationships(response.data || []);
    } catch (err) {
      console.error('Failed to fetch relationships:', err);
      setError('Failed to load relationships. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch relationship history when a relationship is selected
  const fetchRelationshipHistory = async (relationshipId) => {
    if (!relationshipId) return;
    
    try {
      setIsLoadingHistory(true);
      const response = await axios.get(
        `${process.env.REACT_APP_BACKEND_URL}/api/relationships/${relationshipId}/history`
      );
      setRelationshipHistory(response.data);
    } catch (err) {
      console.error('Failed to fetch relationship history:', err);
      // Don't show error UI for history, just log it
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // Create a new relationship
  const handleSubmitNewRelationship = async (e) => {
    e.preventDefault();
    
    try {
      setIsLoading(true);
      setError(null);
      
      const relationship = {
        id: Date.now().toString(),
        name: newRelationship.name,
        type: newRelationship.type,
        notes: newRelationship.notes,
        health_score: 75, // Default starting score
        last_contact: new Date().toISOString(),
        sentiment: 'neutral',
        flag_history: [],
        created_at: new Date().toISOString()
      };
      
      const response = await axios.post(
        `${process.env.REACT_APP_BACKEND_URL}/api/relationships`,
        relationship
      );
      
      setRelationships([...relationships, response.data]);
      setNewRelationship({ name: '', type: 'Friend', notes: '' });
      setShowNewForm(false);
      setSelectedRelationship(response.data);
    } catch (err) {
      console.error('Failed to create relationship:', err);
      setError('Failed to create relationship. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Update an existing relationship
  const handleUpdateRelationship = async (e) => {
    e.preventDefault();
    if (!selectedRelationship) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await axios.put(
        `${process.env.REACT_APP_BACKEND_URL}/api/relationships/${selectedRelationship.id}`,
        {
          name: selectedRelationship.name,
          type: selectedRelationship.type,
          notes: selectedRelationship.notes
        }
      );
      
      // Update relationships list
      setRelationships(relationships.map(rel => 
        rel.id === response.data.id ? response.data : rel
      ));
      
      setShowEditForm(false);
    } catch (err) {
      console.error('Failed to update relationship:', err);
      setError('Failed to update relationship. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Delete a relationship
  const handleDeleteRelationship = async () => {
    if (!selectedRelationship || !window.confirm(`Are you sure you want to delete the relationship with ${selectedRelationship.name}?`)) {
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      await axios.delete(
        `${process.env.REACT_APP_BACKEND_URL}/api/relationships/${selectedRelationship.id}`
      );
      
      // Update relationships list
      setRelationships(relationships.filter(rel => rel.id !== selectedRelationship.id));
      setSelectedRelationship(null);
    } catch (err) {
      console.error('Failed to delete relationship:', err);
      setError('Failed to delete relationship. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle quick message analysis
  const handleQuickAnalysis = async (e) => {
    e.preventDefault();
    if (!quickMessage.trim() || !selectedRelationship) return;
    
    setIsAnalyzing(true);
    try {
      await axios.post(
        `${process.env.REACT_APP_BACKEND_URL}/api/relationships/${selectedRelationship.id}/analyze`,
        {
          text: quickMessage,
          context: `Quick analysis for relationship with ${selectedRelationship.name}`,
          relationship_id: selectedRelationship.id
        }
      );
      
      setQuickMessage('');
      
      // Refresh relationship history
      await fetchRelationshipHistory(selectedRelationship.id);
      
      // Refresh relationships list to get updated health score, etc.
      await fetchRelationships();
    } catch (err) {
      console.error('Quick analysis error:', err);
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Load initial data
  useEffect(() => {
    fetchRelationships();
  }, []);
  
  // Load relationship history when a relationship is selected
  useEffect(() => {
    if (selectedRelationship) {
      fetchRelationshipHistory(selectedRelationship.id);
    } else {
      setRelationshipHistory(null);
    }
  }, [selectedRelationship]);
  
  // Colors for charts
  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088FE', '#00C49F'];
  
  // Map sentiment to color
  const getSentimentColor = (sentiment) => {
    return sentiment === 'positive' ? '#4ade80' : 
           sentiment === 'neutral' ? '#a78bfa' : 
           '#f87171';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header Section */}
      <div className="mb-8 text-center">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-4">
          Relationship Hub
        </h1>
        <p className="text-text-secondary text-lg max-w-2xl mx-auto">
          Track the emotional health of your key relationships over time with AI-powered insights and analytics.
        </p>
      </div>

      {error && (
        <Card variant="glass" className="mb-8 bg-emotion-negative/10 border-emotion-negative/30">
          <div className="flex items-center gap-3">
            <svg className="w-6 h-6 text-emotion-negative flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <p className="text-emotion-negative font-medium">{error}</p>
          </div>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Relationship List Sidebar */}
        <div className="lg:col-span-1">
          <Card variant="glass" hover="lift" className="sticky top-4">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-text-primary flex items-center gap-2">
                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.196M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.196-2.196M7 20v-2c0-.656.126-1.283.356-1.857M13 7a4 4 0 11-8 0 4 4 0 018 0zm-4 6a6 6 0 00-6 6m6-6a6 6 0 016 6" />
                </svg>
                Your Connections
              </h2>
              <Button
                onClick={() => setShowNewForm(true)}
                variant="ghost"
                size="sm"
                disabled={isLoading}
              >
                Add New
              </Button>
            </div>
            
            {isLoading && !relationships.length ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : relationships.length > 0 ? (
              <div className="space-y-3 max-h-[500px] overflow-y-auto pr-2">
                {relationships.map(relationship => (
                  <Card
                    key={relationship.id}
                    onClick={() => {
                      setSelectedRelationship(relationship);
                      setShowNewForm(false);
                      setShowEditForm(false);
                    }}
                    variant="glass"
                    hover="lift"
                    className={`cursor-pointer transition-all duration-300 ${
                      selectedRelationship && selectedRelationship.id === relationship.id
                        ? 'bg-primary/10 border-primary/30 shadow-glow'
                        : 'hover:bg-primary/5'
                    }`}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex-1">
                        <h3 className="font-semibold text-text-primary">{relationship.name}</h3>
                        <p className="text-sm text-text-secondary">{relationship.type}</p>
                      </div>
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-white shadow-soft transition-all duration-300 ${
                        relationship.health_score > 75 
                          ? 'bg-gradient-to-br from-emotion-positive to-emotion-positive/80' 
                          : relationship.health_score > 50 
                          ? 'bg-gradient-to-br from-secondary to-secondary/80' 
                          : 'bg-gradient-to-br from-emotion-negative to-emotion-negative/80'
                      }`}>
                        {relationship.health_score}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="mb-4 animate-float">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center mx-auto">
                    <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.196M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.196-2.196M7 20v-2c0-.656.126-1.283.356-1.857M13 7a4 4 0 11-8 0 4 4 0 018 0zm-4 6a6 6 0 00-6 6m6-6a6 6 0 016 6" />
                    </svg>
                  </div>
                </div>
                <p className="text-text-secondary mb-4">No relationships added yet</p>
                <Button
                  onClick={() => setShowNewForm(true)}
                  variant="gradient"
                  disabled={isLoading}
                >
                  Add Your First Relationship
                </Button>
              </div>
            )}
          </Card>
        </div>

        {/* Relationship Detail or New Form */}
        <div className="lg:col-span-3">
          {showNewForm ? (
            <Card variant="glass" hover="lift" className="animate-float">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-text-primary flex items-center gap-2">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add New Relationship
                </h2>
                <Button
                  onClick={() => setShowNewForm(false)}
                  variant="ghost"
                  size="sm"
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              </div>
              
              <form onSubmit={handleSubmitNewRelationship} className="space-y-6">
                <Input
                  id="name"
                  label="Name"
                  value={newRelationship.name}
                  onChange={(e) => setNewRelationship({...newRelationship, name: e.target.value})}
                  required
                  disabled={isLoading}
                  variant="glass"
                  placeholder="Enter the person's name"
                />
                
                <Select
                  id="type"
                  label="Relationship Type"
                  options={[
                    { value: 'Friend', label: 'Friend' },
                    { value: 'Family', label: 'Family' },
                    { value: 'Partner', label: 'Partner' },
                    { value: 'Colleague', label: 'Colleague' },
                    { value: 'Manager', label: 'Manager' },
                    { value: 'Other', label: 'Other' }
                  ]}
                  value={newRelationship.type}
                  onChange={(e) => setNewRelationship({...newRelationship, type: e.target.value})}
                  disabled={isLoading}
                  variant="glass"
                />
                
                <Textarea
                  id="notes"
                  label="Notes (Optional)"
                  rows={4}
                  value={newRelationship.notes}
                  onChange={(e) => setNewRelationship({...newRelationship, notes: e.target.value})}
                  disabled={isLoading}
                  variant="glass"
                  placeholder="Add any notes about this relationship..."
                />
                
                <div className="flex gap-4 pt-4">
                  <Button
                    type="submit"
                    variant="gradient"
                    disabled={isLoading || !newRelationship.name.trim()}
                    loading={isLoading}
                    loadingText="Adding..."
                    className="flex-1"
                  >
                    Add Relationship
                  </Button>
                  <Button
                    type="button"
                    onClick={() => setShowNewForm(false)}
                    variant="ghost"
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </Card>
          ) : showEditForm && selectedRelationship ? (
            <Card variant="glass" hover="lift" className="animate-float">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-text-primary flex items-center gap-2">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Edit Relationship
                </h2>
                <Button
                  onClick={() => setShowEditForm(false)}
                  variant="ghost"
                  size="sm"
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              </div>
              
              <form onSubmit={handleUpdateRelationship} className="space-y-6">
                <Input
                  id="edit-name"
                  label="Name"
                  value={selectedRelationship.name}
                  onChange={(e) => setSelectedRelationship({...selectedRelationship, name: e.target.value})}
                  required
                  disabled={isLoading}
                  variant="glass"
                />
                
                <Select
                  id="edit-type"
                  label="Relationship Type"
                  options={[
                    { value: 'Friend', label: 'Friend' },
                    { value: 'Family', label: 'Family' },
                    { value: 'Partner', label: 'Partner' },
                    { value: 'Colleague', label: 'Colleague' },
                    { value: 'Manager', label: 'Manager' },
                    { value: 'Other', label: 'Other' }
                  ]}
                  value={selectedRelationship.type}
                  onChange={(e) => setSelectedRelationship({...selectedRelationship, type: e.target.value})}
                  disabled={isLoading}
                  variant="glass"
                />
                
                <Textarea
                  id="edit-notes"
                  label="Notes (Optional)"
                  rows={4}
                  value={selectedRelationship.notes || ''}
                  onChange={(e) => setSelectedRelationship({...selectedRelationship, notes: e.target.value})}
                  disabled={isLoading}
                  variant="glass"
                />
                
                <div className="flex gap-4 pt-4">
                  <Button
                    type="submit"
                    variant="gradient"
                    disabled={isLoading || !selectedRelationship.name.trim()}
                    loading={isLoading}
                    loadingText="Updating..."
                    className="flex-1"
                  >
                    Update Relationship
                  </Button>
                  <Button
                    type="button"
                    onClick={handleDeleteRelationship}
                    variant="danger"
                    disabled={isLoading}
                  >
                    Delete
                  </Button>
                </div>
              </form>
            </Card>
          ) : selectedRelationship ? (
            <Card variant="glass" hover="lift" className="animate-float">
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center gap-4">
                  <div className={`w-16 h-16 rounded-full flex items-center justify-center font-bold text-white shadow-soft transition-all duration-300 ${
                    selectedRelationship.health_score > 75 
                      ? 'bg-gradient-to-br from-emotion-positive to-emotion-positive/80' 
                      : selectedRelationship.health_score > 50 
                      ? 'bg-gradient-to-br from-secondary to-secondary/80' 
                      : 'bg-gradient-to-br from-emotion-negative to-emotion-negative/80'
                  }`}>
                    {selectedRelationship.health_score}
                  </div>
                  <div>
                    <h2 className="text-2xl font-semibold text-text-primary">{selectedRelationship.name}</h2>
                    <p className="text-text-secondary font-medium">{selectedRelationship.type}</p>
                  </div>
                </div>
                <Button
                  onClick={() => setShowEditForm(true)}
                  variant="glass"
                  size="sm"
                  disabled={isLoading}
                >
                  Edit
                </Button>
              </div>
              
              {isLoadingHistory ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : (
                <>
                  {/* Summary Stats */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                    <Card variant="glass" hover="lift" className="bg-primary/5 border-primary/20">
                      <h3 className="text-sm font-medium text-text-secondary mb-1">Last Contact</h3>
                      <p className="text-lg font-semibold text-primary">
                        {new Date(selectedRelationship.last_contact).toLocaleDateString()}
                      </p>
                    </Card>
                    <Card variant="glass" hover="lift" className="bg-secondary/5 border-secondary/20">
                      <h3 className="text-sm font-medium text-text-secondary mb-1">Current Sentiment</h3>
                      <p className="text-lg font-semibold text-secondary capitalize">
                        {selectedRelationship.sentiment}
                      </p>
                    </Card>
                    <Card variant="glass" hover="lift" className="bg-accent/5 border-accent/20">
                      <h3 className="text-sm font-medium text-text-secondary mb-1">Interactions Analyzed</h3>
                      <p className="text-lg font-semibold text-accent">
                        {relationshipHistory?.analyses?.length || 0}
                      </p>
                    </Card>
                  </div>
                  
                  {relationshipHistory ? (
                    <>
                      {/* Health Trend Chart */}
                      {relationshipHistory.health_trend.length > 0 && (
                        <Card variant="glass" hover="lift" className="mb-8">
                          <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
                            <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                            Relationship Health Trend
                          </h3>
                          <div className="h-64">
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart
                                data={relationshipHistory.health_trend}
                                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                              >
                                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                                <XAxis dataKey="date" stroke="rgba(255,255,255,0.6)" />
                                <YAxis domain={[0, 100]} stroke="rgba(255,255,255,0.6)" />
                                <Tooltip 
                                  contentStyle={{
                                    backgroundColor: 'rgba(255,255,255,0.95)',
                                    border: 'none',
                                    borderRadius: '8px',
                                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                                  }}
                                />
                                <Line 
                                  type="monotone" 
                                  dataKey="score" 
                                  stroke="url(#healthGradient)" 
                                  strokeWidth={3}
                                  activeDot={{ r: 8, fill: '#8b5cf6' }}
                                />
                                <defs>
                                  <linearGradient id="healthGradient" x1="0" y1="0" x2="1" y2="0">
                                    <stop offset="0%" stopColor="#8b5cf6" />
                                    <stop offset="100%" stopColor="#06b6d4" />
                                  </linearGradient>
                                </defs>
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        </Card>
                      )}
                      
                      {/* Sentiment and Flag Distribution */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        {/* Sentiment Distribution */}
                        <Card variant="glass" hover="lift">
                          <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
                            <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 11a1 1 0 001 1h8a1 1 0 001-1m-9 0V9a1 1 0 011-1h8a1 1 0 011 1v6M7 4h10M7 4a1 1 0 00-1 1v1h10V5a1 1 0 00-1-1" />
                            </svg>
                            Sentiment Distribution
                          </h3>
                          {Object.values(relationshipHistory.sentiment_counts).some(count => count > 0) ? (
                            <div className="h-64">
                              <ResponsiveContainer width="100%" height="100%">
                                <PieChart>
                                  <Pie
                                    data={Object.entries(relationshipHistory.sentiment_counts).map(([key, value]) => ({
                                      name: key.charAt(0).toUpperCase() + key.slice(1),
                                      value
                                    }))}
                                    cx="50%"
                                    cy="50%"
                                    labelLine={false}
                                    outerRadius={80}
                                    fill="#8884d8"
                                    dataKey="value"
                                  >
                                    {Object.keys(relationshipHistory.sentiment_counts).map((entry, index) => (
                                      <Cell key={`cell-${index}`} fill={getSentimentColor(entry)} />
                                    ))}
                                  </Pie>
                                  <Tooltip />
                                  <Legend />
                                </PieChart>
                              </ResponsiveContainer>
                            </div>
                          ) : (
                            <div className="bg-surface/20 rounded-lg p-4 text-center text-text-secondary">
                              No sentiment data available yet
                            </div>
                          )}
                        </Card>
                        
                        {/* Flag Distribution */}
                        <Card variant="glass" hover="lift">
                          <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
                            <svg className="w-5 h-5 text-emotion-negative" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                            Flag Distribution
                          </h3>
                          {Object.keys(relationshipHistory.flag_types).length > 0 ? (
                            <div className="h-64">
                              <ResponsiveContainer width="100%" height="100%">
                                <BarChart
                                  data={Object.entries(relationshipHistory.flag_types).map(([key, value]) => ({
                                    name: key,
                                    count: value
                                  }))}
                                  margin={{ top: 5, right: 30, left: 20, bottom: 50 }}
                                >
                                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                                  <XAxis 
                                    dataKey="name" 
                                    angle={-45} 
                                    textAnchor="end"
                                    height={60}
                                    tick={{ fontSize: 12 }}
                                    stroke="rgba(255,255,255,0.6)"
                                  />
                                  <YAxis stroke="rgba(255,255,255,0.6)" />
                                  <Tooltip 
                                    contentStyle={{
                                      backgroundColor: 'rgba(255,255,255,0.95)',
                                      border: 'none',
                                      borderRadius: '8px',
                                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                                    }}
                                  />
                                  <Bar dataKey="count" fill="url(#flagGradient)" />
                                  <defs>
                                    <linearGradient id="flagGradient" x1="0" y1="0" x2="0" y2="1">
                                      <stop offset="0%" stopColor="#f87171" />
                                      <stop offset="100%" stopColor="#dc2626" />
                                    </linearGradient>
                                  </defs>
                                </BarChart>
                              </ResponsiveContainer>
                            </div>
                          ) : (
                            <div className="bg-surface/20 rounded-lg p-4 text-center text-text-secondary">
                              No flag data available yet
                            </div>
                          )}
                        </Card>
                      </div>
                      
                      {/* Analysis History */}
                      <Card variant="glass" hover="lift" className="mb-8">
                        <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
                          <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          Recent Analyses
                        </h3>
                        {relationshipHistory.analyses.length > 0 ? (
                          <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
                            {relationshipHistory.analyses.slice(0, 5).map((analysis, index) => (
                              <Card key={index} variant="glass" hover="lift" className="animate-float" style={{animationDelay: `${index * 0.1}s`}}>
                                <div className="flex justify-between items-start mb-2">
                                  <span className="text-sm text-text-secondary">
                                    {new Date(analysis.created_at).toLocaleString()}
                                  </span>
                                  <span className={`px-3 py-1 text-xs font-medium rounded-full transition-all duration-300 ${
                                    analysis.sentiment === 'positive' ? 'bg-emotion-positive/20 text-emotion-positive border border-emotion-positive/30' :
                                    analysis.sentiment === 'neutral' ? 'bg-accent/20 text-accent border border-accent/30' :
                                    'bg-emotion-negative/20 text-emotion-negative border border-emotion-negative/30'
                                  }`}>
                                    {analysis.sentiment.charAt(0).toUpperCase() + analysis.sentiment.slice(1)}
                                  </span>
                                </div>
                                <p className="text-text-primary mb-3">{analysis.text}</p>
                                {analysis.flags && analysis.flags.length > 0 ? (
                                  <div className="flex flex-wrap gap-2">
                                    {analysis.flags.map((flag, fidx) => (
                                      <span key={fidx} className="px-2 py-1 bg-emotion-negative/20 text-emotion-negative text-xs rounded-full border border-emotion-negative/30 animate-pulse">
                                        {flag.type}
                                      </span>
                                    ))}
                                  </div>
                                ) : (
                                  <span className="px-2 py-1 bg-emotion-positive/20 text-emotion-positive text-xs rounded-full border border-emotion-positive/30">
                                    No flags detected
                                  </span>
                                )}
                              </Card>
                            ))}
                          </div>
                        ) : (
                          <div className="bg-surface/20 p-4 rounded-lg text-center">
                            <p className="text-text-secondary">No analysis history yet</p>
                          </div>
                        )}
                      </Card>
                    </>
                  ) : (
                    <Card variant="glass" hover="lift" className="mb-8 bg-surface/20">
                      <p className="text-text-secondary text-center">No history data available for this relationship yet</p>
                    </Card>
                  )}
                  
                  {/* Faith-Based Insight */}
                  {faithModeEnabled && (
                    <Card variant="glass" hover="lift" className="mb-8 bg-primary/5 border-primary/20">
                      <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
                        <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        Faith-Based Insight
                      </h3>
                      <p className="text-text-primary">
                        {getFaithContent('reframe', { theme: 'relationship' }) || 
                         "Your faith tradition offers wisdom about relationships and how to nurture them with compassion and understanding."}
                      </p>
                    </Card>
                  )}
                  
                  {/* Quick Analysis Form */}
                  <Card variant="glass" hover="lift">
                    <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      Quick Message Analysis
                    </h3>
                    <form onSubmit={handleQuickAnalysis} className="space-y-4">
                      <Textarea
                        rows={4}
                        placeholder={`Paste a message from ${selectedRelationship.name} to analyze...`}
                        value={quickMessage}
                        onChange={(e) => setQuickMessage(e.target.value)}
                        required
                        disabled={isAnalyzing}
                        variant="glass"
                      />
                      <Button
                        type="submit"
                        disabled={isAnalyzing || !quickMessage.trim()}
                        variant="gradient"
                        loading={isAnalyzing}
                        loadingText="Analyzing..."
                        className="w-full"
                      >
                        Analyze Message
                      </Button>
                    </form>
                  </Card>
                </>
              )}
            </Card>
          ) : (
            <Card variant="glass" hover="lift" className="h-full flex flex-col items-center justify-center text-center animate-float">
              <div className="mb-4 animate-float">
                <div className="w-20 h-20 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center mx-auto">
                  <svg className="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-medium text-text-primary mb-2">
                Select a relationship
              </h3>
              <p className="text-text-secondary mb-6">
                Or add a new one to start tracking your relationship health
              </p>
              <Button
                onClick={() => setShowNewForm(true)}
                variant="gradient"
                disabled={isLoading}
              >
                Add New Relationship
              </Button>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default RelationshipHub;
