import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import Card from '../components/Card';
import Button from '../components/Button';
import Icon from '../components/modern/Icon';

const Home = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="flex flex-col">
      {/* Enhanced Hero Section with Animated Gradient */}
      <section
        className="relative bg-gradient-to-br from-purple-800 via-indigo-900 to-purple-900 text-white py-20 lg:py-32 overflow-hidden hero-section"
        aria-labelledby="hero-heading"
        role="banner"
        style={{
          '--hero-padding-y': '5rem',
          '--hero-padding-y-lg': '8rem',
          '--orb-size-sm': '4rem',
          '--orb-size-md': '6rem',
          '--orb-size-lg': '8rem',
        }}
      >
        {/* Performance-optimized animated gradient background */}
        <div className="absolute inset-0 hero-gradient-animated" aria-hidden="true"></div>
        
        {/* Secondary gradient layer for depth */}
        <div className="absolute inset-0 hero-gradient-secondary" aria-hidden="true"></div>
        
        {/* Parallax floating orbs with fixed dimensions for CLS optimization */}
        <div 
          className="absolute hero-orb hero-orb-1" 
          style={{ transform: `translateY(${scrollY * 0.1}px)` }}
          aria-hidden="true"
        ></div>
        <div 
          className="absolute hero-orb hero-orb-2" 
          style={{ transform: `translateY(${scrollY * -0.15}px)` }}
          aria-hidden="true"
        ></div>
        <div 
          className="absolute hero-orb hero-orb-3" 
          style={{ transform: `translateY(${scrollY * 0.2}px)` }}
          aria-hidden="true"
        ></div>
        
        {/* Mesh gradient overlay for texture */}
        <div className="absolute inset-0 hero-mesh-gradient" aria-hidden="true"></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto text-center">
            {/* Sophisticated Multi-Line Header */}
            <div className="mb-16 space-y-3">
              {/* Primary Brand Element - My ÆI */}
              <div className="relative mb-8">
                <h1
                  id="hero-heading"
                  className="text-hero text-inverse"
                  aria-label="My A I - Emotional Intelligence and Relationship Clarity System"
                >
                  <span className="gradient-text-luxury tracking-tight text-shadow-luxury">
                    My
                  </span>
                  <span className="mx-3 lg:mx-6 gradient-text-brand font-extrabold tracking-brand animate-subtle-glow brand-hover inline-block">
                    ÆI
                  </span>
                </h1>
                {/* Enhanced glow effect behind ÆI */}
                <div className="absolute inset-0 blur-3xl opacity-20 bg-gradient-to-r from-amber-400 via-yellow-400 to-orange-400 -z-10 animate-pulse-slow" aria-hidden="true"></div>
              </div>
              
              {/* Emotional Intelligence Line */}
              <div className="py-2">
                <h2 className="text-display text-inverse opacity-95 font-light tracking-wide">
                  <span className="font-medium">Emotional Intelligence</span>
                </h2>
              </div>
              
              {/* Decorative Ampersand */}
              <div className="py-6">
                <span className="text-display font-serif text-amber-300/70 italic tracking-widest drop-shadow-lg transform hover:scale-110 transition-transform duration-300 inline-block">
                  &
                </span>
              </div>
              
              {/* Relationship Clarity System Line */}
              <div className="py-2">
                <p className="text-lead font-light tracking-wide text-indigo-200/90">
                  <span className="font-medium">Relationship Clarity System</span>
                </p>
              </div>
            </div>
            
            <p className="text-lg sm:text-xl lg:text-2xl mb-8 opacity-90 leading-relaxed max-w-4xl mx-auto font-light tracking-wide text-purple-100/90 text-shadow-luxury">
              Decode emotional red flags, recognize toxic patterns, and build healthier relationships —
              <br className="hidden sm:block" />
              using behavioral science, spiritual insight, and adaptive AI feedback.
            </p>
            
            {/* Enhanced Trust Badges & Social Proof */}
            <div className="flex flex-wrap justify-center gap-6 mb-10">
              {/* Active Users Badge */}
              <div className="trust-badge group">
                <div className="trust-badge-icon">
                  <div className="flex -space-x-2">
                    <div className="trust-avatar trust-avatar-1"></div>
                    <div className="trust-avatar trust-avatar-2"></div>
                    <div className="trust-avatar trust-avatar-3"></div>
                    <div className="trust-avatar trust-avatar-4"></div>
                  </div>
                </div>
                <div className="trust-badge-content">
                  <span className="trust-badge-number">1,247+</span>
                  <span className="trust-badge-label">Active Users</span>
                </div>
              </div>
              
              {/* Success Rate Badge */}
              <div className="trust-badge group">
                <div className="trust-badge-icon">
                  <Icon name="TrendingUp" size="md" className="text-green-400" />
                </div>
                <div className="trust-badge-content">
                  <span className="trust-badge-number">94%</span>
                  <span className="trust-badge-label">Success Rate</span>
                </div>
              </div>
              
              {/* Science-Backed Badge */}
              <div className="trust-badge group">
                <div className="trust-badge-icon">
                  <Icon name="Award" size="md" className="text-amber-400" />
                </div>
                <div className="trust-badge-content">
                  <span className="trust-badge-number">Science</span>
                  <span className="trust-badge-label">Backed</span>
                </div>
              </div>
            </div>
            {/* Enhanced CTA Button Hierarchy */}
            <div className="cta-container">
              {/* Primary CTA - Most prominent */}
              <div className="cta-primary-wrapper">
                <Button
                  variant="glass"
                  size="large"
                  className="cta-primary group"
                >
                  <Link to="/message-analyzer" className="cta-link">
                    <span className="cta-content">
                      <Icon name="Sparkles" size="md" className="cta-icon cta-icon-sparkle" />
                      <span className="cta-text">Start Free Analysis</span>
                      <Icon name="ArrowRight" size="sm" className="cta-icon cta-icon-arrow" />
                    </span>
                  </Link>
                  {/* Enhanced shimmer effect */}
                  <div className="cta-shimmer" aria-hidden="true"></div>
                  <div className="cta-glow" aria-hidden="true"></div>
                </Button>
                <div className="cta-pulse" aria-hidden="true"></div>
              </div>
              
              {/* Secondary CTA */}
              <Button
                variant="secondary"
                size="large"
                className="cta-secondary group"
              >
                <Link to="/dashboard" className="cta-link">
                  <span className="cta-content">
                    <Icon name="LayoutDashboard" size="md" className="cta-icon" />
                    <span className="cta-text">Explore Dashboard</span>
                  </span>
                </Link>
              </Button>
            </div>
            
            {/* Trust Signal with Security Badge */}
            <div className="trust-signal-container">
              <div className="trust-signal">
                <Icon name="Shield" size="sm" className="trust-signal-icon" />
                <span className="trust-signal-text">No credit card required</span>
                <span className="trust-signal-separator">•</span>
                <Icon name="Clock" size="sm" className="trust-signal-icon" />
                <span className="trust-signal-text">30-second setup</span>
                <span className="trust-signal-separator">•</span>
                <Icon name="Lock" size="sm" className="trust-signal-icon" />
                <span className="trust-signal-text">Privacy-first</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section className="py-20 bg-gradient-to-b from-white to-background-subtle/50" aria-labelledby="features-heading">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 id="features-heading" className="text-4xl lg:text-5xl font-bold mb-4 text-gray-800">Key Features</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Comprehensive tools designed to enhance your emotional intelligence and relationship clarity
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Dashboard Feature */}
            <Card 
              variant="floating" 
              className="group"
              hover={true}
              interactive={true}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Icon name="BarChart3" size="lg" className="text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">Dashboard</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">Your emotional command center with health scores, flag incidence graphs, and relationship sentiment maps.</p>
              <Link to="/dashboard" className="text-indigo-600 font-medium hover:text-indigo-800 transition-colors">
                Explore &rarr;
              </Link>
            </Card>

            {/* Message Analyzer Feature */}
            <Card 
              variant="floating" 
              className="group"
              hover={true}
              interactive={true}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Icon name="MessageSquare" size="lg" className="text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">Message Analyzer</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">Scan messages to uncover emotional red flags, conflict cycles, and communication patterns.</p>
              <Link to="/message-analyzer" className="text-purple-600 font-medium hover:text-purple-800 transition-colors">
                Analyze &rarr;
              </Link>
            </Card>

            {/* Relationship Hub Feature */}
            <Card 
              variant="floating" 
              className="group"
              hover={true}
              interactive={true}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-rose-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Icon name="Users" size="lg" className="text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">Relationship Hub</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">Interactive profiles for key connections with health scores, flag history, and sentiment trends.</p>
              <Link to="/relationships" className="text-pink-600 font-medium hover:text-pink-800 transition-colors">
                Connect &rarr;
              </Link>
            </Card>

            {/* Growth Center Feature */}
            <Card 
              variant="floating" 
              className="group"
              hover={true}
              interactive={true}
            >
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <Icon name="TrendingUp" size="lg" className="text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">Growth Center</h3>
              <p className="text-gray-600 mb-6 leading-relaxed">Personalized plans with self-inquiry prompts, micro-practices, and goal-setting frameworks.</p>
              <Link to="/growth-center" className="text-green-600 font-medium hover:text-green-800 transition-colors">
                Grow &rarr;
              </Link>
            </Card>
          </div>
        </div>
      </section>

      {/* Enhanced How It Works Section */}
      <div className="py-20 bg-gradient-to-b from-background-subtle/50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold mb-4 text-gray-800">How It Works</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Advanced AI meets emotional intelligence research
            </p>
          </div>
          
          <div className="max-w-6xl mx-auto space-y-16">
            <div className="flex flex-col lg:flex-row items-center gap-12">
              <div className="lg:w-1/2 space-y-6">
                <h3 className="text-3xl font-semibold text-gray-800">Natural Language Processing + Behavioral AI</h3>
                <p className="text-lg text-gray-600 leading-relaxed">
                  Built on cognitive and emotional models: CBT, IFS, NVC, Polyvagal Theory. Maps tone, intention, and communication context to distinguish chronic toxicity from normal conflict.
                </p>
                <div className="flex flex-wrap gap-3">
                  <span className="px-4 py-2 bg-indigo-100 text-indigo-800 rounded-full text-sm font-medium">CBT Framework</span>
                  <span className="px-4 py-2 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">IFS Model</span>
                  <span className="px-4 py-2 bg-pink-100 text-pink-800 rounded-full text-sm font-medium">NVC Principles</span>
                </div>
              </div>
              <div className="lg:w-1/2">
                <Card variant="glass" className="p-8">
                  <div className="h-64 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-2xl flex items-center justify-center relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-purple-600/20 animate-gradient-x"></div>
                    <Icon name="Monitor" size={128} className="text-indigo-600 relative z-10" />
                  </div>
                </Card>
              </div>
            </div>
            
            <div className="flex flex-col lg:flex-row-reverse items-center gap-12">
              <div className="lg:w-1/2 space-y-6">
                <h3 className="text-3xl font-semibold text-gray-800">Adaptive Feedback Engine</h3>
                <p className="text-lg text-gray-600 leading-relaxed">
                  Learns your patterns as you journal, set goals, and label behaviors. Adjusts insights based on your growth, preferences, and relational focus for increasingly personalized reflections.
                </p>
                <div className="flex flex-wrap gap-3">
                  <span className="px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">Pattern Learning</span>
                  <span className="px-4 py-2 bg-emerald-100 text-emerald-800 rounded-full text-sm font-medium">Goal Tracking</span>
                  <span className="px-4 py-2 bg-teal-100 text-teal-800 rounded-full text-sm font-medium">Personalization</span>
                </div>
              </div>
              <div className="lg:w-1/2">
                <Card variant="glass" className="p-8">
                  <div className="h-64 bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl flex items-center justify-center relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-600/20 animate-gradient-x" style={{animationDelay: '1s'}}></div>
                    <Icon name="Shield" size={128} className="text-green-600 relative z-10" />
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced CTA Section */}
      <div className="relative py-24 bg-gradient-to-br from-indigo-900 via-purple-900 to-indigo-800 text-white overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-600/20 via-purple-800/20 to-indigo-800/20 animate-gradient-x"></div>
        <div className="absolute top-10 right-10 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-purple-400/20 rounded-full blur-xl animate-float" style={{animationDelay: '3s'}}></div>
        
        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-4xl lg:text-6xl font-bold mb-8 leading-tight">Ready to transform your relationships?</h2>
          <p className="text-xl lg:text-2xl mb-12 max-w-3xl mx-auto opacity-90 leading-relaxed">
            My ÆI is a mirror for your blind spots, a framework for transformation, and a path to emotional freedom — in language your nervous system and spirit can understand.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-6">
            <Button
              variant="glass"
              size="large"
              className="text-lg font-semibold shadow-floating hover:shadow-glow"
            >
              <Link to="/message-analyzer" className="block">
                Start Your Journey
              </Link>
            </Button>
            <Button
              variant="secondary"
              size="large"
              className="text-lg font-semibold border-white/30 hover:bg-white/20"
            >
              <Link to="/dashboard" className="block">
                Explore Features
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
