import React from 'react';
import DashboardWidget from '../components/dashboard/DashboardWidget';
import AnimatedCounter from '../components/dashboard/AnimatedCounter';
import ProgressRing from '../components/dashboard/ProgressRing';
import { 
  NoDataIllustration, 
  NoRelationshipsIllustration, 
  NoMessagesIllustration,
  ErrorIllustration 
} from '../components/dashboard/EmptyStateIllustrations';
import Button from '../components/Button';
import Icon from '../components/modern/Icon';

const DashboardShowcase = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-8 bg-gradient-to-r from-primary via-secondary-lavender to-secondary-violet bg-clip-text text-transparent">
        Dashboard Components Showcase
      </h1>

      {/* Widget Variations */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Dashboard Widgets</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <DashboardWidget
            title="Default Widget"
            subtitle="Basic style"
            icon="Activity"
            iconColor="primary"
            variant="default"
            tooltip="This is a helpful tooltip explaining what this widget shows"
          >
            <div className="h-32 flex items-center justify-center">
              <p className="text-gray-500">Widget content goes here</p>
            </div>
          </DashboardWidget>

          <DashboardWidget
            title="Glass Widget"
            subtitle="Glassmorphism effect"
            icon="Layers"
            iconColor="secondary"
            variant="glass"
            action={
              <Button variant="ghost" size="sm">
                <Icon name="MoreVertical" size="sm" />
              </Button>
            }
          >
            <div className="h-32 flex items-center justify-center">
              <p className="text-gray-500">Glass effect content</p>
            </div>
          </DashboardWidget>

          <DashboardWidget
            title="Floating Widget"
            subtitle="Elevated shadow"
            icon="Zap"
            iconColor="warning"
            variant="floating"
          >
            <div className="h-32 flex items-center justify-center">
              <p className="text-gray-500">Floating content</p>
            </div>
          </DashboardWidget>
        </div>
      </section>

      {/* Progress Rings */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Progress Rings</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <DashboardWidget title="Health Score" variant="glass">
            <div className="flex justify-center py-4">
              <ProgressRing value={85} color="positive" label="Excellent" />
            </div>
          </DashboardWidget>

          <DashboardWidget title="Communication" variant="glass">
            <div className="flex justify-center py-4">
              <ProgressRing value={62} color="warning" label="Good" />
            </div>
          </DashboardWidget>

          <DashboardWidget title="Boundaries" variant="glass">
            <div className="flex justify-center py-4">
              <ProgressRing value={45} color="negative" label="Needs Work" />
            </div>
          </DashboardWidget>

          <DashboardWidget title="Growth" variant="glass">
            <div className="flex justify-center py-4">
              <ProgressRing value={78} color="secondary" label="Progressing" />
            </div>
          </DashboardWidget>
        </div>
      </section>

      {/* Animated Counters */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Animated Counters</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <DashboardWidget title="Messages Analyzed" icon="MessageSquare" iconColor="primary" variant="gradient">
            <div className="text-center py-4">
              <div className="text-3xl font-bold text-primary">
                <AnimatedCounter value={1234} duration={2} />
              </div>
              <p className="text-sm text-gray-500 mt-2">This month</p>
            </div>
          </DashboardWidget>

          <DashboardWidget title="Active Relationships" icon="Users" iconColor="secondary" variant="gradient">
            <div className="text-center py-4">
              <div className="text-3xl font-bold text-secondary-lavender">
                <AnimatedCounter value={42} duration={1.5} />
              </div>
              <p className="text-sm text-gray-500 mt-2">Currently tracking</p>
            </div>
          </DashboardWidget>

          <DashboardWidget title="Growth Rate" icon="TrendingUp" iconColor="positive" variant="gradient">
            <div className="text-center py-4">
              <div className="text-3xl font-bold text-emotion-positive">
                <AnimatedCounter value={89.5} decimals={1} suffix="%" duration={2.5} />
              </div>
              <p className="text-sm text-gray-500 mt-2">Improvement score</p>
            </div>
          </DashboardWidget>
        </div>
      </section>

      {/* Empty States */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Empty State Illustrations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <DashboardWidget
            title="No Data"
            variant="glass"
            isEmpty={true}
            emptyState={
              <>
                <NoDataIllustration className="mb-4" />
                <p className="text-gray-500 mb-4">No data available yet</p>
                <Button variant="soft" size="sm">Get Started</Button>
              </>
            }
          />

          <DashboardWidget
            title="No Messages"
            variant="glass"
            isEmpty={true}
            emptyState={
              <>
                <NoMessagesIllustration className="mb-4" />
                <p className="text-gray-500 mb-4">No messages analyzed</p>
                <Button variant="soft" size="sm">Analyze Message</Button>
              </>
            }
          />

          <DashboardWidget
            title="No Relationships"
            variant="glass"
            isEmpty={true}
            emptyState={
              <>
                <NoRelationshipsIllustration className="mb-4" />
                <p className="text-gray-500 mb-4">No relationships tracked</p>
                <Button variant="soft" size="sm">Add Relationship</Button>
              </>
            }
          />

          <DashboardWidget
            title="Error State"
            variant="glass"
            error="Failed to load data"
            errorState={
              <>
                <ErrorIllustration className="mb-4" />
                <p className="text-gray-700 font-medium mb-2">Something went wrong</p>
                <p className="text-gray-500 mb-4">Failed to load data</p>
                <Button variant="negative" size="sm">Retry</Button>
              </>
            }
          />
        </div>
      </section>

      {/* Loading States */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-6">Loading States</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <DashboardWidget
            title="Loading Data"
            subtitle="Please wait..."
            icon="Clock"
            iconColor="primary"
            variant="glass"
            isLoading={true}
          />

          <DashboardWidget
            title="Processing"
            subtitle="Analyzing patterns..."
            icon="Cpu"
            iconColor="secondary"
            variant="glass"
            isLoading={true}
          />

          <DashboardWidget
            title="Fetching"
            subtitle="Getting latest data..."
            icon="Download"
            iconColor="positive"
            variant="glass"
            isLoading={true}
          />
        </div>
      </section>
    </div>
  );
};

export default DashboardShowcase;
