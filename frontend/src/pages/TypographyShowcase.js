import React from 'react';
import Card from '../components/Card';

const TypographyShowcase = () => {
  return (
    <div className="min-h-screen surface-secondary p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="heading-1 text-primary mb-8">Typography System</h1>
        
        {/* Heading Hierarchy */}
        <section className="mb-12">
          <Card variant="elevated" padding="large">
            <h2 className="heading-2 text-primary mb-6">Heading Hierarchy</h2>
            
            <div className="space-y-6">
              <div>
                <h1 className="heading-1 text-primary">Heading 1 - Main Page Titles</h1>
                <p className="body-small text-secondary mt-2">
                  Font: 32px → 40px → 48px → 56px | Weight: 800 | Line Height: 1.2
                </p>
              </div>
              
              <div>
                <h2 className="heading-2 text-primary">Heading 2 - Section Titles</h2>
                <p className="body-small text-secondary mt-2">
                  Font: 28px → 32px → 36px → 44px | Weight: 700 | Line Height: 1.25
                </p>
              </div>
              
              <div>
                <h3 className="heading-3 text-primary">Heading 3 - Subsection Titles</h3>
                <p className="body-small text-secondary mt-2">
                  Font: 24px → 28px → 32px → 36px | Weight: 600 | Line Height: 1.3
                </p>
              </div>
              
              <div>
                <h4 className="heading-4 text-primary">Heading 4 - Component Titles</h4>
                <p className="body-small text-secondary mt-2">
                  Font: 20px → 24px → 28px | Weight: 600 | Line Height: 1.35
                </p>
              </div>
              
              <div>
                <h5 className="heading-5 text-primary">Heading 5 - Card Titles</h5>
                <p className="body-small text-secondary mt-2">
                  Font: 18px | Weight: 600 | Line Height: 1.4
                </p>
              </div>
              
              <div>
                <h6 className="heading-6 text-primary">Heading 6 - Small Titles</h6>
                <p className="body-small text-secondary mt-2">
                  Font: 16px | Weight: 600 | Line Height: 1.4
                </p>
              </div>
            </div>
          </Card>
        </section>

        {/* Body Text Styles */}
        <section className="mb-12">
          <Card variant="elevated" padding="large">
            <h2 className="heading-2 text-primary mb-6">Body Text Styles</h2>
            
            <div className="space-y-6">
              <div>
                <p className="body-large text-primary">
                  Body Large - Used for important paragraphs and lead text that needs emphasis.
                </p>
                <p className="body-small text-secondary mt-2">
                  Font: 18px | Weight: 400 | Line Height: 1.6
                </p>
              </div>
              
              <div>
                <p className="body-base text-primary">
                  Body Base - The standard body text used throughout the application for most content.
                </p>
                <p className="body-small text-secondary mt-2">
                  Font: 16px | Weight: 400 | Line Height: 1.6
                </p>
              </div>
              
              <div>
                <p className="body-small text-primary">
                  Body Small - Used for secondary information, captions, and supporting text.
                </p>
                <p className="body-small text-secondary mt-2">
                  Font: 14px | Weight: 400 | Line Height: 1.5
                </p>
              </div>
              
              <div>
                <p className="body-xs text-primary">
                  Body Extra Small - Used for labels, metadata, and fine print.
                </p>
                <p className="body-small text-secondary mt-2">
                  Font: 12px | Weight: 400 | Line Height: 1.4
                </p>
              </div>
            </div>
          </Card>
        </section>

        {/* Special Typography */}
        <section className="mb-12">
          <Card variant="elevated" padding="large">
            <h2 className="heading-2 text-primary mb-6">Special Typography</h2>
            
            <div className="space-y-8">
              <div>
                <h1 className="text-hero text-primary">Hero Text</h1>
                <p className="body-small text-secondary mt-2">
                  Font: 40px → 64px → 80px → 96px | Weight: 900 | Line Height: 1.1
                </p>
                <p className="body-small text-secondary">
                  Used for landing page heroes and major impact statements.
                </p>
              </div>
              
              <div>
                <h2 className="text-display text-primary">Display Text</h2>
                <p className="body-small text-secondary mt-2">
                  Font: 32px → 48px | Weight: 800 | Line Height: 1.15
                </p>
                <p className="body-small text-secondary">
                  Used for prominent section headers and feature callouts.
                </p>
              </div>
              
              <div>
                <p className="text-lead text-primary">
                  Lead Text - This is used for introductory paragraphs and important descriptions that need to stand out from regular body text.
                </p>
                <p className="body-small text-secondary mt-2">
                  Font: 20px → 22px | Weight: 400 | Line Height: 1.6
                </p>
              </div>
            </div>
          </Card>
        </section>

        {/* Usage Examples */}
        <section className="mb-12">
          <Card variant="elevated" padding="large">
            <h2 className="heading-2 text-primary mb-6">Usage Examples</h2>
            
            <div className="space-y-8">
              {/* Article Example */}
              <div className="border-l-4 border-primary pl-6">
                <h3 className="heading-3 text-primary mb-4">
                  Understanding Emotional Intelligence in Relationships
                </h3>
                <p className="text-lead text-primary mb-4">
                  Emotional intelligence plays a crucial role in building and maintaining healthy relationships.
                </p>
                <p className="body-base text-primary mb-4">
                  Research shows that individuals with higher emotional intelligence are better at recognizing their own emotions and those of others. This awareness leads to more effective communication and stronger interpersonal connections.
                </p>
                <p className="body-base text-primary mb-4">
                  The My ÆI platform helps users develop these skills through advanced analysis of communication patterns and personalized feedback.
                </p>
                <p className="body-small text-secondary">
                  Last updated: March 2024 | Reading time: 3 minutes
                </p>
              </div>

              {/* Card Example */}
              <Card variant="default" padding="medium">
                <h4 className="heading-4 text-primary mb-2">Analysis Results</h4>
                <p className="body-base text-primary mb-3">
                  Your recent conversation shows positive emotional patterns with high empathy scores.
                </p>
                <div className="flex items-center justify-between">
                  <span className="body-small text-secondary">Confidence: 94%</span>
                  <span className="body-xs text-success">Healthy Communication</span>
                </div>
              </Card>
            </div>
          </Card>
        </section>

        {/* Implementation Guide */}
        <section>
          <Card variant="elevated" padding="large">
            <h2 className="heading-2 text-primary mb-6">Implementation Guide</h2>
            
            <div className="prose max-w-none">
              <h3 className="heading-3 text-primary mb-4">CSS Classes</h3>
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-6">
                <pre className="body-small text-primary">
{`<!-- Headings -->
<h1 className="heading-1">Page Title</h1>
<h2 className="heading-2">Section Title</h2>
<h3 className="heading-3">Subsection Title</h3>

<!-- Body Text -->
<p className="body-large">Important paragraph</p>
<p className="body-base">Standard paragraph</p>
<p className="body-small">Secondary text</p>

<!-- Special Typography -->
<h1 className="text-hero">Hero Text</h1>
<h2 className="text-display">Display Text</h2>
<p className="text-lead">Lead paragraph</p>`}
                </pre>
              </div>
              
              <h3 className="heading-3 text-primary mb-4">Best Practices</h3>
              <ul className="body-base text-primary space-y-2">
                <li>Use semantic HTML elements (h1, h2, p) with typography classes</li>
                <li>Maintain proper heading hierarchy (don't skip levels)</li>
                <li>Use text-hero sparingly for maximum impact</li>
                <li>Combine typography classes with color tokens for consistency</li>
                <li>Test typography at different screen sizes</li>
              </ul>
            </div>
          </Card>
        </section>
      </div>
    </div>
  );
};

export default TypographyShowcase;
