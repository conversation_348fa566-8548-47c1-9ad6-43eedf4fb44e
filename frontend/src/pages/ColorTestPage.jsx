import React, { useState } from 'react';

const ColorTestPage = () => {
  const [darkMode, setDarkMode] = useState(false);

  // Color scales
  const colorScales = {
    primary: {
      name: 'Primary',
      colors: [
        { shade: '50', hex: '#f0fdfa' },
        { shade: '100', hex: '#ccfbf1' },
        { shade: '200', hex: '#99f6e4' },
        { shade: '300', hex: '#5eead4' },
        { shade: '400', hex: '#2dd4bf' },
        { shade: '500', hex: '#2A9D8F', isBase: true },
        { shade: '600', hex: '#0d9488' },
        { shade: '700', hex: '#0f766e' },
        { shade: '800', hex: '#115e59' },
        { shade: '900', hex: '#134e4a' },
        { shade: '950', hex: '#042f2e' },
      ]
    },
    coral: {
      name: 'Secondary - Coral',
      colors: [
        { shade: '50', hex: '#fef2f2' },
        { shade: '100', hex: '#fee2e2' },
        { shade: '200', hex: '#fecaca' },
        { shade: '300', hex: '#fca5a5' },
        { shade: '400', hex: '#f87171' },
        { shade: '500', hex: '#E76F51', isBase: true },
        { shade: '600', hex: '#dc2626' },
        { shade: '700', hex: '#b91c1c' },
        { shade: '800', hex: '#991b1b' },
        { shade: '900', hex: '#7f1d1d' },
        { shade: '950', hex: '#450a0a' },
      ]
    },
    lavender: {
      name: 'Secondary - Lavender',
      colors: [
        { shade: '50', hex: '#f5f3ff' },
        { shade: '100', hex: '#ede9fe' },
        { shade: '200', hex: '#ddd6fe' },
        { shade: '300', hex: '#c4b5fd' },
        { shade: '400', hex: '#a78bfa' },
        { shade: '500', hex: '#9D8DF1', isBase: true },
        { shade: '600', hex: '#8b5cf6' },
        { shade: '700', hex: '#7c3aed' },
        { shade: '800', hex: '#6d28d9' },
        { shade: '900', hex: '#5b21b6' },
        { shade: '950', hex: '#2e1065' },
      ]
    },
    sage: {
      name: 'Secondary - Sage',
      colors: [
        { shade: '50', hex: '#f0fdf4' },
        { shade: '100', hex: '#dcfce7' },
        { shade: '200', hex: '#bbf7d0' },
        { shade: '300', hex: '#86efac' },
        { shade: '400', hex: '#4ade80' },
        { shade: '500', hex: '#87A878', isBase: true },
        { shade: '600', hex: '#16a34a' },
        { shade: '700', hex: '#15803d' },
        { shade: '800', hex: '#166534' },
        { shade: '900', hex: '#14532d' },
        { shade: '950', hex: '#052e16' },
      ]
    },
  };

  const emotionalColors = [
    { name: 'Positive', base: '#4AD295', text: '#15803d', textDark: '#86efac' },
    { name: 'Neutral', base: '#A5B4FC', text: '#334155', textDark: '#cbd5e1' },
    { name: 'Negative', base: '#E56B6F', text: '#b91c1c', textDark: '#fca5a5' },
  ];

  const stateColors = [
    { name: 'Error', color: '#dc2626', light: '#fef2f2', dark: '#7f1d1d' },
    { name: 'Warning', color: '#f59e0b', light: '#fef3c7', dark: '#92400e' },
    { name: 'Success', color: '#10b981', light: '#d1fae5', dark: '#064e3b' },
    { name: 'Info', color: '#3b82f6', light: '#dbeafe', dark: '#1e3a8a' },
  ];

  const gradients = [
    { name: 'Primary', class: 'bg-gradient-primary' },
    { name: 'Secondary', class: 'bg-gradient-secondary' },
    { name: 'Emotional', class: 'bg-gradient-emotional' },
    { name: 'Warm', class: 'bg-gradient-warm' },
    { name: 'Cool', class: 'bg-gradient-cool' },
    { name: 'Sunset', class: 'bg-gradient-sunset' },
  ];

  const glassVariants = [
    { name: 'White 10%', class: 'bg-glass-white-10' },
    { name: 'White 30%', class: 'bg-glass-white-30' },
    { name: 'White 50%', class: 'bg-glass-white-50' },
    { name: 'White 80%', class: 'bg-glass-white-80' },
    { name: 'Primary 20%', class: 'bg-glass-primary-20' },
    { name: 'Lavender 20%', class: 'bg-glass-lavender-20' },
  ];

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-background-primary-dark' : 'bg-background-primary'}`}>
      {/* Header */}
      <div className="sticky top-0 z-50 bg-background-elevated dark:bg-background-elevated-dark border-b border-border-primary dark:border-border-primary-dark">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold text-text-primary dark:text-text-primary-dark">
              Color Palette Test Page
            </h1>
            <button
              onClick={() => setDarkMode(!darkMode)}
              className="px-4 py-2 rounded-lg bg-primary-500 text-white hover:bg-primary-600 transition-colors"
            >
              {darkMode ? '☀️ Light Mode' : '🌙 Dark Mode'}
            </button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8 space-y-12">
        {/* Color Scales */}
        <section>
          <h2 className="text-2xl font-semibold mb-6 text-text-primary dark:text-text-primary-dark">
            Color Scales
          </h2>
          <div className="space-y-8">
            {Object.entries(colorScales).map(([key, scale]) => (
              <div key={key}>
                <h3 className="text-xl font-medium mb-4 text-text-primary dark:text-text-primary-dark">
                  {scale.name}
                </h3>
                <div className="grid grid-cols-11 gap-2">
                  {scale.colors.map((color) => (
                    <div key={color.shade} className="text-center">
                      <div
                        className={`h-20 rounded-lg shadow-md ${color.isBase ? 'ring-2 ring-offset-2 ring-gray-400' : ''}`}
                        style={{ backgroundColor: color.hex }}
                      />
                      <p className="text-sm mt-2 text-text-secondary dark:text-text-secondary-dark">
                        {color.shade}
                      </p>
                      <p className="text-xs text-text-tertiary dark:text-text-tertiary-dark">
                        {color.hex}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Emotional Colors */}
        <section>
          <h2 className="text-2xl font-semibold mb-6 text-text-primary dark:text-text-primary-dark">
            Emotional Colors
          </h2>
          <div className="grid grid-cols-3 gap-6">
            {emotionalColors.map((emotion) => (
              <div key={emotion.name} className="space-y-4">
                <div
                  className="h-24 rounded-lg shadow-md flex items-center justify-center text-white font-medium"
                  style={{ backgroundColor: emotion.base }}
                >
                  {emotion.name}
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-8 h-8 rounded"
                      style={{ backgroundColor: emotion.text }}
                    />
                    <p className="text-sm text-text-secondary dark:text-text-secondary-dark">
                      Text (Light): {emotion.text}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-8 h-8 rounded"
                      style={{ backgroundColor: emotion.textDark }}
                    />
                    <p className="text-sm text-text-secondary dark:text-text-secondary-dark">
                      Text (Dark): {emotion.textDark}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* State Colors */}
        <section>
          <h2 className="text-2xl font-semibold mb-6 text-text-primary dark:text-text-primary-dark">
            State Colors
          </h2>
          <div className="grid grid-cols-4 gap-6">
            {stateColors.map((state) => (
              <div key={state.name} className="space-y-2">
                <div
                  className="h-20 rounded-lg shadow-md flex items-center justify-center text-white font-medium"
                  style={{ backgroundColor: state.color }}
                >
                  {state.name}
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div
                    className="h-12 rounded flex items-center justify-center text-xs"
                    style={{ backgroundColor: state.light, color: state.color }}
                  >
                    Light
                  </div>
                  <div
                    className="h-12 rounded flex items-center justify-center text-xs text-white"
                    style={{ backgroundColor: state.dark }}
                  >
                    Dark
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Gradients */}
        <section>
          <h2 className="text-2xl font-semibold mb-6 text-text-primary dark:text-text-primary-dark">
            Gradients
          </h2>
          <div className="grid grid-cols-3 gap-6">
            {gradients.map((gradient) => (
              <div key={gradient.name} className="space-y-2">
                <div className={`h-32 rounded-lg shadow-md ${gradient.class}`} />
                <p className="text-center text-text-secondary dark:text-text-secondary-dark">
                  {gradient.name}
                </p>
              </div>
            ))}
          </div>
        </section>

        {/* Glass Effects */}
        <section>
          <h2 className="text-2xl font-semibold mb-6 text-text-primary dark:text-text-primary-dark">
            Glass Morphism Effects
          </h2>
          <div className="relative">
            {/* Background with gradient */}
            <div className="absolute inset-0 bg-gradient-mesh rounded-lg" />
            <div className="relative grid grid-cols-3 gap-6 p-8">
              {glassVariants.map((variant) => (
                <div
                  key={variant.name}
                  className={`h-32 rounded-lg ${variant.class} backdrop-blur-md border border-glass-white-20 shadow-glass flex items-center justify-center`}
                >
                  <p className="text-text-primary dark:text-text-primary-dark font-medium">
                    {variant.name}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Typography Contrast Examples */}
        <section>
          <h2 className="text-2xl font-semibold mb-6 text-text-primary dark:text-text-primary-dark">
            Typography & Contrast
          </h2>
          <div className="space-y-8">
            {/* Primary Text Examples */}
            <div>
              <h3 className="text-xl font-medium mb-4 text-text-primary dark:text-text-primary-dark">
                Text on Backgrounds
              </h3>
              <div className="grid grid-cols-2 gap-6">
                {/* Light mode examples */}
                <div className="space-y-4">
                  <h4 className="font-medium text-text-secondary dark:text-text-secondary-dark">Light Mode</h4>
                  <div className="p-4 bg-white rounded-lg shadow">
                    <p className="text-primary-700">Primary text on white (AA compliant)</p>
                    <p className="text-secondary-coral-700 mt-2">Coral text on white (AA compliant)</p>
                    <p className="text-secondary-lavender-700 mt-2">Lavender text on white (AA compliant)</p>
                    <p className="text-secondary-sage-700 mt-2">Sage text on white (AA compliant)</p>
                  </div>
                </div>
                {/* Dark mode examples */}
                <div className="space-y-4">
                  <h4 className="font-medium text-text-secondary dark:text-text-secondary-dark">Dark Mode</h4>
                  <div className="p-4 bg-background-primary-dark rounded-lg shadow">
                    <p className="text-primary-300">Primary text on dark (AAA compliant)</p>
                    <p className="text-secondary-coral-300 mt-2">Coral text on dark (AAA compliant)</p>
                    <p className="text-secondary-lavender-300 mt-2">Lavender text on dark (AAA compliant)</p>
                    <p className="text-secondary-sage-300 mt-2">Sage text on dark (AAA compliant)</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Interactive States */}
            <div>
              <h3 className="text-xl font-medium mb-4 text-text-primary dark:text-text-primary-dark">
                Interactive States
              </h3>
              <div className="flex flex-wrap gap-4">
                <button className="px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 active:bg-primary-700 transition-colors">
                  Primary Button
                </button>
                <button className="px-6 py-3 bg-secondary-coral-500 text-white rounded-lg hover:bg-secondary-coral-600 active:bg-secondary-coral-700 transition-colors">
                  Coral Button
                </button>
                <button className="px-6 py-3 bg-secondary-lavender-500 text-white rounded-lg hover:bg-secondary-lavender-600 active:bg-secondary-lavender-700 transition-colors">
                  Lavender Button
                </button>
                <button className="px-6 py-3 bg-primary-disabled text-gray-400 rounded-lg cursor-not-allowed">
                  Disabled Button
                </button>
              </div>
            </div>

            {/* Text Gradients */}
            <div>
              <h3 className="text-xl font-medium mb-4 text-text-primary dark:text-text-primary-dark">
                Text Gradients
              </h3>
              <div className="space-y-4">
                <h1 className="text-4xl font-bold text-gradient-primary">
                  Primary Gradient Text
                </h1>
                <h1 className="text-4xl font-bold text-gradient-secondary">
                  Secondary Gradient Text
                </h1>
                <h1 className="text-4xl font-bold text-gradient-emotional">
                  Emotional Gradient Text
                </h1>
              </div>
            </div>
          </div>
        </section>

        {/* Shadow Examples */}
        <section>
          <h2 className="text-2xl font-semibold mb-6 text-text-primary dark:text-text-primary-dark">
            Shadow System
          </h2>
          <div className="grid grid-cols-3 gap-6">
            <div className="p-6 bg-white dark:bg-background-elevated-dark rounded-lg shadow-soft">
              <p className="text-text-primary dark:text-text-primary-dark">Soft Shadow</p>
            </div>
            <div className="p-6 bg-white dark:bg-background-elevated-dark rounded-lg shadow-elevated">
              <p className="text-text-primary dark:text-text-primary-dark">Elevated Shadow</p>
            </div>
            <div className="p-6 bg-white dark:bg-background-elevated-dark rounded-lg shadow-floating">
              <p className="text-text-primary dark:text-text-primary-dark">Floating Shadow</p>
            </div>
            <div className="p-6 bg-primary-500 text-white rounded-lg shadow-glow-primary">
              <p>Primary Glow</p>
            </div>
            <div className="p-6 bg-secondary-coral-500 text-white rounded-lg shadow-glow-coral">
              <p>Coral Glow</p>
            </div>
            <div className="p-6 bg-secondary-lavender-500 text-white rounded-lg shadow-glow-lavender">
              <p>Lavender Glow</p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ColorTestPage;
