import React, { useState } from 'react';
import { useAppContext } from '../contexts/AppContext';
import { useFaith } from '../contexts/FaithContext';
import Card from '../components/Card';
import Button from '../components/Button';
import { Textarea, Select } from '../components/FormElements';

const MessageAnalyzer = () => {
  const { analyzeMessage, isLoading, error, relationships, connectionStatus, checkBackendConnection } = useAppContext();
  const { faithModeEnabled, getFaithContent } = useFaith();
  
  const [messageText, setMessageText] = useState('');
  const [selectedRelationship, setSelectedRelationship] = useState('');
  const [context, setContext] = useState('');
  const [analysisResult, setAnalysisResult] = useState(null);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Handle connection status
  if (connectionStatus === 'checking') {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="flex justify-center items-center h-64 flex-col" variant="glass">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
          <p className="text-text-secondary">Checking connection to backend server...</p>
        </Card>
      </div>
    );
  }

  if (connectionStatus === 'error') {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card variant="glass" className="bg-emotion-negative/10 border-emotion-negative/20">
          <h2 className="text-xl font-bold mb-2 text-emotion-negative">Connection Error</h2>
          <p className="mb-4 text-text-primary">Unable to connect to the backend server. Please make sure the server is running and accessible.</p>
          <Button 
            onClick={checkBackendConnection}
            variant="danger"
          >
            Retry Connection
          </Button>
        </Card>
      </div>
    );
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!messageText.trim()) return;
    
    try {
      const result = await analyzeMessage(
        messageText, 
        context || null, 
        selectedRelationship || null
      );
      
      setAnalysisResult(result);
      
      // Clear form if successful
      if (result) {
        setMessageText('');
        setContext('');
      }
    } catch (err) {
      console.error('Analysis submission error:', err);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header Section */}
      <div className="mb-8 text-center">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary-dark bg-clip-text text-transparent mb-4">
          Message Analyzer
        </h1>
        <p className="text-text-secondary text-lg max-w-2xl mx-auto">
          Analyze messages for emotional red flags and get insights on communication patterns with AI-powered analysis.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Form */}
        <Card variant="glass" hover="lift" className="h-fit">
          <h2 className="text-xl font-semibold text-text-primary mb-6 flex items-center gap-2">
            <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Analyze a Message
          </h2>
          
          {error && (
            <Card variant="glass" className="mb-6 bg-emotion-negative/10 border-emotion-negative/30">
              <p className="text-emotion-negative font-medium">{error}</p>
            </Card>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <Textarea
              id="message-text"
              label="Message Text"
              rows={6}
              placeholder="Paste a message or conversation to analyze..."
              value={messageText}
              onChange={(e) => setMessageText(e.target.value)}
              required
              disabled={isLoading}
              variant="glass"
            />
            <div className="text-xs text-text-secondary -mt-2">
              For best results, include both sides of the conversation.
            </div>
            
            <Select
              id="relationship"
              label="Relationship (Optional)"
              options={[
                { value: '', label: 'None' },
                ...relationships.map(relationship => ({
                  value: relationship.id,
                  label: `${relationship.name} (${relationship.type})`
                }))
              ]}
              value={selectedRelationship}
              onChange={(e) => setSelectedRelationship(e.target.value)}
              disabled={isLoading}
              variant="glass"
            />
            
            <div>
              <Button
                type="button"
                onClick={() => setShowAdvanced(!showAdvanced)}
                variant="ghost"
                size="sm"
                className="mb-4"
              >
                {showAdvanced ? 'Hide Advanced Options' : 'Show Advanced Options'}
              </Button>
              
              {showAdvanced && (
                <div className="animate-float">
                  <Textarea
                    id="context"
                    label="Additional Context"
                    rows={3}
                    placeholder="Add any relevant context about the situation..."
                    value={context}
                    onChange={(e) => setContext(e.target.value)}
                    disabled={isLoading}
                    variant="glass"
                  />
                </div>
              )}
            </div>
            
            <Button
              type="submit"
              variant="gradient"
              size="lg"
              fullWidth
              disabled={isLoading || !messageText.trim()}
              loading={isLoading}
              loadingText="Analyzing..."
            >
              Analyze Message
            </Button>
          </form>
        </Card>
        
        {/* Analysis Results */}
        <div>
          {analysisResult ? (
            <Card variant="glass" hover="lift" className="animate-float">
              <h2 className="text-xl font-semibold text-text-primary mb-6 flex items-center gap-2">
                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Analysis Results
              </h2>
              
              {/* Sentiment Badge */}
              <div className="mb-6">
                <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold shadow-soft ${
                  analysisResult.sentiment === 'positive' 
                    ? 'bg-emotion-positive/20 text-emotion-positive border border-emotion-positive/30' 
                    : analysisResult.sentiment === 'neutral' 
                    ? 'bg-primary/20 text-primary border border-primary/30' 
                    : 'bg-emotion-negative/20 text-emotion-negative border border-emotion-negative/30'
                }`}>
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    analysisResult.sentiment === 'positive' ? 'bg-emotion-positive' :
                    analysisResult.sentiment === 'neutral' ? 'bg-primary' : 'bg-emotion-negative'
                  }`}></div>
                  {analysisResult.sentiment.charAt(0).toUpperCase() + analysisResult.sentiment.slice(1)} Sentiment
                </div>
              </div>
              
              {/* Red Flags */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
                  <svg className="w-5 h-5 text-emotion-negative" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {analysisResult.flags.length > 0 
                    ? `Red Flags Detected (${analysisResult.flags.length})` 
                    : 'No Red Flags Detected'}
                </h3>
                
                {analysisResult.flags.length > 0 ? (
                  <div className="space-y-3">
                    {analysisResult.flags.map((flag, index) => (
                      <Card 
                        key={index} 
                        variant="glass" 
                        className="bg-emotion-negative/10 border-emotion-negative/30 hover:bg-emotion-negative/15 transition-all duration-300"
                      >
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0 w-8 h-8 bg-emotion-negative/20 rounded-full flex items-center justify-center">
                            <svg className="w-4 h-4 text-emotion-negative" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <div>
                            <p className="font-semibold text-emotion-negative">{flag.type}</p>
                            <p className="text-sm text-text-primary mt-1">{flag.description}</p>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card variant="glass" className="bg-emotion-positive/10 border-emotion-positive/30">
                    <div className="flex items-center gap-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-emotion-positive/20 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-emotion-positive" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <p className="text-emotion-positive font-medium">
                        No emotional red flags were detected in this message.
                      </p>
                    </div>
                  </Card>
                )}
              </div>
              
              {/* Interpretation */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-text-primary mb-3 flex items-center gap-2">
                  <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                  Interpretation
                </h3>
                <Card variant="glass" className="bg-background-subtle/50">
                  <p className="text-text-primary leading-relaxed">{analysisResult.interpretation}</p>
                </Card>
              </div>
              
              {/* Suggestions */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-text-primary mb-3 flex items-center gap-2">
                  <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Suggestions
                </h3>
                <Card variant="glass" className="bg-primary/5">
                  <ul className="space-y-3">
                    {analysisResult.suggestions.map((suggestion, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="flex-shrink-0 w-5 h-5 bg-primary/20 rounded-full flex items-center justify-center mt-0.5">
                          <div className="w-2 h-2 bg-primary rounded-full"></div>
                        </div>
                        <span className="text-text-primary">{suggestion}</span>
                      </li>
                    ))}
                  </ul>
                </Card>
              </div>
              
              {/* Faith-Based Insight */}
              {faithModeEnabled && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-text-primary mb-3 flex items-center gap-2">
                    <svg className="w-5 h-5 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    Faith-Based Insight
                  </h3>
                  <Card variant="glass" className="bg-secondary/10 border-secondary/30">
                    <p className="text-text-primary leading-relaxed">
                      {getFaithContent('reframe', { sentiment: analysisResult.sentiment }) || 
                       "Your faith tradition offers wisdom about communication and relationships that can help you navigate this situation with compassion and understanding."}
                    </p>
                  </Card>
                </div>
              )}
              
              <Button
                onClick={() => setAnalysisResult(null)}
                variant="ghost"
                className="mt-4"
              >
                Analyze Another Message
              </Button>
            </Card>
          ) : (
            <Card variant="glass" className="h-full flex flex-col items-center justify-center text-center min-h-[500px]">
              <div className="mb-6 animate-float">
                <div className="w-24 h-24 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center">
                  <svg className="w-12 h-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-text-primary mb-3">
                No Analysis Yet
              </h3>
              <p className="text-text-secondary mb-6 max-w-md">
                Enter a message on the left to analyze it for emotional red flags and communication patterns.
              </p>
              <Card variant="glass" className="text-sm text-text-secondary max-w-md bg-background-subtle/30">
                <p className="font-medium mb-3">Our AI will identify potential issues like:</p>
                <div className="grid grid-cols-2 gap-2 text-left">
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>Gaslighting</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>Manipulation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>Invalidation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                    <span>Blame-shifting</span>
                  </div>
                </div>
              </Card>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default MessageAnalyzer;
