import React, { useState, useEffect } from 'react';
import { runContrastTests, testGlassContrast, getContrastRatio, meetsWCAG } from '../utils/contrastChecker';
import Button from '../components/Button';
import Card from '../components/Card';

const ContrastTestPage = () => {
  const [contrastResults, setContrastResults] = useState({});
  const [glassTests, setGlassTests] = useState({});

  useEffect(() => {
    // Run contrast tests on component mount
    const results = runContrastTests();
    setContrastResults(results);

    // Test glass morphism on different backgrounds
    const glassTestResults = {
      lightBackground: testGlassContrast('#ffffff', '#ffffff', '#111827'),
      darkBackground: testGlassContrast('#0f172a', '#0f172a', '#f9fafb'),
      primaryBackground: testGlassContrast('#2A9D8F', '#ffffff', '#111827'),
      gradientBackground: testGlassContrast('#667eea', '#ffffff', '#111827'),
    };
    setGlassTests(glassTestResults);
  }, []);

  const ContrastResult = ({ label, result }) => (
    <div className="p-4 border border-default rounded-lg">
      <h4 className="font-semibold text-primary mb-2">{label}</h4>
      <div className="space-y-2 text-sm">
        <div>Contrast Ratio: <span className="font-mono">{result.contrastRatio?.toFixed(2)}</span></div>
        <div className="flex gap-4">
          <span className={`px-2 py-1 rounded text-xs ${result.meetsAA ? 'bg-success text-white' : 'bg-error text-white'}`}>
            WCAG AA: {result.meetsAA ? 'Pass' : 'Fail'}
          </span>
          <span className={`px-2 py-1 rounded text-xs ${result.meetsAAA ? 'bg-success text-white' : 'bg-warning text-white'}`}>
            WCAG AAA: {result.meetsAAA ? 'Pass' : 'Fail'}
          </span>
        </div>
        {result.recommendation && (
          <div className="text-warning text-xs mt-2">{result.recommendation}</div>
        )}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen surface-secondary p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-primary mb-8">Contrast & Accessibility Testing</h1>
        
        {/* Glass Morphism Visual Tests */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-primary mb-6">Glass Morphism Visual Tests</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Light Background */}
            <div className="relative h-64 bg-white rounded-lg overflow-hidden">
              <div className="absolute inset-4">
                <Card variant="glass" className="h-full flex flex-col justify-center items-center text-center">
                  <h3 className="font-semibold mb-2">Glass on Light</h3>
                  <p className="text-sm glass-text-muted">This text should be readable</p>
                  <Button variant="glass" size="small" className="mt-4">Button</Button>
                </Card>
              </div>
            </div>

            {/* Dark Background */}
            <div className="relative h-64 bg-gray-900 rounded-lg overflow-hidden">
              <div className="absolute inset-4">
                <Card variant="glass" className="h-full flex flex-col justify-center items-center text-center">
                  <h3 className="font-semibold mb-2">Glass on Dark</h3>
                  <p className="text-sm glass-text-muted">This text should be readable</p>
                  <Button variant="glass" size="small" className="mt-4">Button</Button>
                </Card>
              </div>
            </div>

            {/* Primary Background */}
            <div className="relative h-64 bg-primary rounded-lg overflow-hidden">
              <div className="absolute inset-4">
                <Card variant="glass" className="h-full flex flex-col justify-center items-center text-center">
                  <h3 className="font-semibold mb-2">Glass on Primary</h3>
                  <p className="text-sm glass-text-muted">This text should be readable</p>
                  <Button variant="glass" size="small" className="mt-4">Button</Button>
                </Card>
              </div>
            </div>

            {/* Gradient Background */}
            <div className="relative h-64 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg overflow-hidden">
              <div className="absolute inset-4">
                <Card variant="glass" className="h-full flex flex-col justify-center items-center text-center">
                  <h3 className="font-semibold mb-2">Glass on Gradient</h3>
                  <p className="text-sm glass-text-muted">This text should be readable</p>
                  <Button variant="glass" size="small" className="mt-4">Button</Button>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Contrast Test Results */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-primary mb-6">Glass Morphism Contrast Results</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Object.entries(glassTests).map(([key, result]) => (
              <ContrastResult key={key} label={key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} result={result} />
            ))}
          </div>
        </section>

        {/* Standard Color Combinations */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-primary mb-6">Standard Color Combinations</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Object.entries(contrastResults).filter(([key]) => !key.includes('glass')).map(([key, result]) => (
              <ContrastResult key={key} label={key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} result={result} />
            ))}
          </div>
        </section>

        {/* Component Examples */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-primary mb-6">Component Examples</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Primary Button */}
            <div className="p-6 surface-primary border border-default rounded-lg">
              <h3 className="font-semibold mb-4">Primary Button</h3>
              <Button variant="primary">Primary Action</Button>
            </div>

            {/* Secondary Button */}
            <div className="p-6 surface-primary border border-default rounded-lg">
              <h3 className="font-semibold mb-4">Secondary Button</h3>
              <Button variant="secondary">Secondary Action</Button>
            </div>

            {/* Danger Button */}
            <div className="p-6 surface-primary border border-default rounded-lg">
              <h3 className="font-semibold mb-4">Danger Button</h3>
              <Button variant="danger">Danger Action</Button>
            </div>

            {/* Ghost Button */}
            <div className="p-6 surface-primary border border-default rounded-lg">
              <h3 className="font-semibold mb-4">Ghost Button</h3>
              <Button variant="ghost">Ghost Action</Button>
            </div>

            {/* Glass Button on Different Backgrounds */}
            <div className="p-6 bg-gradient-to-br from-primary to-secondary-lavender rounded-lg">
              <h3 className="font-semibold mb-4 text-inverse">Glass Button</h3>
              <Button variant="glass">Glass Action</Button>
            </div>

            {/* Cards */}
            <div className="p-6 surface-secondary rounded-lg">
              <h3 className="font-semibold mb-4">Card Variants</h3>
              <div className="space-y-4">
                <Card variant="default" padding="small">
                  <p className="text-sm">Default Card</p>
                </Card>
                <Card variant="elevated" padding="small">
                  <p className="text-sm">Elevated Card</p>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Accessibility Guidelines */}
        <section>
          <h2 className="text-2xl font-semibold text-primary mb-6">Accessibility Guidelines</h2>
          <Card variant="elevated" padding="large">
            <div className="prose max-w-none">
              <h3 className="text-lg font-semibold mb-4">WCAG Contrast Requirements</h3>
              <ul className="space-y-2 text-sm">
                <li><strong>WCAG AA Normal Text:</strong> Minimum contrast ratio of 4.5:1</li>
                <li><strong>WCAG AA Large Text:</strong> Minimum contrast ratio of 3:1</li>
                <li><strong>WCAG AAA Normal Text:</strong> Minimum contrast ratio of 7:1</li>
                <li><strong>WCAG AAA Large Text:</strong> Minimum contrast ratio of 4.5:1</li>
              </ul>
              
              <h3 className="text-lg font-semibold mb-4 mt-6">Glass Morphism Best Practices</h3>
              <ul className="space-y-2 text-sm">
                <li>Use high opacity (95%+) for glass surfaces to ensure text readability</li>
                <li>Test glass effects on various background colors and patterns</li>
                <li>Provide fallback styles for browsers that don't support backdrop-filter</li>
                <li>Ensure sufficient contrast between glass text and the effective background color</li>
                <li>Consider using darker text on light glass surfaces and lighter text on dark glass surfaces</li>
              </ul>
            </div>
          </Card>
        </section>
      </div>
    </div>
  );
};

export default ContrastTestPage;
