import React, { useState, useEffect } from 'react';
import { useAppContext } from '../contexts/AppContext';
import { useFaith } from '../contexts/FaithContext';
import Card from '../components/Card';
import Button from '../components/Button';
import axios from 'axios';
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  BarChart, Bar, PieChart, Pie, Cell, Legend
} from 'recharts';

const Reports = () => {
  const { relationships, analysisHistory, dashboardData } = useAppContext();
  const { faithModeEnabled, getFaithContent } = useFaith();
  
  const [reportData, setReportData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dateRange, setDateRange] = useState(30); // days
  const [selectedReport, setSelectedReport] = useState('overview');

  useEffect(() => {
    fetchReportData();
  }, [dateRange]);

  const fetchReportData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const backendUrl = process.env.REACT_APP_BACKEND_URL || '';
      
      // Fetch comprehensive report data
      const [dashboardResponse, historyResponse] = await Promise.all([
        axios.get(`${backendUrl}/api/dashboard`),
        axios.get(`${backendUrl}/api/history?days=${dateRange}`)
      ]);
      
      const reportData = {
        ...dashboardResponse.data,
        history: historyResponse.data,
        dateRange: dateRange
      };
      
      setReportData(reportData);
    } catch (err) {
      console.error('Failed to fetch report data:', err);
      setError('Failed to load report data. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const exportReport = () => {
    const reportContent = {
      generatedAt: new Date().toISOString(),
      dateRange: `${dateRange} days`,
      summary: {
        totalAnalyses: reportData?.total_analyses || 0,
        healthScore: reportData?.health_score || 0,
        totalFlags: reportData?.total_flags_detected || 0,
        relationshipsTracked: relationships?.length || 0
      },
      sentiment_timeline: reportData?.sentiment_timeline || [],
      flag_counts: reportData?.flag_counts || {},
      relationships: relationships || []
    };
    
    const dataStr = JSON.stringify(reportContent, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `my-aei-report-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // Chart colors
  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088FE', '#00C49F'];
  
  // Prepare chart data
  const flagChartData = reportData?.flag_counts 
    ? Object.entries(reportData.flag_counts).map(([type, count]) => ({
        name: type.replace('_', ' '),
        count
      }))
    : [];

  const sentimentData = reportData?.sentiment_timeline 
    ? reportData.sentiment_timeline.map(([date, sentiment]) => ({
        date: new Date(date).toLocaleDateString(),
        value: sentiment === 'positive' ? 1 : sentiment === 'neutral' ? 0 : -1,
        sentiment
      }))
    : [];

  const relationshipHealthData = relationships 
    ? relationships.map(r => ({
        name: r.name,
        health_score: r.health_score,
        sentiment: r.sentiment
      }))
    : [];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background-subtle to-white flex items-center justify-center">
        <Card variant="glass" className="text-center p-12">
          <div className="w-16 h-16 mx-auto mb-6">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/30 border-t-primary"></div>
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Generating Reports</h2>
          <p className="text-gray-600">Analyzing your communication data...</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-primary via-secondary-lavender to-secondary-violet bg-clip-text text-transparent mb-3">
              Communication Reports
            </h1>
            <p className="text-gray-600 text-lg">
              Comprehensive analytics and insights from your communication patterns
            </p>
          </div>
          
          <div className="flex items-center space-x-4 mt-6 md:mt-0">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(parseInt(e.target.value))}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all duration-200"
            >
              <option value={7}>Last 7 days</option>
              <option value={30}>Last 30 days</option>
              <option value={90}>Last 3 months</option>
              <option value={365}>Last year</option>
            </select>
            
            <Button
              onClick={exportReport}
              variant="gradient"
              size="sm"
            >
              Export Report
            </Button>
          </div>
        </div>
      </div>

      {error && (
        <Card variant="negative" className="mb-8">
          <p className="text-white">{error}</p>
        </Card>
      )}

      {/* Report Type Navigation */}
      <Card variant="glass" className="mb-8">
        <div className="p-6">
          <div className="flex flex-wrap gap-2">
            {[
              { id: 'overview', name: 'Overview', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
              { id: 'communication', name: 'Communication Patterns', icon: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z' },
              { id: 'relationships', name: 'Relationship Health', icon: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z' },
              { id: 'flags', name: 'Flag Analysis', icon: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.081 16.5c-.77.833.192 2.5 1.732 2.5z' }
            ].map((tab) => (
              <Button
                key={tab.id}
                onClick={() => setSelectedReport(tab.id)}
                variant={selectedReport === tab.id ? "gradient" : "ghost"}
                size="sm"
                className="flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={tab.icon} />
                </svg>
                {tab.name}
              </Button>
            ))}
          </div>
        </div>
      </Card>

      {/* Report Content */}
      {selectedReport === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Key Metrics Cards */}
          <Card variant="glass" hover="lift" className="p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-secondary-lavender/20 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Health Score</p>
                <p className="text-2xl font-semibold bg-gradient-to-r from-emotion-positive to-emotion-positive-light bg-clip-text text-transparent">
                  {reportData?.health_score || 0}%
                </p>
              </div>
            </div>
          </Card>

          <Card variant="glass" hover="lift" className="p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-secondary-sage/20 to-secondary-mint/20 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-secondary-sage" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Messages Analyzed</p>
                <p className="text-2xl font-semibold text-gray-900">{reportData?.total_analyses || 0}</p>
              </div>
            </div>
          </Card>

          <Card variant="glass" hover="lift" className="p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-emotion-negative/20 to-accent-coral/20 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-emotion-negative" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Red Flags</p>
                <p className="text-2xl font-semibold bg-gradient-to-r from-emotion-negative to-accent-coral bg-clip-text text-transparent">
                  {reportData?.total_flags_detected || 0}
                </p>
              </div>
            </div>
          </Card>

          <Card variant="glass" hover="lift" className="p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-secondary-lavender/20 to-secondary-violet/20 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-secondary-lavender" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Relationships</p>
                <p className="text-2xl font-semibold text-gray-900">{relationships?.length || 0}</p>
              </div>
            </div>
          </Card>
        </div>
      )}

      {selectedReport === 'communication' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Sentiment Timeline */}
          <Card variant="glass" className="p-6">
            <h3 className="text-lg font-semibold bg-gradient-to-r from-primary to-secondary-lavender bg-clip-text text-transparent mb-4">
              Sentiment Over Time
            </h3>
            {sentimentData.length > 0 ? (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={sentimentData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(139, 92, 246, 0.1)" />
                    <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                    <YAxis 
                      ticks={[-1, 0, 1]} 
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => {
                        return value === 1 ? 'Positive' : value === 0 ? 'Neutral' : 'Negative';
                      }}
                    />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(139, 92, 246, 0.2)',
                        borderRadius: '12px',
                        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                      }}
                      formatter={(value) => {
                        return value === 1 ? 'Positive' : value === 0 ? 'Neutral' : 'Negative';
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="value" 
                      stroke="url(#sentimentGradient)" 
                      strokeWidth={3}
                      activeDot={{ r: 6, fill: '#8b5cf6' }} 
                    />
                    <defs>
                      <linearGradient id="sentimentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="#8b5cf6" />
                        <stop offset="100%" stopColor="#06b6d4" />
                      </linearGradient>
                    </defs>
                  </LineChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <Card variant="ghost" className="flex items-center justify-center h-80">
                <div className="text-center">
                  <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <p className="text-gray-500">No sentiment data available for the selected period</p>
                </div>
              </Card>
            )}
          </Card>

          {/* Communication Frequency */}
          <Card variant="glass" className="p-6">
            <h3 className="text-lg font-semibold bg-gradient-to-r from-secondary-sage to-secondary-mint bg-clip-text text-transparent mb-4">
              Communication Frequency
            </h3>
            <div className="space-y-6">
              <div className="flex justify-between items-center p-4 bg-gradient-to-r from-primary/5 to-secondary-lavender/5 rounded-lg">
                <span className="text-sm font-medium text-gray-600">Daily Average</span>
                <span className="text-lg font-semibold bg-gradient-to-r from-primary to-secondary-lavender bg-clip-text text-transparent">
                  {Math.round((reportData?.total_analyses || 0) / dateRange)} messages
                </span>
              </div>
              <div className="flex justify-between items-center p-4 bg-gradient-to-r from-secondary-sage/5 to-secondary-mint/5 rounded-lg">
                <span className="text-sm font-medium text-gray-600">Peak Activity</span>
                <span className="text-lg font-semibold text-gray-900">Weekdays</span>
              </div>
              <div className="flex justify-between items-center p-4 bg-gradient-to-r from-secondary-lavender/5 to-secondary-violet/5 rounded-lg">
                <span className="text-sm font-medium text-gray-600">Response Time</span>
                <span className="text-lg font-semibold text-gray-900">~2.5 hours</span>
              </div>
            </div>
          </Card>
        </div>
      )}

      {selectedReport === 'relationships' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Relationship Health Chart */}
          <Card variant="glass" className="p-6">
            <h3 className="text-lg font-semibold bg-gradient-to-r from-secondary-lavender to-secondary-violet bg-clip-text text-transparent mb-4">
              Relationship Health Scores
            </h3>
            {relationshipHealthData.length > 0 ? (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={relationshipHealthData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(139, 92, 246, 0.1)" />
                    <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                    <YAxis domain={[0, 100]} tick={{ fontSize: 12 }} />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(139, 92, 246, 0.2)',
                        borderRadius: '12px',
                        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Bar dataKey="health_score" fill="url(#healthGradient)" radius={[4, 4, 0, 0]} />
                    <defs>
                      <linearGradient id="healthGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" stopColor="#8b5cf6" />
                        <stop offset="100%" stopColor="#a78bfa" />
                      </linearGradient>
                    </defs>
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <Card variant="ghost" className="flex items-center justify-center h-80">
                <div className="text-center">
                  <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  <p className="text-gray-500">No relationship data available</p>
                </div>
              </Card>
            )}
          </Card>

          {/* Relationship Summary Table */}
          <Card variant="glass" className="p-6">
            <h3 className="text-lg font-semibold bg-gradient-to-r from-emotion-positive to-emotion-positive-light bg-clip-text text-transparent mb-4">
              Relationship Summary
            </h3>
            {relationships && relationships.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="border-b border-gray-200/50">
                      <th className="text-left py-3 px-3 text-sm font-semibold text-gray-700">Name</th>
                      <th className="text-left py-3 px-3 text-sm font-semibold text-gray-700">Type</th>
                      <th className="text-left py-3 px-3 text-sm font-semibold text-gray-700">Health</th>
                      <th className="text-left py-3 px-3 text-sm font-semibold text-gray-700">Sentiment</th>
                    </tr>
                  </thead>
                  <tbody>
                    {relationships.map((relationship, index) => (
                      <tr key={index} className="border-b border-gray-100/50 hover:bg-gradient-to-r hover:from-primary/5 hover:to-secondary-lavender/5 transition-all duration-200">
                        <td className="py-3 px-3 text-sm font-medium text-gray-800">{relationship.name}</td>
                        <td className="py-3 px-3 text-sm text-gray-600 capitalize">{relationship.type}</td>
                        <td className="py-3 px-3 text-sm">
                          <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
                            relationship.health_score > 75 ? 'bg-gradient-to-r from-emotion-positive/20 to-emotion-positive-light/20 text-emotion-positive border border-emotion-positive/30' :
                            relationship.health_score > 50 ? 'bg-gradient-to-r from-accent-warm/20 to-accent-coral/20 text-accent-warm border border-accent-warm/30' :
                            'bg-gradient-to-r from-emotion-negative/20 to-accent-coral/20 text-emotion-negative border border-emotion-negative/30'
                          }`}>
                            {relationship.health_score}%
                          </span>
                        </td>
                        <td className="py-3 px-3 text-sm">
                          <span className={`px-3 py-1 rounded-full text-xs font-semibold capitalize ${
                            relationship.sentiment === 'positive' ? 'bg-gradient-to-r from-emotion-positive/20 to-emotion-positive-light/20 text-emotion-positive border border-emotion-positive/30' :
                            relationship.sentiment === 'neutral' ? 'bg-gradient-to-r from-secondary-sage/20 to-secondary-mint/20 text-secondary-sage border border-secondary-sage/30' :
                            'bg-gradient-to-r from-emotion-negative/20 to-accent-coral/20 text-emotion-negative border border-emotion-negative/30'
                          }`}>
                            {relationship.sentiment}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <Card variant="ghost" className="flex items-center justify-center h-32">
                <div className="text-center">
                  <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <p className="text-gray-500">No relationships tracked yet</p>
                </div>
              </Card>
            )}
          </Card>
        </div>
      )}

      {selectedReport === 'flags' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Flag Distribution Chart */}
          <Card variant="glass" className="p-6">
            <h3 className="text-lg font-semibold bg-gradient-to-r from-emotion-negative to-accent-coral bg-clip-text text-transparent mb-4">
              Red Flag Distribution
            </h3>
            {flagChartData.length > 0 ? (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={flagChartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="url(#flagsGradient)"
                      dataKey="count"
                      stroke="rgba(255, 255, 255, 0.2)"
                      strokeWidth={2}
                    >
                      {flagChartData.map((entry, index) => (
                        <Cell 
                          key={`cell-${index}`} 
                          fill={[
                            '#ef4444', // red-500
                            '#f97316', // orange-500  
                            '#eab308', // yellow-500
                            '#06b6d4', // cyan-500
                            '#8b5cf6', // violet-500
                            '#ec4899'  // pink-500
                          ][index % 6]} 
                        />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(239, 68, 68, 0.2)',
                        borderRadius: '12px',
                        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <defs>
                      <linearGradient id="flagsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#ef4444" />
                        <stop offset="100%" stopColor="#f97316" />
                      </linearGradient>
                    </defs>
                  </PieChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <Card variant="ghost" className="flex items-center justify-center h-80">
                <div className="text-center">
                  <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-gray-500">No red flags detected in the selected period</p>
                  <p className="text-sm text-gray-400 mt-1">Great communication health!</p>
                </div>
              </Card>
            )}
          </Card>

          {/* Flag Frequency Chart */}
          <Card variant="glass" className="p-6">
            <h3 className="text-lg font-semibold bg-gradient-to-r from-accent-warm to-accent-coral bg-clip-text text-transparent mb-4">
              Flag Frequency
            </h3>
            {flagChartData.length > 0 ? (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={flagChartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(239, 68, 68, 0.1)" />
                    <XAxis 
                      dataKey="name" 
                      angle={-45} 
                      textAnchor="end" 
                      height={80}
                      tick={{ fontSize: 12 }}
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(239, 68, 68, 0.2)',
                        borderRadius: '12px',
                        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Bar 
                      dataKey="count" 
                      fill="url(#flagFrequencyGradient)" 
                      radius={[4, 4, 0, 0]}
                    />
                    <defs>
                      <linearGradient id="flagFrequencyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" stopColor="#ef4444" />
                        <stop offset="100%" stopColor="#f97316" />
                      </linearGradient>
                    </defs>
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <Card variant="ghost" className="flex items-center justify-center h-80">
                <div className="text-center">
                  <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-gray-500">No red flags detected in the selected period</p>
                  <p className="text-sm text-gray-400 mt-1">Excellent communication patterns!</p>
                </div>
              </Card>
            )}
          </Card>
        </div>
      )}

      {/* Faith-Based Insights */}
      {faithModeEnabled && (
        <Card variant="glass" className="mt-8 p-6 bg-gradient-to-br from-secondary-lavender/10 to-secondary-violet/10 border border-secondary-lavender/20">
          <h3 className="text-lg font-semibold bg-gradient-to-r from-secondary-lavender to-secondary-violet bg-clip-text text-transparent mb-4 flex items-center gap-2">
            <svg className="w-5 h-5 text-secondary-lavender" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            Faith-Based Reflection
          </h3>
          <div className="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <p className="text-gray-700 leading-relaxed">
              {getFaithContent('report_reflection', { 
                healthScore: reportData?.health_score || 0,
                period: `${dateRange} days`
              }) || 
              "Consider how your communication patterns align with principles of love, kindness, and understanding. Growth in emotional intelligence is a journey of faith and patience."}
            </p>
          </div>
        </Card>
      )}
    </div>
  );
};

export default Reports;
