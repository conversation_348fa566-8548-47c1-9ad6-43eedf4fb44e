import React, { useState, useEffect } from 'react';
import { useAppContext } from '../contexts/AppContext';
import { useFaith } from '../contexts/FaithContext';
import Card from '../components/Card';
import Button from '../components/Button';
import axios from 'axios';

const AnomalyDetection = () => {
  const { relationships } = useAppContext();
  const { faithModeEnabled, getFaithContent } = useFaith();
  
  const [anomalies, setAnomalies] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedRelationship, setSelectedRelationship] = useState('');
  const [analysisParams, setAnalysisParams] = useState({
    days: 30,
    method: 'statistical'
  });
  const [lastAnalysis, setLastAnalysis] = useState(null);

  const fetchAnomalies = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const backendUrl = process.env.REACT_APP_BACKEND_URL || '';
      
      // Build query parameters
      const params = new URLSearchParams({
        days: analysisParams.days.toString(),
        method: analysisParams.method
      });
      
      if (selectedRelationship) {
        params.append('relationship_id', selectedRelationship);
      }
      
      const response = await axios.get(`${backendUrl}/api/detect-anomalies?${params}`);
      
      setAnomalies(response.data.anomalies || []);
      setLastAnalysis(response.data);
    } catch (err) {
      console.error('Failed to fetch anomalies:', err);
      setError('Failed to detect anomalies. Please try again later.');
      setAnomalies([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAnomalies();
  }, []);

  const handleAnalyze = () => {
    fetchAnomalies();
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high': return 'bg-gradient-to-r from-emotion-negative/20 to-accent-coral/20 text-emotion-negative border border-emotion-negative/30';
      case 'medium': return 'bg-gradient-to-r from-accent-warm/20 to-accent-coral/20 text-accent-warm border border-accent-warm/30';
      case 'low': return 'bg-gradient-to-r from-secondary-sage/20 to-secondary-mint/20 text-secondary-sage border border-secondary-sage/30';
      default: return 'bg-gradient-to-r from-gray-100 to-gray-50 text-gray-800 border border-gray-200';
    }
  };

  const getAnomalyIcon = (type) => {
    switch (type) {
      case 'sentiment_shift':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
          </svg>
        );
      case 'communication_frequency_anomaly':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        );
      case 'flag_escalation':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const formatAnomalyType = (type) => {
    return type
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const getRelationshipName = (relationshipId) => {
    const relationship = relationships?.find(r => r.id === relationshipId);
    return relationship ? relationship.name : 'Unknown Relationship';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary via-secondary-lavender to-secondary-violet bg-clip-text text-transparent mb-3">
          Anomaly Detection
        </h1>
        <p className="text-gray-600 text-lg">
          Detect unusual patterns in your communication and relationships using advanced analytics
        </p>
      </div>

      {/* Analysis Controls */}
      <Card variant="glass" className="p-6 mb-8">
        <h2 className="text-lg font-semibold bg-gradient-to-r from-primary to-secondary-lavender bg-clip-text text-transparent mb-4">
          Analysis Parameters
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Time Period
            </label>
            <select
              value={analysisParams.days}
              onChange={(e) => setAnalysisParams(prev => ({ ...prev, days: parseInt(e.target.value) }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all duration-200 bg-white/50 backdrop-blur-sm"
            >
              <option value={7}>Last 7 days</option>
              <option value={30}>Last 30 days</option>
              <option value={90}>Last 3 months</option>
              <option value={180}>Last 6 months</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Relationship (Optional)
            </label>
            <select
              value={selectedRelationship}
              onChange={(e) => setSelectedRelationship(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all duration-200 bg-white/50 backdrop-blur-sm"
            >
              <option value="">All Relationships</option>
              {relationships?.map(relationship => (
                <option key={relationship.id} value={relationship.id}>
                  {relationship.name} ({relationship.type})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Detection Method
            </label>
            <select
              value={analysisParams.method}
              onChange={(e) => setAnalysisParams(prev => ({ ...prev, method: e.target.value }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all duration-200 bg-white/50 backdrop-blur-sm"
            >
              <option value="statistical">Statistical Analysis</option>
              <option value="pattern">Pattern Recognition</option>
              <option value="hybrid">Hybrid Approach</option>
            </select>
          </div>
        </div>

        <Button
          onClick={handleAnalyze}
          disabled={isLoading}
          variant={isLoading ? "disabled" : "gradient"}
          size="lg"
          className="w-full md:w-auto"
        >
          {isLoading ? (
            <span className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white/30 border-t-white mr-3"></div>
              Analyzing...
            </span>
          ) : (
            <span className="flex items-center justify-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              Run Anomaly Detection
            </span>
          )}
        </Button>
      </Card>

      {/* Error Display */}
      {error && (
        <Card variant="negative" className="mb-8">
          <p className="text-white">{error}</p>
        </Card>
      )}

      {/* Analysis Summary */}
      {lastAnalysis && !isLoading && (
        <Card variant="glass" className="p-6 mb-8">
          <h2 className="text-lg font-semibold bg-gradient-to-r from-secondary-sage to-secondary-mint bg-clip-text text-transparent mb-4">
            Analysis Summary
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card variant="ghost" className="p-4 bg-gradient-to-br from-primary/5 to-secondary-lavender/5 border border-primary/20">
              <div className="flex items-center mb-2">
                <div className="w-8 h-8 bg-gradient-to-br from-primary/20 to-secondary-lavender/20 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <p className="text-sm font-medium text-gray-600">Total Anomalies</p>
              </div>
              <p className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary-lavender bg-clip-text text-transparent">
                {lastAnalysis.total_count}
              </p>
            </Card>
            
            <Card variant="ghost" className="p-4 bg-gradient-to-br from-secondary-sage/5 to-secondary-mint/5 border border-secondary-sage/20">
              <div className="flex items-center mb-2">
                <div className="w-8 h-8 bg-gradient-to-br from-secondary-sage/20 to-secondary-mint/20 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-secondary-sage" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p className="text-sm font-medium text-gray-600">Analysis Period</p>
              </div>
              <p className="text-2xl font-bold text-secondary-sage">{lastAnalysis.analysis_period_days}d</p>
            </Card>
            
            <Card variant="ghost" className="p-4 bg-gradient-to-br from-secondary-lavender/5 to-secondary-violet/5 border border-secondary-lavender/20">
              <div className="flex items-center mb-2">
                <div className="w-8 h-8 bg-gradient-to-br from-secondary-lavender/20 to-secondary-violet/20 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-secondary-lavender" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <p className="text-sm font-medium text-gray-600">Method Used</p>
              </div>
              <p className="text-lg font-bold text-secondary-lavender capitalize">{lastAnalysis.method}</p>
            </Card>
            
            <Card variant="ghost" className="p-4 bg-gradient-to-br from-accent-warm/5 to-accent-coral/5 border border-accent-warm/20">
              <div className="flex items-center mb-2">
                <div className="w-8 h-8 bg-gradient-to-br from-accent-warm/20 to-accent-coral/20 rounded-lg flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-accent-warm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <p className="text-sm font-medium text-gray-600">Generated At</p>
              </div>
              <p className="text-sm font-bold text-accent-warm">
                {new Date(lastAnalysis.generated_at).toLocaleString()}
              </p>
            </Card>
          </div>
        </Card>
      )}

      {/* Anomalies List */}
      <Card variant="glass" className="p-6">
        <h2 className="text-lg font-semibold bg-gradient-to-r from-emotion-negative to-accent-coral bg-clip-text text-transparent mb-6">
          Detected Anomalies
        </h2>
        
        {!isLoading && anomalies.length === 0 ? (
          <Card variant="ghost" className="text-center py-12 bg-gradient-to-br from-emotion-positive/5 to-emotion-positive-light/5 border border-emotion-positive/20">
            <div className="w-16 h-16 bg-gradient-to-br from-emotion-positive/20 to-emotion-positive-light/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-emotion-positive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold bg-gradient-to-r from-emotion-positive to-emotion-positive-light bg-clip-text text-transparent mb-2">
              No Anomalies Detected
            </h3>
            <p className="text-gray-600">
              Your communication patterns appear normal for the selected time period. 
              This is a positive sign of healthy relationship dynamics.
            </p>
          </Card>
        ) : (
          <div className="space-y-4">
            {anomalies.map((anomaly, index) => (
              <Card key={index} variant="ghost" hover="lift" className={`p-4 ${getSeverityColor(anomaly.severity)}`}>
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-4 mt-1">
                    <div className="w-8 h-8 bg-white/50 backdrop-blur-sm rounded-lg flex items-center justify-center">
                      {getAnomalyIcon(anomaly.type)}
                    </div>
                  </div>
                  
                  <div className="flex-grow">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-lg">
                        {formatAnomalyType(anomaly.type)}
                      </h3>
                      <span className={`px-3 py-1 text-xs font-semibold rounded-full ${getSeverityColor(anomaly.severity)}`}>
                        {anomaly.severity.toUpperCase()}
                      </span>
                    </div>
                    
                    <p className="text-sm mb-4 text-gray-700">
                      {anomaly.description}
                    </p>
                    
                    {/* Additional Details */}
                    <div className="bg-white/30 backdrop-blur-sm rounded-lg p-3 space-y-2 text-xs">
                      {anomaly.relationship_id && (
                        <div className="flex items-center">
                          <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                          </svg>
                          <span className="font-medium">Relationship:</span> 
                          <span className="ml-1 text-gray-700">{getRelationshipName(anomaly.relationship_id)}</span>
                        </div>
                      )}
                      
                      {anomaly.z_score && (
                        <div className="flex items-center">
                          <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                          </svg>
                          <span className="font-medium">Statistical Score:</span> 
                          <span className="ml-1 font-mono text-gray-700">{anomaly.z_score}</span>
                        </div>
                      )}
                      
                      {anomaly.from_sentiment && anomaly.to_sentiment && (
                        <div className="flex items-center">
                          <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                          </svg>
                          <span className="font-medium">Sentiment Change:</span> 
                          <span className="ml-1">
                            <span className="capitalize">{anomaly.from_sentiment}</span> → <span className="capitalize">{anomaly.to_sentiment}</span>
                          </span>
                        </div>
                      )}
                      
                      {anomaly.flag_count_increase && (
                        <div className="flex items-center">
                          <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          <span className="font-medium">Flag Increase:</span> 
                          <span className="ml-1 text-emotion-negative font-semibold">+{anomaly.flag_count_increase} flags</span>
                        </div>
                      )}
                      
                      {anomaly.recent_flags && (
                        <div className="flex items-start">
                          <svg className="w-4 h-4 mr-2 mt-0.5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                          </svg>
                          <div>
                            <span className="font-medium">Recent Flags:</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {anomaly.recent_flags.map((flag, flagIndex) => (
                                <span key={flagIndex} className="px-2 py-1 bg-emotion-negative/10 text-emotion-negative text-xs rounded-full">
                                  {flag}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                      
                      <div className="flex items-center pt-2 border-t border-white/20">
                        <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span className="font-medium">Detected:</span> 
                        <span className="ml-1 text-gray-700">{new Date(anomaly.detected_at).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </Card>

      {/* Interpretation Guide */}
      <Card variant="glass" className="mt-8 p-6 bg-gradient-to-br from-background-subtle/50 to-white/50">
        <h3 className="text-lg font-semibold bg-gradient-to-r from-primary to-secondary-lavender bg-clip-text text-transparent mb-4">
          Understanding Anomalies
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card variant="ghost" className="p-4 bg-gradient-to-br from-primary/5 to-secondary-lavender/5 border border-primary/20">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-gradient-to-br from-primary/20 to-secondary-lavender/20 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-800">Sentiment Shifts</h4>
            </div>
            <p className="text-sm text-gray-600">
              Sudden changes in emotional tone that may indicate relationship stress or significant events.
            </p>
          </Card>
          
          <Card variant="ghost" className="p-4 bg-gradient-to-br from-secondary-sage/5 to-secondary-mint/5 border border-secondary-sage/20">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-gradient-to-br from-secondary-sage/20 to-secondary-mint/20 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-secondary-sage" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-800">Communication Frequency</h4>
            </div>
            <p className="text-sm text-gray-600">
              Unusual increases or decreases in message volume that deviate from normal patterns.
            </p>
          </Card>
          
          <Card variant="ghost" className="p-4 bg-gradient-to-br from-emotion-negative/5 to-accent-coral/5 border border-emotion-negative/20">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-gradient-to-br from-emotion-negative/20 to-accent-coral/20 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-4 h-4 text-emotion-negative" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h4 className="font-semibold text-gray-800">Flag Escalations</h4>
            </div>
            <p className="text-sm text-gray-600">
              Increasing patterns of concerning communication flags that suggest relationship deterioration.
            </p>
          </Card>
        </div>
      </Card>

      {/* Faith-Based Insight */}
      {faithModeEnabled && (
        <Card variant="glass" className="mt-8 p-6 bg-gradient-to-br from-secondary-lavender/10 to-secondary-violet/10 border border-secondary-lavender/20">
          <h3 className="text-lg font-semibold bg-gradient-to-r from-secondary-lavender to-secondary-violet bg-clip-text text-transparent mb-4 flex items-center gap-2">
            <svg className="w-5 h-5 text-secondary-lavender" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            Faith-Based Perspective
          </h3>
          <div className="bg-white/50 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <p className="text-gray-700 leading-relaxed">
              {getFaithContent('anomaly_reflection', { 
                anomalyCount: anomalies.length,
                hasAnomalies: anomalies.length > 0
              }) || 
              (anomalies.length > 0 
                ? "Even in times of relational difficulty, remember that growth often comes through challenges. Approach these patterns with wisdom, grace, and a heart open to understanding."
                : "The absence of concerning patterns is a blessing. Continue to nurture your relationships with love, patience, and good communication."
              )}
            </p>
          </div>
        </Card>
      )}
    </div>
  );
};

export default AnomalyDetection;
