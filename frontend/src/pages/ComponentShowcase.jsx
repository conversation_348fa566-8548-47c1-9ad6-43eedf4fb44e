import React, { useState } from 'react';
import {
  Button,
  Card,
  Input,
  Select,
  Checkbox,
  Textarea,
  Modal,
  Toggle,
  NavBar
} from '../components/modern';
import IconShowcase from '../components/IconShowcase';

const ComponentShowcase = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    country: '',
    agree: false,
    message: '',
  });
  const [toggleStates, setToggleStates] = useState({
    notifications: false,
    darkMode: false,
    autoSave: true,
  });

  const navItems = [
    { label: 'Home', href: '#home', active: true },
    { label: 'Components', href: '#components' },
    { label: 'Documentation', href: '#docs' },
    { label: 'Contact', href: '#contact' },
  ];

  const countryOptions = [
    { value: '', label: 'Select a country' },
    { value: 'us', label: 'United States' },
    { value: 'uk', label: 'United Kingdom' },
    { value: 'ca', label: 'Canada' },
    { value: 'au', label: 'Australia' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <NavBar
        brand="Component Library"
        items={navItems}
        variant="glass"
        position="sticky"
        rightContent={
          <div className="flex items-center gap-2">
            <Toggle
              checked={toggleStates.darkMode}
              onChange={(checked) => setToggleStates({ ...toggleStates, darkMode: checked })}
              size="small"
              variant="gradient"
            />
            <Button variant="primary" size="small">Get Started</Button>
          </div>
        }
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">Modern Component Showcase</h1>
        
        {/* Buttons Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Buttons</h2>
          <Card variant="elevated" padding="large">
            <div className="space-y-6">
              {/* Button Variants */}
              <div>
                <h3 className="text-lg font-medium text-gray-700 mb-3">Variants</h3>
                <div className="flex flex-wrap gap-3">
                  <Button variant="primary">Primary</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="danger">Danger</Button>
                  <Button variant="glass">Glass</Button>
                </div>
              </div>

              {/* Button Sizes */}
              <div>
                <h3 className="text-lg font-medium text-gray-700 mb-3">Sizes</h3>
                <div className="flex flex-wrap items-center gap-3">
                  <Button size="small">Small</Button>
                  <Button size="medium">Medium</Button>
                  <Button size="large">Large</Button>
                </div>
              </div>

              {/* Button States */}
              <div>
                <h3 className="text-lg font-medium text-gray-700 mb-3">States</h3>
                <div className="flex flex-wrap gap-3">
                  <Button>Normal</Button>
                  <Button disabled>Disabled</Button>
                  <Button loading>Loading</Button>
                  <Button icon={<span>🚀</span>}>With Icon</Button>
                </div>
              </div>
            </div>
          </Card>
        </section>

        {/* Cards Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Cards</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card variant="default" hover>
              <h3 className="text-lg font-semibold mb-2">Default Card</h3>
              <p className="text-gray-600">This is a default card with hover effect.</p>
            </Card>
            <Card variant="elevated">
              <h3 className="text-lg font-semibold mb-2">Elevated Card</h3>
              <p className="text-gray-600">This card has more prominent shadows.</p>
            </Card>
            <Card variant="glass">
              <h3 className="text-lg font-semibold mb-2">Glass Card</h3>
              <p className="text-gray-600">A glassmorphism effect card.</p>
            </Card>
          </div>
        </section>

        {/* Form Elements Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Form Elements</h2>
          <Card variant="elevated" padding="large">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-gray-700 mb-4">Input Variants</h3>
                <Input
                  id="email"
                  type="email"
                  label="Email Address"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  variant="default"
                  required
                />
                <Input
                  id="email-float"
                  type="email"
                  label="Floating Label"
                  variant="floating"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                />
                <Input
                  id="email-glass"
                  type="email"
                  label="Glass Input"
                  placeholder="Glass effect"
                  variant="glass"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                />
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-700 mb-4">Select & Checkbox</h3>
                <Select
                  id="country"
                  label="Country"
                  options={countryOptions}
                  value={formData.country}
                  onChange={(e) => setFormData({ ...formData, country: e.target.value })}
                  variant="modern"
                />
                <Checkbox
                  id="agree"
                  label="I agree to the terms and conditions"
                  checked={formData.agree}
                  onChange={(e) => setFormData({ ...formData, agree: e.target.checked })}
                  variant="modern"
                />
              </div>

              <div className="md:col-span-2">
                <h3 className="text-lg font-medium text-gray-700 mb-4">Textarea</h3>
                <Textarea
                  id="message"
                  label="Message"
                  placeholder="Enter your message here..."
                  value={formData.message}
                  onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                  rows={5}
                />
              </div>
            </div>
          </Card>
        </section>

        {/* Toggles Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Toggles</h2>
          <Card variant="elevated" padding="large">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-700 mb-3">Toggle Variants</h3>
                <div className="space-y-3">
                  <Toggle
                    checked={toggleStates.notifications}
                    onChange={(checked) => setToggleStates({ ...toggleStates, notifications: checked })}
                    label="Enable Notifications"
                    variant="default"
                  />
                  <Toggle
                    checked={toggleStates.darkMode}
                    onChange={(checked) => setToggleStates({ ...toggleStates, darkMode: checked })}
                    label="Dark Mode"
                    variant="gradient"
                  />
                  <Toggle
                    checked={toggleStates.autoSave}
                    onChange={(checked) => setToggleStates({ ...toggleStates, autoSave: checked })}
                    label="Auto Save"
                    variant="glass"
                  />
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-700 mb-3">Toggle Sizes</h3>
                <div className="flex items-center gap-6">
                  <Toggle
                    checked={true}
                    onChange={() => {}}
                    size="small"
                    label="Small"
                  />
                  <Toggle
                    checked={true}
                    onChange={() => {}}
                    size="medium"
                    label="Medium"
                  />
                  <Toggle
                    checked={true}
                    onChange={() => {}}
                    size="large"
                    label="Large"
                  />
                </div>
              </div>
            </div>
          </Card>
        </section>

        {/* Icons Section */}
        <section className="mb-12">
          <IconShowcase />
        </section>

        {/* Modal Section */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">Modal</h2>
          <Card variant="elevated" padding="large">
            <div className="flex gap-3">
              <Button onClick={() => setModalOpen(true)} variant="primary">
                Open Modal
              </Button>
              <Button onClick={() => setModalOpen(true)} variant="glass">
                Open Glass Modal
              </Button>
            </div>
          </Card>
        </section>

        {/* Modal Component */}
        <Modal
          isOpen={modalOpen}
          onClose={() => setModalOpen(false)}
          title="Example Modal"
          variant="glass"
          footer={
            <div className="flex justify-end gap-3">
              <Button variant="ghost" onClick={() => setModalOpen(false)}>
                Cancel
              </Button>
              <Button variant="primary" onClick={() => setModalOpen(false)}>
                Confirm
              </Button>
            </div>
          }
        >
          <p className="text-gray-600">
            This is an example modal with glass morphism effect. It includes a header,
            content area, and footer with action buttons.
          </p>
          <p className="text-gray-600 mt-4">
            The modal locks body scroll when open and can be closed by clicking the
            backdrop or the close button.
          </p>
        </Modal>
      </div>
    </div>
  );
};

export default ComponentShowcase;
