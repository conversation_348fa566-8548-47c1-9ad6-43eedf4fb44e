import React, { useState } from 'react';
import { useAppContext } from '../contexts/AppContext';
import { useFaith, FAITH_OPTIONS } from '../contexts/FaithContext';
import Card from '../components/Card';
import Button from '../components/Button';
import { Textarea } from '../components/FormElements';

const GrowthCenter = () => {
  const { growthPlan } = useAppContext();
  const { 
    faithModeEnabled, 
    selectedFaith, 
    toggleFaithMode, 
    changeFaith, 
    getFaithContent 
  } = useFaith();

  const [activeDay, setActiveDay] = useState(1);
  const [journalEntry, setJournalEntry] = useState('');
  const [journalSubmitted, setJournalSubmitted] = useState(false);

  const handleJournalSubmit = (e) => {
    e.preventDefault();
    if (!journalEntry.trim()) return;

    // In a real app, we would send this to the API
    console.log('Journal submitted:', journalEntry);
    setJournalSubmitted(true);
    // Don't clear the entry to allow the user to see what they submitted
  };

  if (!growthPlan) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header Section */}
      <div className="mb-8 text-center">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent mb-4">
          Growth Center
        </h1>
        <p className="text-text-secondary text-lg max-w-2xl mx-auto">
          Where emotional awareness becomes actionable growth through personalized practices and insights
        </p>
      </div>

      {/* Faith Selection (visible only when Faith Mode is on) */}
      {faithModeEnabled && (
        <Card variant="glass" hover="lift" className="mb-8 bg-primary/5 border-primary/20">
          <h2 className="text-lg font-semibold text-primary mb-4 flex items-center gap-2">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
            Faith Framework
          </h2>
          <p className="text-text-primary mb-6">
            Select your preferred spiritual or faith framework to receive content aligned with your values and beliefs.
          </p>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            {FAITH_OPTIONS.map(option => (
              <Button
                key={option.id}
                onClick={() => changeFaith(option.id)}
                variant={selectedFaith === option.id ? 'gradient' : 'glass'}
                size="sm"
                className="transition-all duration-300"
              >
                {option.name}
              </Button>
            ))}
          </div>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Weekly Plan */}
        <div className="lg:col-span-2">
          <Card variant="glass" hover="lift" className="animate-float">
            <h2 className="text-xl font-semibold text-text-primary mb-6 flex items-center gap-2">
              <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              This Week's Journey: {growthPlan.current_week.theme}
            </h2>

            {/* Day tabs */}
            <div className="flex overflow-x-auto space-x-2 mb-6 p-1">
              {growthPlan.current_week.days.map(day => (
                <Button
                  key={day.day}
                  onClick={() => setActiveDay(day.day)}
                  variant={activeDay === day.day ? 'gradient' : 'glass'}
                  size="sm"
                  className="whitespace-nowrap transition-all duration-300"
                >
                  Day {day.day}
                </Button>
              ))}
            </div>

            {/* Active day content */}
            {growthPlan.current_week.days.map(day => (
              <div key={day.day} className={`transition-all duration-500 ${activeDay === day.day ? 'block animate-float' : 'hidden'}`}>
                <Card variant="glass" hover="lift" className="mb-6 bg-secondary/5 border-secondary/20">
                  <h3 className="text-lg font-semibold text-text-primary mb-2">{day.title}</h3>
                  <div className="inline-block px-3 py-1 bg-gradient-to-r from-accent to-accent/80 text-white text-sm rounded-full mb-4 shadow-soft">
                    {day.activity_type}
                  </div>
                  <p className="text-text-primary leading-relaxed">{day.content}</p>

                  {/* Faith-specific content (shown only in Faith Mode) */}
                  {faithModeEnabled && selectedFaith && (
                    <Card variant="glass" hover="lift" className="mt-6 bg-primary/5 border-primary/20">
                      <h4 className="text-md font-medium text-primary mb-2 flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        {selectedFaith === 'christianity' ? 'Scripture Reflection' : 
                         selectedFaith === 'buddhism' ? 'Dharma Teaching' :
                         selectedFaith === 'islam' ? 'Quranic Wisdom' :
                         selectedFaith === 'judaism' ? 'Torah Insight' :
                         selectedFaith === 'hinduism' ? 'Vedic Guidance' :
                         'Spiritual Perspective'}
                      </h4>
                      <p className="text-text-primary">
                        {getFaithContent('scripture', { theme: growthPlan.current_week.theme }) || 
                         "Reflect on how this practice aligns with your spiritual values and beliefs."}
                      </p>
                    </Card>
                  )}
                </Card>

                {/* Journaling area */}
                <Card variant="glass" hover="lift">
                  <h3 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
                    <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                    Reflection Journal
                  </h3>
                  <form onSubmit={handleJournalSubmit} className="space-y-4">
                    <Textarea
                      rows={6}
                      placeholder="Reflect on today's practice..."
                      value={journalEntry}
                      onChange={(e) => setJournalEntry(e.target.value)}
                      variant="glass"
                    />
                    <Button
                      type="submit"
                      disabled={!journalEntry.trim() || journalSubmitted}
                      variant={journalSubmitted ? 'success' : 'gradient'}
                      loading={false}
                      className="transition-all duration-300"
                    >
                      {journalSubmitted ? 'Submitted ✓' : 'Save Reflection'}
                    </Button>
                  </form>
                </Card>
              </div>
            ))}
          </Card>
        </div>

        {/* Side panel with goals and practices */}
        <div className="lg:col-span-1 space-y-6">
          <Card variant="glass" hover="lift" className="animate-float" style={{animationDelay: '0.2s'}}>
            <h2 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
              <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
              </svg>
              Your Growth Goals
            </h2>
            <div className="space-y-3">
              {growthPlan.goals.map((goal, index) => (
                <div key={index} className="flex items-start group transition-all duration-300 hover:scale-105">
                  <div className="flex-none mr-3">
                    <div className="w-5 h-5 border-2 border-primary rounded-full flex items-center justify-center group-hover:border-secondary transition-colors duration-300">
                      <div className="w-2 h-2 bg-primary rounded-full group-hover:bg-secondary transition-colors duration-300"></div>
                    </div>
                  </div>
                  <p className="text-text-primary group-hover:text-primary transition-colors duration-300">{goal}</p>
                </div>
              ))}
            </div>
          </Card>

          <Card variant="glass" hover="lift" className="animate-float" style={{animationDelay: '0.4s'}}>
            <h2 className="text-lg font-semibold text-text-primary mb-4 flex items-center gap-2">
              <svg className="w-5 h-5 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707" />
              </svg>
              Daily Practice
            </h2>
            <Card variant="glass" hover="lift" className="bg-emotion-positive/10 border-emotion-positive/30">
              <h3 className="font-medium text-emotion-positive mb-2 flex items-center gap-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Today's Micro-Practice
              </h3>
              <p className="text-text-primary mb-4 leading-relaxed">
                {faithModeEnabled && getFaithContent('practice') ? (
                  getFaithContent('practice')
                ) : (
                  "Take three deep breaths whenever you notice self-criticism today. With each exhale, mentally say \"I acknowledge this feeling without judgment.\""
                )}
              </p>
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Set a reminder</span>
                <Button variant="ghost" size="sm" className="text-emotion-positive border-emotion-positive/30 hover:bg-emotion-positive/10">
                  Remind Me
                </Button>
              </div>
            </Card>

            <div className="mt-6">
              <h3 className="font-medium text-text-primary mb-3 flex items-center gap-2">
                <svg className="w-4 h-4 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                Emotional Reframe
              </h3>
              <div className="space-y-3">
                <Card variant="glass" hover="lift" className="bg-secondary/10 border-secondary/30">
                  <p className="text-sm text-text-primary">
                    {faithModeEnabled && getFaithContent('reframe') ? (
                      getFaithContent('reframe')
                    ) : (
                      "Your feelings don't need to be justified to be real. You can acknowledge them without judgment."
                    )}
                  </p>
                </Card>
                <Card variant="glass" hover="lift" className="bg-secondary/10 border-secondary/30">
                  <p className="text-sm text-text-primary">
                    "When you notice emotional reactions, treat them as messengers rather than problems to solve."
                  </p>
                </Card>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default GrowthCenter;
