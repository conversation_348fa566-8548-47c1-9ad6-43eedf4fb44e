import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useAppContext } from '../contexts/AppContext';
import { useNavigation } from '../contexts/NavigationContext';
import Card from '../components/Card';
import Button from '../components/Button';
import Icon from '../components/modern/Icon';
import { DashboardHeader } from '../components/PageHeader';

// Dashboard components
import DashboardWidget from '../components/dashboard/DashboardWidget';
import AnimatedCounter from '../components/dashboard/AnimatedCounter';
import ProgressRing from '../components/dashboard/ProgressRing';
import FAB from '../components/dashboard/FAB';
import { 
  NoDataIllustration, 
  NoRelationshipsIllustration, 
  NoMessagesIllustration,
  ErrorIllustration 
} from '../components/dashboard/EmptyStateIllustrations';

// Chart components (using recharts)
import { 
  Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell
} from 'recharts';

// Import the new chart theme
import { 
  useChartTheme, 
  CustomTooltip, 
  getChartConfig, 
  getAxisStyle, 
  getGridStyle, 
  getColorScheme,
  chartGradients,
  renderGradient 
} from '../utils/ChartTheme';

const Dashboard = () => {
  const { 
    dashboardData, 
    isLoadingDashboard, 
    dashboardError,
    relationships,
    analysisHistory,
    connectionStatus,
    checkBackendConnection
  } = useAppContext();

  // Use the chart theme hook
  const { colors, isDarkMode, prefersReducedMotion } = useChartTheme();

  // State for real-time updates
  const [realTimeData, setRealTimeData] = useState({
    activeUsers: 0,
    messagesProcessed: 0,
    flagsDetected: 0
  });

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeData(prev => ({
        activeUsers: Math.max(0, prev.activeUsers + Math.floor(Math.random() * 3 - 1)),
        messagesProcessed: prev.messagesProcessed + Math.floor(Math.random() * 2),
        flagsDetected: prev.flagsDetected + (Math.random() > 0.8 ? 1 : 0)
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Ensure analysisHistory is an array
  const safeAnalysisHistory = Array.isArray(analysisHistory) ? analysisHistory : [];

  // Handle connection status with modern styling
  if (connectionStatus === 'checking') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background-subtle to-white flex items-center justify-center">
        <Card variant="glass" className="text-center p-12">
          <div className="w-16 h-16 mx-auto mb-6">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/30 border-t-primary"></div>
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Connecting to Your ÆI</h2>
          <p className="text-gray-600">Establishing connection to your emotional intelligence backend...</p>
        </Card>
      </div>
    );
  }

  if (connectionStatus === 'error') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background-subtle to-white flex items-center justify-center">
        <Card variant="floating" className="text-center p-12 max-w-md">
          <div className="w-16 h-16 mx-auto mb-6 bg-emotion-negative/10 rounded-full flex items-center justify-center">
            <Icon name="AlertTriangle" size="lg" className="text-emotion-negative" />
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Connection Error</h2>
          <p className="text-gray-600 mb-6">Unable to connect to the backend server. Please make sure the server is running and accessible.</p>
          <Button
            variant="negative"
            onClick={checkBackendConnection}
            className="w-full"
          >
            Retry Connection
          </Button>
        </Card>
      </div>
    );
  }

  if (isLoadingDashboard) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background-subtle to-white flex items-center justify-center">
        <Card variant="glass" className="text-center p-12">
          <div className="w-16 h-16 mx-auto mb-6">
            <div className="animate-pulse-slow rounded-full h-16 w-16 bg-gradient-to-r from-primary to-secondary-lavender"></div>
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Loading Dashboard</h2>
          <p className="text-gray-600">Gathering your emotional intelligence insights...</p>
        </Card>
      </div>
    );
  }

  if (dashboardError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card variant="negative" className="p-6">
          <p className="text-white">{dashboardError}</p>
        </Card>
      </div>
    );
  }

  // Prepare chart data for flag counts
  const flagChartData = dashboardData?.flag_counts 
    ? Object.entries(dashboardData.flag_counts).map(([type, count]) => ({
        name: type.replace('_', ' '),
        count
      }))
    : [];

  // Prepare data for sentiment timeline
  const sentimentData = dashboardData?.sentiment_timeline 
    ? dashboardData.sentiment_timeline.map(([date, sentiment]) => ({
        date: new Date(date).toLocaleDateString(),
        value: sentiment === 'positive' ? 1 : sentiment === 'neutral' ? 0 : -1,
        sentiment
      }))
    : [];

  // Get color schemes for charts
  const barChartColors = getColorScheme('categorical', 6, isDarkMode);
  const pieChartColors = getColorScheme('categorical', relationships?.length || 6, isDarkMode);
  const barChartConfig = getChartConfig('bar', isDarkMode, prefersReducedMotion);
  const lineChartConfig = getChartConfig('line', isDarkMode, prefersReducedMotion);
  const pieChartConfig = getChartConfig('pie', isDarkMode, prefersReducedMotion);
  const axisStyle = getAxisStyle(isDarkMode);
  const gridStyle = getGridStyle(isDarkMode);

  return (
    <>
      <DashboardHeader
        title="Your Emotional Command Center"
        description="Track your emotional intelligence progress and get personalized insights"
      />
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">

        {/* Real-time Stats Bar */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
          <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-4 flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Now</p>
              <div className="text-2xl font-bold text-primary">
                <AnimatedCounter value={realTimeData.activeUsers} />
              </div>
            </div>
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
          </div>
          <div className="bg-gradient-to-r from-secondary-lavender/10 to-secondary-lavender/5 rounded-xl p-4">
            <p className="text-sm text-gray-600">Messages Today</p>
            <div className="text-2xl font-bold text-secondary-lavender">
              <AnimatedCounter value={realTimeData.messagesProcessed} />
            </div>
          </div>
          <div className="bg-gradient-to-r from-emotion-negative/10 to-emotion-negative/5 rounded-xl p-4">
            <p className="text-sm text-gray-600">Flags Detected</p>
            <div className="text-2xl font-bold text-emotion-negative">
              <AnimatedCounter value={realTimeData.flagsDetected} />
            </div>
          </div>
        </div>

        {/* Top row - Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {/* Health Score */}
          <DashboardWidget
            title="Health Score"
            subtitle="Overall wellness"
            icon="Heart"
            iconColor="positive"
            variant="gradient"
            tooltip="Your health score is calculated based on communication consistency, emotional clarity, and conflict repair patterns across all your relationships."
          >
            <div className="flex flex-col items-center">
              <ProgressRing 
                value={dashboardData?.health_score || 0} 
                size={120}
                color={dashboardData?.health_score > 75 ? 'positive' : dashboardData?.health_score > 50 ? 'warning' : 'negative'}
                label="Overall"
              />
            </div>
          </DashboardWidget>

          {/* Communication Stats */}
          <DashboardWidget
            title="Messages"
            subtitle="Analyzed this month"
            icon="MessageSquare"
            iconColor="primary"
            variant="glass"
            className="md:col-span-2"
            tooltip="Track the number of messages you've analyzed for emotional insights and communication patterns."
          >
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  <AnimatedCounter value={dashboardData?.total_analyses || 0} />
                </div>
                <p className="text-xs text-gray-500 mt-1">Total</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-emotion-negative">
                  <AnimatedCounter value={dashboardData?.total_flags_detected || 0} />
                </div>
                <p className="text-xs text-gray-500 mt-1">Flags</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-secondary-violet">
                  <AnimatedCounter value={relationships?.length || 0} />
                </div>
                <p className="text-xs text-gray-500 mt-1">Relations</p>
              </div>
            </div>
          </DashboardWidget>

          {/* Live Alerts */}
          <DashboardWidget
            title="Live Alerts"
            subtitle="Recent activity"
            icon="Bell"
            iconColor="warning"
            variant="floating"
            tooltip="See your most recent message analyses and relationship updates in real-time."
            isEmpty={!safeAnalysisHistory || safeAnalysisHistory.length === 0}
            emptyState={
              <>
                <NoMessagesIllustration className="mb-4" />
                <p className="text-gray-500 mb-4">No recent activity to display</p>
                <Button variant="soft" size="sm" asChild>
                  <Link to="/message-analyzer">Analyze your first message</Link>
                </Button>
              </>
            }
          >
            {safeAnalysisHistory && safeAnalysisHistory.length > 0 && (
              <div className="space-y-2">
                {safeAnalysisHistory.slice(0, 3).map((analysis, index) => (
                  <div key={index} className={`p-3 rounded-lg transition-all duration-200 ${
                    analysis.flags && analysis.flags.length > 0 
                      ? 'bg-emotion-negative/5 border border-emotion-negative/10 hover:bg-emotion-negative/10' 
                      : 'bg-emotion-positive/5 border border-emotion-positive/10 hover:bg-emotion-positive/10'
                  }`}>
                    <div className="flex items-start">
                      <div className={`w-3 h-3 mt-1 rounded-full mr-3 animate-pulse ${
                        analysis.flags && analysis.flags.length > 0 ? 'bg-emotion-negative' : 'bg-emotion-positive'
                      }`}></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-800">
                          {analysis.flags && analysis.flags.length > 0 
                            ? `${analysis.flags.length} flag${analysis.flags.length > 1 ? 's' : ''} detected` 
                            : 'Healthy communication'}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(analysis.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                <Link to="/message-analyzer" className="text-sm text-primary hover:text-secondary-lavender font-medium block text-center mt-4 transition-colors">
                  View all analyses →
                </Link>
              </div>
            )}
          </DashboardWidget>
        </div>

        {/* Middle row - Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Red Flag Incidence Graph */}
          <DashboardWidget
            title="Red Flag Patterns"
            subtitle="Communication concerns"
            icon="AlertTriangle"
            iconColor="negative"
            variant="glass"
            tooltip="Track patterns of concerning communication behaviors across your messages to identify areas for improvement."
            isEmpty={flagChartData.length === 0}
            emptyState={
              <>
                <div className="w-16 h-16 mx-auto mb-4 bg-emotion-positive/10 rounded-full flex items-center justify-center">
                  <Icon name="CheckCircle" size="lg" className="text-emotion-positive" />
                </div>
                <p className="text-gray-500 mb-4">No red flags detected yet</p>
                <Button variant="soft" size="sm" asChild>
                  <Link to="/message-analyzer">Analyze a message</Link>
                </Button>
              </>
            }
          >
            {flagChartData.length > 0 && (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={flagChartData}
                    {...barChartConfig}
                  >
                    <defs>
                      {renderGradient(chartGradients.primary('barGradient'))}
                    </defs>
                    <CartesianGrid {...gridStyle} />
                    <XAxis dataKey="name" {...axisStyle} />
                    <YAxis {...axisStyle} />
                    <Tooltip 
                      content={<CustomTooltip isDarkMode={isDarkMode} />}
                    />
                    <Bar 
                      dataKey="count" 
                      fill="url(#barGradient)"
                      radius={[8, 8, 0, 0]}
                      onMouseEnter={(data, index, e) => {
                        if (!prefersReducedMotion && e?.target) {
                          e.target.style.opacity = '0.8';
                          e.target.style.transform = 'scale(1.02)';
                        }
                      }}
                      onMouseLeave={(data, index, e) => {
                        if (!prefersReducedMotion && e?.target) {
                          e.target.style.opacity = '1';
                          e.target.style.transform = 'scale(1)';
                        }
                      }}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            )}
          </DashboardWidget>

          {/* Sentiment Timeline */}
          <DashboardWidget
            title="Communication Trendline"
            subtitle="Emotional patterns"
            icon="TrendingUp"
            iconColor="secondary"
            variant="glass"
            tooltip="Monitor the emotional trajectory of your communications over time to identify patterns and trends."
            isEmpty={sentimentData.length === 0}
            emptyState={
              <>
                <NoDataIllustration className="mb-4" />
                <p className="text-gray-500 mb-4">Not enough data to display trends</p>
                <Button variant="soft" size="sm" asChild>
                  <Link to="/message-analyzer">Analyze more messages</Link>
                </Button>
              </>
            }
          >
            {sentimentData.length > 0 && (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={sentimentData}
                    {...lineChartConfig}
                  >
                    <defs>
                      {renderGradient(chartGradients.emotional('lineGradient'))}
                    </defs>
                    <CartesianGrid {...gridStyle} />
                    <XAxis dataKey="date" {...axisStyle} />
                    <YAxis 
                      {...axisStyle}
                      ticks={[-1, 0, 1]} 
                      tickFormatter={(value) => {
                        return value === 1 ? 'Positive' : value === 0 ? 'Neutral' : 'Negative';
                      }}
                    />
                    <Tooltip 
                      content={<CustomTooltip isDarkMode={isDarkMode} />}
                      formatter={(value) => {
                        return value === 1 ? 'Positive' : value === 0 ? 'Neutral' : 'Negative';
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="value" 
                      stroke="url(#lineGradient)"
                      strokeWidth={3}
                      dot={{ 
                        fill: colors.background, 
                        stroke: colors.primary[0], 
                        strokeWidth: 2, 
                        r: 4 
                      }}
                      activeDot={{ 
                        r: 6, 
                        fill: colors.primary[0],
                        stroke: colors.background,
                        strokeWidth: 2,
                        style: {
                          filter: isDarkMode 
                            ? 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.4))' 
                            : 'drop-shadow(0 0 8px rgba(0, 0, 0, 0.2))',
                        }
                      }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            )}
          </DashboardWidget>
        </div>

        {/* Bottom row - Insights */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Relationship Sentiment Map */}
          <DashboardWidget
            title="Relationship Map"
            subtitle="Connection health"
            icon="Users"
            iconColor="secondary"
            variant="gradient"
            tooltip="Visualize the health status of all your tracked relationships at a glance."
            isEmpty={!relationships || relationships.length === 0}
            emptyState={
              <>
                <NoRelationshipsIllustration className="mb-4" />
                <p className="text-gray-500 mb-4">No relationships tracked yet</p>
                <Button variant="soft" size="sm" asChild>
                  <Link to="/relationships">Add your first relationship</Link>
                </Button>
              </>
            }
          >
            {relationships && relationships.length > 0 && (
              <div>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={relationships.map(r => ({
                          name: r.name,
                          value: r.health_score
                        }))}
                        {...pieChartConfig}
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        dataKey="value"
                        nameKey="name"
                      >
                        {relationships.map((entry, index) => (
                          <Cell 
                            key={`cell-${index}`} 
                            fill={pieChartColors[index % pieChartColors.length]}
                            onMouseEnter={(e) => {
                              if (!prefersReducedMotion && e?.target) {
                                e.target.style.opacity = '0.8';
                                e.target.style.transform = 'scale(1.05)';
                              }
                            }}
                            onMouseLeave={(e) => {
                              if (!prefersReducedMotion && e?.target) {
                                e.target.style.opacity = '1';
                                e.target.style.transform = 'scale(1)';
                              }
                            }}
                          />
                        ))}
                      </Pie>
                      <Tooltip 
                        content={<CustomTooltip isDarkMode={isDarkMode} />}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="mt-4">
                  <Button variant="soft" size="sm" className="w-full" asChild>
                    <Link to="/relationships">View all relationships →</Link>
                  </Button>
                </div>
              </div>
            )}
          </DashboardWidget>

          {/* Goal Progress */}
          <DashboardWidget
            title="Growth Goals"
            subtitle="Your progress"
            icon="Target"
            iconColor="positive"
            variant="glass"
            className="col-span-1 lg:col-span-2"
            tooltip="Track your progress towards personal growth goals in emotional intelligence and healthy communication."
            action={
              <Button variant="ghost" size="sm" asChild>
                <Link to="/growth-center">
                  <Icon name="ArrowRight" size="sm" />
                </Link>
              </Button>
            }
          >
            <div className="space-y-6">
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Set healthy boundaries</span>
                  <span className="text-sm font-semibold bg-gradient-to-r from-emotion-positive to-accent-green bg-clip-text text-transparent">60%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                  <div 
                    className="bg-gradient-to-r from-emotion-positive to-accent-green h-3 rounded-full transition-all duration-700 ease-out" 
                    style={{ width: '60%' }}
                  ></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Recognize gaslighting</span>
                  <span className="text-sm font-semibold bg-gradient-to-r from-emotion-positive to-accent-green bg-clip-text text-transparent">85%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                  <div 
                    className="bg-gradient-to-r from-emotion-positive to-accent-green h-3 rounded-full transition-all duration-700 ease-out" 
                    style={{ width: '85%' }}
                  ></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Improve emotional regulation</span>
                  <span className="text-sm font-semibold bg-gradient-to-r from-accent-yellow to-accent-coral bg-clip-text text-transparent">45%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                  <div 
                    className="bg-gradient-to-r from-accent-yellow to-accent-coral h-3 rounded-full transition-all duration-700 ease-out" 
                    style={{ width: '45%' }}
                  ></div>
                </div>
              </div>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Button variant="soft" size="sm" className="w-full" asChild>
                  <Link to="/growth-center">View your growth plan →</Link>
                </Button>
              </div>
            </div>
          </DashboardWidget>
        </div>
      </div>

      {/* Floating Action Button */}
      <FAB />
    </>
  );
};

export default Dashboard;
