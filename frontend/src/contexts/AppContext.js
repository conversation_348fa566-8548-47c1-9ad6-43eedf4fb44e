import React, { createContext, useState, useEffect, useContext, useMemo, useCallback } from 'react';
import axios from 'axios';
import { useAuth0 } from '@auth0/auth0-react';

// Create context
const AppContext = createContext();

// Custom hook to use the context
export const useAppContext = () => useContext(AppContext);

export const AppProvider = ({ children }) => {
  const { isAuthenticated, getAccessTokenSilently, isLoading: auth0Loading } = useAuth0();
  
  // Global state
  const [dashboardData, setDashboardData] = useState(null);
  const [isLoadingDashboard, setIsLoadingDashboard] = useState(true);
  const [dashboardError, setDashboardError] = useState(null);
  const [relationships, setRelationships] = useState([]);
  const [analysisHistory, setAnalysisHistory] = useState([]);
  const [growthPlan, setGrowthPlan] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('checking');

  // Configure axios with the backend URL - use relative URLs since we have a proxy
  const backendUrl = process.env.REACT_APP_BACKEND_URL || '';
  
  // Function to get auth headers - memoized to prevent unnecessary re-renders
  const getAuthHeaders = useCallback(async () => {
    if (isAuthenticated) {
      try {
        const token = await getAccessTokenSilently({
          audience: process.env.REACT_APP_AUTH0_AUDIENCE,
        });
        return {
          Authorization: `Bearer ${token}`,
        };
      } catch (error) {
        console.error('Error getting access token:', error);
        return {};
      }
    }
    return {};
  }, [isAuthenticated, getAccessTokenSilently]);
  
  // Configure axios
  useEffect(() => {
    // Add request interceptor for debugging and auth
    axios.interceptors.request.use(
      async config => {
        console.log(`Making ${config.method.toUpperCase()} request to: ${config.url}`);
        
        // Add auth headers for protected endpoints
        if (isAuthenticated && !auth0Loading) {
          const authHeaders = await getAuthHeaders();
          config.headers = { ...config.headers, ...authHeaders };
        }
        
        return config;
      },
      error => {
        console.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for debugging
    axios.interceptors.response.use(
      response => {
        console.log(`Response from ${response.config.url}:`, response.status);
        return response;
      },
      error => {
        console.error('Response error:', error);
        return Promise.reject(error);
      }
    );

    // Check connection to backend only if not loading auth
    if (!auth0Loading) {
      checkBackendConnection();
    }
  }, [isAuthenticated, auth0Loading]);

  // Check if the backend is accessible - memoized to prevent unnecessary re-renders
  const checkBackendConnection = useCallback(async () => {
    try {
      const response = await axios.get(`${backendUrl}/api/health`);
      if (response.data && response.data.status === 'healthy') {
        console.log('Backend connection successful!');
        setConnectionStatus('connected');
      } else {
        console.warn('Backend returned unexpected response:', response.data);
        setConnectionStatus('error');
      }
    } catch (err) {
      console.error('Backend connection failed:', err);
      setConnectionStatus('error');
    }
  }, [backendUrl]);

  // Fetch dashboard data - memoized to prevent unnecessary re-renders
  const fetchDashboardData = useCallback(async () => {
    try {
      setIsLoadingDashboard(true);
      setDashboardError(null);

      const response = await axios.get(`${backendUrl}/api/dashboard`);
      console.log('Dashboard data:', response.data);
      setDashboardData(response.data);
    } catch (err) {
      console.error('Failed to fetch dashboard data:', err);
      setDashboardError('Failed to load dashboard data. Please try again later.');

      // Set default data if the API request fails
      if (!dashboardData) {
        setDashboardData({
          health_score: 75,
          total_analyses: 0,
          total_flags_detected: 0,
          flag_counts: {},
          sentiment_timeline: []
        });
      }
    } finally {
      setIsLoadingDashboard(false);
    }
  }, [backendUrl, dashboardData]);

  // Fetch analysis history - memoized to prevent unnecessary re-renders
  const fetchAnalysisHistory = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await axios.get(`${backendUrl}/api/history`);
      console.log('Analysis history:', response.data);
      setAnalysisHistory(response.data || []);
    } catch (err) {
      console.error('Failed to fetch analysis history:', err);
      setError('Failed to load analysis history');
      // Set empty array if the API request fails and no data exists
      if (!analysisHistory.length) {
        setAnalysisHistory([]);
      }
    } finally {
      setIsLoading(false);
    }
  }, [backendUrl, analysisHistory.length]);

  // Fetch relationships
  const fetchRelationships = async () => {
    if (!isAuthenticated) {
      console.log('Not authenticated, skipping relationships fetch');
      setRelationships([]);
      return;
    }

    try {
      setIsLoading(true);
      const authHeaders = await getAuthHeaders();
      const response = await axios.get(`${backendUrl}/api/relationships`, {
        headers: authHeaders
      });
      console.log('Relationships:', response.data);
      setRelationships(response.data || []);
    } catch (err) {
      console.error('Failed to fetch relationships:', err);
      setError('Failed to load relationships');
      // Set empty array if the API request fails
      setRelationships([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Analyze message - memoized to prevent unnecessary re-renders
  const analyzeMessage = useCallback(async (text, context = null, relationshipId = null) => {
    if (!isAuthenticated) {
      setError('Please log in to analyze messages');
      return null;
    }

    try {
      setIsLoading(true);
      setError(null);

      const authHeaders = await getAuthHeaders();
      const response = await axios.post(`${backendUrl}/api/analyze`, {
        text,
        context,
        relationship_id: relationshipId
      }, {
        headers: authHeaders
      });

      console.log('Analysis result:', response.data);

      // Update local history
      setAnalysisHistory(prev => [response.data, ...prev]);

      // Refresh dashboard data to reflect the new analysis
      fetchDashboardData();

      return response.data;
    } catch (err) {
      console.error('Analysis failed:', err);
      setError('Failed to analyze message. Please try again.');

      // Return a mock analysis result if the API request fails
      const mockResult = {
        id: Date.now().toString(),
        text,
        context,
        relationship_id: relationshipId,
        flags: [],
        interpretation: "Unable to analyze message due to connection issues.",
        suggestions: ["Try again when connection is restored"],
        sentiment: "neutral",
        created_at: new Date().toISOString()
      };

      // Still update history with mock result
      setAnalysisHistory(prev => [mockResult, ...prev]);

      return mockResult;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, backendUrl, getAuthHeaders]);

  // Load initial data
  useEffect(() => {
    if (connectionStatus === 'connected') {
      fetchDashboardData();
      fetchAnalysisHistory();
      fetchRelationships();
      
      // Fetch growth plan
      fetchGrowthPlan();
    }
  }, [connectionStatus]);

  // Fetch growth plan
  const fetchGrowthPlan = async () => {
    try {
      const response = await axios.get(`${backendUrl}/api/growth-plan`);
      console.log('Growth plan:', response.data);
      setGrowthPlan(response.data);
    } catch (err) {
      console.error('Failed to fetch growth plan:', err);
      // Set default growth plan if the API request fails
      setGrowthPlan({
        current_week: {
          theme: "Emotional self-validation",
          days: [
            {
              day: 1,
              title: "Understanding Self-Validation",
              activity_type: "Guided Self-Inquiry",
              content: "Reflect on when you've dismissed your own feelings. What triggers self-doubt about your emotional responses?"
            },
            {
              day: 2,
              title: "Recognizing Emotional Patterns",
              activity_type: "Emotional Pattern Reframe",
              content: "Identify a recurring emotional response that you often judge. How would you respond to a friend with the same feelings?"
            },
            {
              day: 3,
              title: "Practice Validating Language",
              activity_type: "Practice/Script Challenge",
              content: "Write three statements that validate your feelings about a recent difficult situation."
            },
            {
              day: 4,
              title: "Micro-Ritual",
              activity_type: "Daily Practice",
              content: "Each time you notice self-criticism today, place a hand over your heart and say 'This feeling is valid.'"
            },
            {
              day: 5,
              title: "Weekly Reflection",
              activity_type: "Journal Prompt",
              content: "How has validating your emotions changed your interactions this week? What differences did you notice?"
            }
          ]
        },
        goals: [
          "Affirm my feelings without judgment",
          "Recognize when I'm dismissing my own emotions",
          "Respond to myself with the same compassion I'd offer others"
        ]
      });
    }
  };

  // Refresh data periodically
  useEffect(() => {
    if (connectionStatus === 'connected') {
      const interval = setInterval(() => {
        fetchDashboardData();
      }, 300000); // Refresh every 5 minutes

      return () => clearInterval(interval);
    }
  }, [connectionStatus]);

  // Memoized value object to prevent unnecessary re-renders
  const value = useMemo(() => ({
    dashboardData,
    isLoadingDashboard,
    dashboardError,
    relationships,
    analysisHistory,
    growthPlan,
    isLoading,
    error,
    connectionStatus,
    analyzeMessage,
    fetchDashboardData,
    fetchAnalysisHistory,
    fetchRelationships,
    setRelationships,
    checkBackendConnection
  }), [
    dashboardData,
    isLoadingDashboard,
    dashboardError,
    relationships,
    analysisHistory,
    growthPlan,
    isLoading,
    error,
    connectionStatus,
    analyzeMessage,
    fetchDashboardData,
    fetchAnalysisHistory,
    fetchRelationships,
    checkBackendConnection
  ]);

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};
