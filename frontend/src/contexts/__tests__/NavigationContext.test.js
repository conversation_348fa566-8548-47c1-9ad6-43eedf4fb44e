import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { NavigationProvider, useNavigation } from '../NavigationContext';

// Test component to access navigation context
const TestComponent = () => {
  const {
    currentPath,
    pageTitle,
    pageDescription,
    isNavigating,
    canGoBack,
    navigationHistory,
    navigateWithLoading,
    goBack
  } = useNavigation();

  return (
    <div>
      <div data-testid="current-path">{currentPath}</div>
      <div data-testid="page-title">{pageTitle}</div>
      <div data-testid="page-description">{pageDescription}</div>
      <div data-testid="is-navigating">{isNavigating.toString()}</div>
      <div data-testid="can-go-back">{canGoBack.toString()}</div>
      <div data-testid="history-length">{navigationHistory.length}</div>
      <button onClick={() => navigateWithLoading('/dashboard')}>
        Navigate to Dashboard
      </button>
      <button onClick={goBack}>Go Back</button>
    </div>
  );
};

// Mock react-router-dom
const mockNavigate = jest.fn();
const mockUseLocation = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => mockUseLocation()
}));

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <NavigationProvider>
      {children}
    </NavigationProvider>
  </BrowserRouter>
);

describe('NavigationContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset document title
    document.title = '';
    // Mock location
    mockUseLocation.mockReturnValue({
      pathname: '/',
      search: ''
    });
  });

  it('provides navigation context values', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('current-path')).toHaveTextContent('/');
    expect(screen.getByTestId('page-title')).toHaveTextContent('My ÆI - Emotional Intelligence Dashboard');
    expect(screen.getByTestId('is-navigating')).toHaveTextContent('false');
  });

  it('updates page metadata based on current path', () => {
    mockUseLocation.mockReturnValue({
      pathname: '/dashboard',
      search: ''
    });

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('page-title')).toHaveTextContent('Dashboard - My ÆI');
    expect(screen.getByTestId('page-description')).toHaveTextContent('Your emotional intelligence overview and key metrics');
  });

  it('updates document title when location changes', () => {
    mockUseLocation.mockReturnValue({
      pathname: '/dashboard',
      search: ''
    });

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    expect(document.title).toBe('Dashboard - My ÆI');
  });

  it('tracks navigation history', async () => {
    const { rerender } = render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    // Initial state
    expect(screen.getByTestId('history-length')).toHaveTextContent('1');

    // Simulate navigation to dashboard
    mockUseLocation.mockReturnValue({
      pathname: '/dashboard',
      search: ''
    });

    rerender(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId('history-length')).toHaveTextContent('2');
    });
  });

  it('handles navigateWithLoading correctly', async () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    const navigateButton = screen.getByText('Navigate to Dashboard');
    
    act(() => {
      navigateButton.click();
    });

    // Should show loading state briefly
    expect(screen.getByTestId('is-navigating')).toHaveTextContent('true');

    // Should call navigate
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard', {});
    });
  });

  it('determines canGoBack correctly', () => {
    // On home page with no history
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('can-go-back')).toHaveTextContent('false');

    // On non-home page
    mockUseLocation.mockReturnValue({
      pathname: '/dashboard',
      search: ''
    });

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('can-go-back')).toHaveTextContent('true');
  });

  it('throws error when used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useNavigation must be used within a NavigationProvider');

    consoleSpy.mockRestore();
  });
});
