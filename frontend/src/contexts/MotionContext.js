import React, { createContext, useState, useContext, useEffect } from 'react';

// Create context
const MotionContext = createContext();

// Motion preference options
export const MOTION_PREFERENCES = {
  FULL: 'full',
  REDUCED: 'reduced'
};

// Provider component
export const MotionProvider = ({ children }) => {
  // Check if user has system preference for reduced motion
  const prefersReducedMotion = () => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  };

  // Initialize state from localStorage or system preference or default to full
  const [motionPreference, setMotionPreference] = useState(() => {
    // First check localStorage
    const savedPreference = localStorage.getItem('motionPreference');
    if (savedPreference) {
      return savedPreference;
    }
    
    // Then check system preference
    if (prefersReducedMotion()) {
      return MOTION_PREFERENCES.REDUCED;
    }
    
    // Default to full motion
    return MOTION_PREFERENCES.FULL;
  });

  // Update data-motion attribute on document when motion preference changes
  useEffect(() => {
    // Set data attribute on document for CSS targeting
    document.documentElement.setAttribute('data-motion', motionPreference);
    
    // Save preference to localStorage
    localStorage.setItem('motionPreference', motionPreference);
  }, [motionPreference]);

  // Listen for changes in system preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleChange = (e) => {
      if (e.matches) {
        setMotionPreference(MOTION_PREFERENCES.REDUCED);
      } else {
        setMotionPreference(MOTION_PREFERENCES.FULL);
      }
    };
    
    // Add event listener
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleChange);
    }
    
    // Clean up
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleChange);
      } else {
        // Fallback for older browsers
        mediaQuery.removeListener(handleChange);
      }
    };
  }, []);

  // Toggle between motion preferences
  const toggleMotionPreference = () => {
    setMotionPreference(prev => 
      prev === MOTION_PREFERENCES.FULL 
        ? MOTION_PREFERENCES.REDUCED 
        : MOTION_PREFERENCES.FULL
    );
  };

  // Set specific motion preference
  const setReducedMotion = (reduced) => {
    setMotionPreference(reduced ? MOTION_PREFERENCES.REDUCED : MOTION_PREFERENCES.FULL);
  };

  return (
    <MotionContext.Provider value={{ 
      motionPreference, 
      isReducedMotion: motionPreference === MOTION_PREFERENCES.REDUCED,
      toggleMotionPreference,
      setReducedMotion
    }}>
      {children}
    </MotionContext.Provider>
  );
};

// Custom hook for using the motion context
export const useMotion = () => {
  const context = useContext(MotionContext);
  if (context === undefined) {
    throw new Error('useMotion must be used within a MotionProvider');
  }
  return context;
};

export default MotionContext;