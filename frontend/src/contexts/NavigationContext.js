import React, { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const NavigationContext = createContext();

export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

// Navigation history and state management
export const NavigationProvider = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Navigation state
  const [navigationHistory, setNavigationHistory] = useState([]);
  const [isNavigating, setIsNavigating] = useState(false);
  const [pageTitle, setPageTitle] = useState('');
  const [pageDescription, setPageDescription] = useState('');

  // Page metadata configuration
  const pageMetadata = useMemo(() => ({
    '/': {
      title: 'My ÆI - Emotional Intelligence Dashboard',
      description: 'Track and improve your emotional intelligence with AI-powered insights'
    },
    '/dashboard': {
      title: 'Dashboard - My ÆI',
      description: 'Your emotional intelligence overview and key metrics'
    },
    '/message-analyzer': {
      title: 'Message Analyzer - My ÆI',
      description: 'Analyze the emotional tone and impact of your messages'
    },
    '/relationships': {
      title: 'Relationships - My ÆI',
      description: 'Manage and improve your relationship dynamics'
    },
    '/growth-center': {
      title: 'Growth Center - My ÆI',
      description: 'Personalized recommendations for emotional growth'
    },
    '/reports': {
      title: 'Reports - My ÆI',
      description: 'Detailed analytics and progress reports'
    },
    '/anomaly-detection': {
      title: 'Anomaly Detection - My ÆI',
      description: 'Identify unusual patterns in your emotional data'
    }
  }), []);

  // Update page metadata when location changes
  useEffect(() => {
    const metadata = pageMetadata[location.pathname] || {
      title: 'My ÆI',
      description: 'Emotional Intelligence Dashboard'
    };
    
    setPageTitle(metadata.title);
    setPageDescription(metadata.description);
    
    // Update document title
    document.title = metadata.title;
    
    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', metadata.description);
    }
  }, [location.pathname, pageMetadata]);

  // Track navigation history
  useEffect(() => {
    setNavigationHistory(prev => {
      const newHistory = [...prev];
      const currentEntry = {
        pathname: location.pathname,
        search: location.search,
        timestamp: Date.now()
      };
      
      // Avoid duplicate consecutive entries
      if (newHistory.length === 0 || 
          newHistory[newHistory.length - 1].pathname !== location.pathname) {
        newHistory.push(currentEntry);
        
        // Keep only last 10 entries
        if (newHistory.length > 10) {
          newHistory.shift();
        }
      }
      
      return newHistory;
    });
  }, [location.pathname, location.search]);

  // Navigation helpers
  const navigateWithLoading = useCallback(async (to, options = {}) => {
    setIsNavigating(true);
    
    try {
      // Add small delay for loading state visibility
      await new Promise(resolve => setTimeout(resolve, 100));
      navigate(to, options);
    } finally {
      // Reset loading state after navigation
      setTimeout(() => setIsNavigating(false), 200);
    }
  }, [navigate]);

  const goBack = useCallback(() => {
    if (navigationHistory.length > 1) {
      const previousPage = navigationHistory[navigationHistory.length - 2];
      navigateWithLoading(previousPage.pathname + previousPage.search);
    } else {
      navigateWithLoading('/');
    }
  }, [navigationHistory, navigateWithLoading]);

  const canGoBack = useMemo(() => {
    return navigationHistory.length > 1 || location.pathname !== '/';
  }, [navigationHistory.length, location.pathname]);

  // Breadcrumb helpers
  const getCurrentPageInfo = useCallback(() => {
    return {
      pathname: location.pathname,
      title: pageTitle,
      description: pageDescription,
      canGoBack
    };
  }, [location.pathname, pageTitle, pageDescription, canGoBack]);

  // Context value
  const value = useMemo(() => ({
    // Current state
    currentPath: location.pathname,
    isNavigating,
    pageTitle,
    pageDescription,
    navigationHistory,
    canGoBack,
    
    // Navigation methods
    navigateWithLoading,
    goBack,
    getCurrentPageInfo,
    
    // Setters for page metadata
    setPageTitle,
    setPageDescription
  }), [
    location.pathname,
    isNavigating,
    pageTitle,
    pageDescription,
    navigationHistory,
    canGoBack,
    navigateWithLoading,
    goBack,
    getCurrentPageInfo
  ]);

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  );
};
