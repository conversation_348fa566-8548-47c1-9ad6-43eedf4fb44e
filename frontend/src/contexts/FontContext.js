import React, { createContext, useState, useContext, useEffect } from 'react';

// Create context
const FontContext = createContext();

// Font preference options
export const FONT_PREFERENCES = {
  STANDARD: 'standard',
  DYSLEXIC: 'dyslexic'
};

// Provider component
export const FontProvider = ({ children }) => {
  // Initialize state from localStorage or default to standard
  const [fontPreference, setFontPreference] = useState(() => {
    const savedPreference = localStorage.getItem('fontPreference');
    return savedPreference || FONT_PREFERENCES.STANDARD;
  });

  // Update body class when font preference changes
  useEffect(() => {
    // Remove all font preference classes
    document.body.classList.remove('font-standard', 'font-dyslexic');
    
    // Add the current preference class
    document.body.classList.add(`font-${fontPreference}`);
    
    // Save preference to localStorage
    localStorage.setItem('fontPreference', fontPreference);
  }, [fontPreference]);

  // Toggle between font preferences
  const toggleFontPreference = () => {
    setFontPreference(prev => 
      prev === FONT_PREFERENCES.STANDARD 
        ? FONT_PREFERENCES.DYSLEXIC 
        : FONT_PREFERENCES.STANDARD
    );
  };

  return (
    <FontContext.Provider value={{ fontPreference, toggleFontPreference }}>
      {children}
    </FontContext.Provider>
  );
};

// Custom hook for using the font context
export const useFontPreference = () => {
  const context = useContext(FontContext);
  if (context === undefined) {
    throw new Error('useFontPreference must be used within a FontProvider');
  }
  return context;
};

export default FontContext;