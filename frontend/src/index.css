/* Import design tokens for semantic color system */
@import './styles/design-tokens.css';

/* Using system fonts for better performance and avoiding CORS issues */

/* Self-hosted OpenDyslexic font for accessibility */
@font-face {
  font-family: 'OpenDyslexic';
  src: url('/fonts/OpenDyslexic-Regular.woff2') format('woff2'),
       url('/fonts/OpenDyslexic-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'OpenDyslexic';
  src: url('/fonts/OpenDyslexic-Bold.woff2') format('woff2'),
       url('/fonts/OpenDyslexic-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'OpenDyslexic';
  src: url('/fonts/OpenDyslexic-Italic.woff2') format('woff2'),
       url('/fonts/OpenDyslexic-Italic.woff') format('woff');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import mobile optimization styles */
@import './styles/mobile-optimizations.css';

@layer base {
  /* Enhanced body typography with consistent system fonts */
  body {
    margin: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    @apply text-base text-text-primary leading-normal;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }

  /* Standard font mode */
  body.font-standard {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }

  /* Dyslexia-friendly font mode */
  body.font-dyslexic {
    font-family: 'OpenDyslexic', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    letter-spacing: 0.05em;
    word-spacing: 0.1em;
    line-height: 1.8;
  }

  /* Enhanced heading hierarchy with refined typography */
  h1 {
    @apply font-bold text-text-primary mt-8 mb-6;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-size: clamp(2.5rem, 5vw + 1rem, 4.5rem);
    line-height: 1.1;
    letter-spacing: -0.04em;
  }

  h2 {
    @apply font-semibold text-text-primary mt-6 mb-4;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-size: clamp(2rem, 4vw + 0.5rem, 3.5rem);
    line-height: 1.2;
    letter-spacing: -0.035em;
  }

  h3 {
    @apply font-semibold text-text-primary mt-5 mb-3;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-size: clamp(1.5rem, 3vw + 0.25rem, 2.5rem);
    line-height: 1.3;
    letter-spacing: -0.03em;
  }

  h4 {
    @apply font-medium text-text-primary mt-4 mb-3;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-size: clamp(1.25rem, 2.5vw + 0.25rem, 2rem);
    line-height: 1.4;
    letter-spacing: -0.025em;
  }

  h5 {
    @apply font-medium text-text-primary mt-4 mb-2;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-size: clamp(1.125rem, 2vw + 0.25rem, 1.5rem);
    line-height: 1.5;
    letter-spacing: -0.02em;
  }

  h6 {
    @apply font-medium text-text-primary mt-3 mb-2;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-size: clamp(1rem, 1.5vw + 0.25rem, 1.25rem);
    line-height: 1.6;
    letter-spacing: -0.015em;
  }

  /* Enhanced paragraph spacing and readability */
  p {
    @apply mb-4 text-text-primary;
    font-size: clamp(1rem, 1vw + 0.5rem, 1.125rem);
    line-height: 1.75;
    letter-spacing: 0;
    max-width: 65ch; /* Optimal reading width */
  }
  
  /* Responsive list styles */
  ul, ol {
    @apply mb-4 pl-6 text-text-primary;
    font-size: clamp(1rem, 1vw + 0.5rem, 1.125rem);
    line-height: 1.75;
  }
  
  li {
    @apply mb-2;
  }
  
  /* Enhanced link styles */
  a {
    @apply transition-colors duration-200;
    color: theme('colors.text.link.DEFAULT');
    text-decoration: none;
  }
  
  a:hover {
    color: theme('colors.text.link.hover');
    text-decoration: underline;
    text-underline-offset: 0.2em;
  }
  
  /* Enhanced blockquote styles */
  blockquote {
    @apply pl-4 border-l-4 border-primary-500/30 italic my-6;
    font-size: clamp(1.125rem, 1.25vw + 0.5rem, 1.25rem);
    line-height: 1.75;
    letter-spacing: -0.01em;
  }
}

/* Enhanced text style utility classes */
@layer components {
  /* Body text variations with responsive sizing */
  .text-body {
    @apply text-text-primary;
    font-size: clamp(1rem, 1vw + 0.5rem, 1.125rem);
    line-height: 1.75;
    letter-spacing: 0;
  }

  .text-body-large {
    @apply text-text-primary;
    font-size: clamp(1.125rem, 1.25vw + 0.5rem, 1.375rem);
    line-height: 1.875;
    letter-spacing: -0.01em;
  }

  .text-body-small {
    @apply text-text-secondary;
    font-size: clamp(0.875rem, 0.75vw + 0.5rem, 1rem);
    line-height: 1.625;
    letter-spacing: 0.015em;
  }

  /* Caption and label variations */
  .text-caption {
    @apply text-text-tertiary italic;
    font-size: clamp(0.75rem, 0.5vw + 0.5rem, 0.875rem);
    line-height: 1.5;
    letter-spacing: 0.025em;
  }

  .text-overline {
    @apply uppercase font-semibold text-text-secondary;
    font-size: clamp(0.625rem, 0.5vw + 0.375rem, 0.75rem);
    line-height: 1.25;
    letter-spacing: 0.15em;
  }
  
  .text-label {
    @apply font-medium text-text-primary;
    font-size: clamp(0.875rem, 0.75vw + 0.5rem, 1rem);
    line-height: 1.5;
    letter-spacing: 0.025em;
  }

  /* Enhanced text containers with better responsive behavior */
  .text-container {
    @apply max-w-[65ch] mx-auto px-4;
  }

  .text-container-narrow {
    @apply max-w-[45ch] mx-auto px-4;
  }

  .text-container-wide {
    @apply max-w-[80ch] mx-auto px-4;
  }

  /* Enhanced emotional typography variations */
  .text-positive {
    @apply text-xl leading-loose font-medium text-emotion-positive-600;
  }

  .text-technical {
    @apply text-base leading-snug text-text-secondary;
    font-family: ui-monospace, SFMono-Regular, 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  }

  .text-reflective {
    @apply text-lg italic leading-relaxed text-secondary-lavender-600;
  }

  .text-alert {
    @apply text-base font-semibold leading-normal text-emotion-negative-600;
  }

  .text-calm {
    @apply text-base leading-relaxed text-emotion-neutral-600;
  }

  /* Enhanced focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset;
  }

  /* Enhanced glassmorphism utilities */
  .glass-card {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-card:hover {
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.4);
  }

  /* Ensure backdrop-filter support */
  @supports (backdrop-filter: blur(10px)) {
    .backdrop-blur-md {
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
    }
  }

  /* Fallback for browsers without backdrop-filter support */
  @supports not (backdrop-filter: blur(10px)) {
    .backdrop-blur-md {
      background: rgba(255, 255, 255, 0.8);
    }
  }
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Reduced Motion Styles */
@layer utilities {
  /* When reduced motion is enabled via context */
  html[data-motion="reduced"] * {
    /* Disable all animations and transitions */
    animation-duration: 0.001ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.001ms !important;
    scroll-behavior: auto !important;
  }

  /* Alternative animations for essential animations when reduced motion is enabled */
  html[data-motion="reduced"] .animate-fade {
    opacity: 1 !important;
  }

  html[data-motion="reduced"] .animate-slide {
    transform: none !important;
  }

  html[data-motion="reduced"] .animate-pulse {
    animation: none !important;
    opacity: 0.7;
  }

  /* Ensure focus indicators are still visible */
  html[data-motion="reduced"] *:focus {
    outline: 2px solid currentColor !important;
    outline-offset: 2px !important;
  }
}
