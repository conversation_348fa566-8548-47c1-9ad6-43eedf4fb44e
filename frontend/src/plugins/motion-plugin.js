const plugin = require('tailwindcss/plugin');

// Motion design tokens
const motionTokens = {
  // Timing curves (Material Design standard easing)
  curves: {
    // Standard curve - for most interactions
    standard: 'cubic-bezier(0.4, 0, 0.2, 1)',
    // Deceleration curve - objects entering the screen
    decelerate: 'cubic-bezier(0, 0, 0.2, 1)',
    // Acceleration curve - objects leaving the screen
    accelerate: 'cubic-bezier(0.4, 0, 1, 1)',
    // Sharp curve - for objects that may return
    sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
    // Emphasized curve - for larger movements
    emphasized: 'cubic-bezier(0.2, 0, 0, 1)',
    // Bounce curve - for playful interactions
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    // Elastic curve - for stretchy effects
    elastic: 'cubic-bezier(0.68, -0.55, 0.265, 1.35)',
    // Smooth curve - for very subtle transitions
    smooth: 'cubic-bezier(0.25, 0.1, 0.25, 1)',
  },

  // Duration scales
  durations: {
    instant: '50ms',
    faster: '100ms',
    fast: '150ms',
    normal: '200ms',
    moderate: '250ms',
    slow: '300ms',
    slower: '400ms',
    slowest: '500ms',
    // Special durations
    ripple: '600ms',
    morph: '300ms',
    fade: '200ms',
    slide: '250ms',
    scale: '200ms',
    rotate: '300ms',
    // Loading states
    skeleton: '1.5s',
    spinner: '1s',
    pulse: '2s',
  },

  // Delay scales
  delays: {
    none: '0ms',
    shortest: '50ms',
    shorter: '100ms',
    short: '150ms',
    normal: '200ms',
    long: '300ms',
    longer: '400ms',
    longest: '500ms',
    // Stagger delays for list animations
    stagger1: '50ms',
    stagger2: '100ms',
    stagger3: '150ms',
    stagger4: '200ms',
    stagger5: '250ms',
  },
};

// Keyframe definitions
const keyframes = {
  // Fade animations
  'fade-in': {
    '0%': { opacity: '0' },
    '100%': { opacity: '1' },
  },
  'fade-out': {
    '0%': { opacity: '1' },
    '100%': { opacity: '0' },
  },
  'fade-in-up': {
    '0%': { 
      opacity: '0',
      transform: 'translateY(10px)',
    },
    '100%': { 
      opacity: '1',
      transform: 'translateY(0)',
    },
  },
  'fade-in-down': {
    '0%': { 
      opacity: '0',
      transform: 'translateY(-10px)',
    },
    '100%': { 
      opacity: '1',
      transform: 'translateY(0)',
    },
  },
  'fade-in-left': {
    '0%': { 
      opacity: '0',
      transform: 'translateX(10px)',
    },
    '100%': { 
      opacity: '1',
      transform: 'translateX(0)',
    },
  },
  'fade-in-right': {
    '0%': { 
      opacity: '0',
      transform: 'translateX(-10px)',
    },
    '100%': { 
      opacity: '1',
      transform: 'translateX(0)',
    },
  },

  // Scale animations
  'scale-in': {
    '0%': { 
      opacity: '0',
      transform: 'scale(0.9)',
    },
    '100%': { 
      opacity: '1',
      transform: 'scale(1)',
    },
  },
  'scale-out': {
    '0%': { 
      opacity: '1',
      transform: 'scale(1)',
    },
    '100%': { 
      opacity: '0',
      transform: 'scale(0.9)',
    },
  },
  'scale-in-up': {
    '0%': { 
      opacity: '0',
      transform: 'scale(0.9) translateY(10px)',
    },
    '100%': { 
      opacity: '1',
      transform: 'scale(1) translateY(0)',
    },
  },

  // Slide animations
  'slide-in-up': {
    '0%': { transform: 'translateY(100%)' },
    '100%': { transform: 'translateY(0)' },
  },
  'slide-in-down': {
    '0%': { transform: 'translateY(-100%)' },
    '100%': { transform: 'translateY(0)' },
  },
  'slide-in-left': {
    '0%': { transform: 'translateX(-100%)' },
    '100%': { transform: 'translateX(0)' },
  },
  'slide-in-right': {
    '0%': { transform: 'translateX(100%)' },
    '100%': { transform: 'translateX(0)' },
  },

  // Rotate animations
  'rotate-in': {
    '0%': { 
      opacity: '0',
      transform: 'rotate(-180deg)',
    },
    '100%': { 
      opacity: '1',
      transform: 'rotate(0)',
    },
  },
  'rotate-out': {
    '0%': { 
      opacity: '1',
      transform: 'rotate(0)',
    },
    '100%': { 
      opacity: '0',
      transform: 'rotate(180deg)',
    },
  },

  // Flip animations
  'flip-in-x': {
    '0%': { 
      opacity: '0',
      transform: 'perspective(400px) rotateX(90deg)',
    },
    '100%': { 
      opacity: '1',
      transform: 'perspective(400px) rotateX(0)',
    },
  },
  'flip-in-y': {
    '0%': { 
      opacity: '0',
      transform: 'perspective(400px) rotateY(90deg)',
    },
    '100%': { 
      opacity: '1',
      transform: 'perspective(400px) rotateY(0)',
    },
  },

  // Bounce animations
  'bounce-in': {
    '0%': { 
      opacity: '0',
      transform: 'scale(0.3)',
    },
    '50%': { 
      opacity: '1',
      transform: 'scale(1.05)',
    },
    '70%': { transform: 'scale(0.9)' },
    '100%': { transform: 'scale(1)' },
  },
  'bounce-soft': {
    '0%, 100%': { transform: 'translateY(0)' },
    '50%': { transform: 'translateY(-5px)' },
  },

  // Shake animation
  'shake': {
    '0%, 100%': { transform: 'translateX(0)' },
    '10%, 30%, 50%, 70%, 90%': { transform: 'translateX(-2px)' },
    '20%, 40%, 60%, 80%': { transform: 'translateX(2px)' },
  },
  'shake-horizontal': {
    '0%, 100%': { transform: 'translateX(0)' },
    '10%, 30%, 50%, 70%, 90%': { transform: 'translateX(-4px)' },
    '20%, 40%, 60%, 80%': { transform: 'translateX(4px)' },
  },

  // Pulse animations
  'pulse-scale': {
    '0%, 100%': { transform: 'scale(1)' },
    '50%': { transform: 'scale(1.05)' },
  },
  'pulse-opacity': {
    '0%, 100%': { opacity: '1' },
    '50%': { opacity: '0.7' },
  },

  // Ripple effect
  'ripple': {
    '0%': { 
      transform: 'scale(0)',
      opacity: '1',
    },
    '100%': { 
      transform: 'scale(4)',
      opacity: '0',
    },
  },

  // Skeleton loading
  'skeleton-wave': {
    '0%': { 
      transform: 'translateX(-100%)',
    },
    '100%': { 
      transform: 'translateX(100%)',
    },
  },

  // Spinner animations
  'spin-slow': {
    '0%': { transform: 'rotate(0deg)' },
    '100%': { transform: 'rotate(360deg)' },
  },
  'spin-pulse': {
    '0%, 100%': { 
      transform: 'rotate(0deg) scale(1)',
      opacity: '1',
    },
    '50%': { 
      transform: 'rotate(180deg) scale(0.8)',
      opacity: '0.5',
    },
  },

  // Morph animations
  'morph': {
    '0%, 100%': { 
      borderRadius: '60% 40% 30% 70%/60% 30% 70% 40%',
    },
    '50%': { 
      borderRadius: '30% 60% 70% 40%/50% 60% 30% 60%',
    },
  },

  // Gradient animations
  'gradient-shift': {
    '0%, 100%': { 
      backgroundPosition: '0% 50%',
    },
    '50%': { 
      backgroundPosition: '100% 50%',
    },
  },

  // Float animation
  'float': {
    '0%, 100%': { 
      transform: 'translateY(0)',
    },
    '50%': { 
      transform: 'translateY(-10px)',
    },
  },

  // Attention seekers
  'wiggle': {
    '0%, 100%': { transform: 'rotate(0deg)' },
    '25%': { transform: 'rotate(-3deg)' },
    '75%': { transform: 'rotate(3deg)' },
  },
  'heartbeat': {
    '0%, 100%': { transform: 'scale(1)' },
    '14%': { transform: 'scale(1.3)' },
    '28%': { transform: 'scale(1)' },
    '42%': { transform: 'scale(1.3)' },
    '56%': { transform: 'scale(1)' },
  },
};

module.exports = plugin(function({ addUtilities, addBase, matchUtilities, theme }) {
  // Add CSS custom properties for motion tokens
  addBase({
    ':root': {
      // Timing curves
      '--motion-standard': motionTokens.curves.standard,
      '--motion-decelerate': motionTokens.curves.decelerate,
      '--motion-accelerate': motionTokens.curves.accelerate,
      '--motion-sharp': motionTokens.curves.sharp,
      '--motion-emphasized': motionTokens.curves.emphasized,
      '--motion-bounce': motionTokens.curves.bounce,
      '--motion-elastic': motionTokens.curves.elastic,
      '--motion-smooth': motionTokens.curves.smooth,

      // Durations
      '--duration-instant': motionTokens.durations.instant,
      '--duration-faster': motionTokens.durations.faster,
      '--duration-fast': motionTokens.durations.fast,
      '--duration-normal': motionTokens.durations.normal,
      '--duration-moderate': motionTokens.durations.moderate,
      '--duration-slow': motionTokens.durations.slow,
      '--duration-slower': motionTokens.durations.slower,
      '--duration-slowest': motionTokens.durations.slowest,

      // Special durations
      '--duration-ripple': motionTokens.durations.ripple,
      '--duration-morph': motionTokens.durations.morph,
      '--duration-fade': motionTokens.durations.fade,
      '--duration-slide': motionTokens.durations.slide,
      '--duration-scale': motionTokens.durations.scale,
      '--duration-rotate': motionTokens.durations.rotate,
      '--duration-skeleton': motionTokens.durations.skeleton,
      '--duration-spinner': motionTokens.durations.spinner,
      '--duration-pulse': motionTokens.durations.pulse,
    },

    // Reduced motion preferences
    '@media (prefers-reduced-motion: reduce)': {
      '*': {
        'animation-duration': '0.01ms !important',
        'animation-iteration-count': '1 !important',
        'transition-duration': '0.01ms !important',
        'scroll-behavior': 'auto !important',
      },
      // Keep essential animations but make them instant
      '.motion-safe\\:animate-spin': {
        'animation-duration': '1s !important',
      },
      '.motion-safe\\:animate-pulse': {
        'animation-duration': '2s !important',
      },
    },

    // Focus ring styles
    '*:focus': {
      outline: 'none',
    },
    '*:focus-visible': {
      outline: '2px solid',
      'outline-color': theme('colors.primary.500'),
      'outline-offset': '2px',
      'border-radius': theme('borderRadius.md'),
    },
    '.dark *:focus-visible': {
      'outline-color': theme('colors.primary.400'),
    },
  });

  // Transition utilities
  const transitionUtilities = {
    // Base transitions
    '.transition-standard': {
      'transition-timing-function': motionTokens.curves.standard,
      'transition-duration': motionTokens.durations.normal,
    },
    '.transition-decelerate': {
      'transition-timing-function': motionTokens.curves.decelerate,
      'transition-duration': motionTokens.durations.normal,
    },
    '.transition-accelerate': {
      'transition-timing-function': motionTokens.curves.accelerate,
      'transition-duration': motionTokens.durations.normal,
    },
    '.transition-sharp': {
      'transition-timing-function': motionTokens.curves.sharp,
      'transition-duration': motionTokens.durations.fast,
    },
    '.transition-emphasized': {
      'transition-timing-function': motionTokens.curves.emphasized,
      'transition-duration': motionTokens.durations.slow,
    },
    '.transition-bounce': {
      'transition-timing-function': motionTokens.curves.bounce,
      'transition-duration': motionTokens.durations.moderate,
    },
    '.transition-smooth': {
      'transition-timing-function': motionTokens.curves.smooth,
      'transition-duration': motionTokens.durations.slow,
    },

    // Hover lift effect
    '.hover-lift': {
      'transition': `transform ${motionTokens.durations.normal} ${motionTokens.curves.standard}`,
      '&:hover': {
        transform: 'translateY(-2px)',
      },
    },
    '.hover-lift-sm': {
      'transition': `transform ${motionTokens.durations.fast} ${motionTokens.curves.standard}`,
      '&:hover': {
        transform: 'translateY(-1px)',
      },
    },
    '.hover-lift-lg': {
      'transition': `transform ${motionTokens.durations.moderate} ${motionTokens.curves.standard}`,
      '&:hover': {
        transform: 'translateY(-4px)',
      },
    },

    // Hover scale effect
    '.hover-scale': {
      'transition': `transform ${motionTokens.durations.normal} ${motionTokens.curves.standard}`,
      '&:hover': {
        transform: 'scale(1.05)',
      },
    },
    '.hover-scale-sm': {
      'transition': `transform ${motionTokens.durations.fast} ${motionTokens.curves.standard}`,
      '&:hover': {
        transform: 'scale(1.02)',
      },
    },
    '.hover-scale-lg': {
      'transition': `transform ${motionTokens.durations.moderate} ${motionTokens.curves.standard}`,
      '&:hover': {
        transform: 'scale(1.1)',
      },
    },

    // Hover brightness effect
    '.hover-bright': {
      'transition': `filter ${motionTokens.durations.normal} ${motionTokens.curves.standard}`,
      '&:hover': {
        filter: 'brightness(1.1)',
      },
    },
    '.hover-dim': {
      'transition': `filter ${motionTokens.durations.normal} ${motionTokens.curves.standard}`,
      '&:hover': {
        filter: 'brightness(0.9)',
      },
    },

    // Active press effect
    '.active-scale': {
      'transition': `transform ${motionTokens.durations.fast} ${motionTokens.curves.standard}`,
      '&:active': {
        transform: 'scale(0.95)',
      },
    },
  };

  // Ripple effect utilities
  const rippleUtilities = {
    '.ripple': {
      position: 'relative',
      overflow: 'hidden',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '50%',
        left: '50%',
        width: '0',
        height: '0',
        borderRadius: '50%',
        background: 'rgba(255, 255, 255, 0.5)',
        transform: 'translate(-50%, -50%)',
        transition: `width ${motionTokens.durations.ripple} ${motionTokens.curves.standard}, height ${motionTokens.durations.ripple} ${motionTokens.curves.standard}`,
      },
      '&:active::before': {
        width: '300px',
        height: '300px',
      },
    },
    '.ripple-dark': {
      position: 'relative',
      overflow: 'hidden',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: '50%',
        left: '50%',
        width: '0',
        height: '0',
        borderRadius: '50%',
        background: 'rgba(0, 0, 0, 0.2)',
        transform: 'translate(-50%, -50%)',
        transition: `width ${motionTokens.durations.ripple} ${motionTokens.curves.standard}, height ${motionTokens.durations.ripple} ${motionTokens.curves.standard}`,
      },
      '&:active::before': {
        width: '300px',
        height: '300px',
      },
    },
  };

  // Loading skeleton utilities
  const skeletonUtilities = {
    '.skeleton': {
      position: 'relative',
      overflow: 'hidden',
      backgroundColor: theme('colors.gray.200'),
      '&::after': {
        content: '""',
        position: 'absolute',
        top: '0',
        right: '0',
        bottom: '0',
        left: '0',
        transform: 'translateX(-100%)',
        background: 'linear-gradient(90deg, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 0.2) 20%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0))',
        animation: `skeleton-wave ${motionTokens.durations.skeleton} infinite`,
      },
    },
    '.skeleton-dark': {
      position: 'relative',
      overflow: 'hidden',
      backgroundColor: theme('colors.gray.700'),
      '&::after': {
        content: '""',
        position: 'absolute',
        top: '0',
        right: '0',
        bottom: '0',
        left: '0',
        transform: 'translateX(-100%)',
        background: 'linear-gradient(90deg, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 0.05) 20%, rgba(255, 255, 255, 0.1) 60%, rgba(255, 255, 255, 0))',
        animation: `skeleton-wave ${motionTokens.durations.skeleton} infinite`,
      },
    },
    '.skeleton-pulse': {
      animation: `pulse-opacity ${motionTokens.durations.pulse} ${motionTokens.curves.standard} infinite`,
      backgroundColor: theme('colors.gray.200'),
    },
    '.skeleton-pulse-dark': {
      animation: `pulse-opacity ${motionTokens.durations.pulse} ${motionTokens.curves.standard} infinite`,
      backgroundColor: theme('colors.gray.700'),
    },
  };

  // Spinner utilities
  const spinnerUtilities = {
    '.spinner': {
      display: 'inline-block',
      width: '1em',
      height: '1em',
      border: '2px solid',
      borderColor: 'currentColor',
      borderTopColor: 'transparent',
      borderRadius: '50%',
      animation: `spin ${motionTokens.durations.spinner} linear infinite`,
    },
    '.spinner-dots': {
      display: 'inline-flex',
      gap: '0.25em',
      '& > span': {
        display: 'inline-block',
        width: '0.5em',
        height: '0.5em',
        backgroundColor: 'currentColor',
        borderRadius: '50%',
        animation: `pulse-scale ${motionTokens.durations.spinner} ${motionTokens.curves.standard} infinite`,
      },
      '& > span:nth-child(2)': {
        animationDelay: '0.2s',
      },
      '& > span:nth-child(3)': {
        animationDelay: '0.4s',
      },
    },
    '.spinner-pulse': {
      display: 'inline-block',
      width: '1em',
      height: '1em',
      backgroundColor: 'currentColor',
      borderRadius: '50%',
      animation: `pulse-scale ${motionTokens.durations.spinner} ${motionTokens.curves.standard} infinite`,
    },
  };

  // Add all utilities
  addUtilities({
    ...transitionUtilities,
    ...rippleUtilities,
    ...skeletonUtilities,
    ...spinnerUtilities,
  });

  // Dynamic animation utilities
  matchUtilities(
    {
      'animate': (value) => ({
        animation: value,
      }),
    },
    {
      values: Object.keys(keyframes).reduce((acc, key) => {
        const duration = motionTokens.durations[key.split('-')[0]] || motionTokens.durations.normal;
        const curve = motionTokens.curves.standard;
        acc[key] = `${key} ${duration} ${curve}`;
        return acc;
      }, {}),
    }
  );

  // Dynamic duration utilities
  matchUtilities(
    {
      'duration': (value) => ({
        'animation-duration': value,
        'transition-duration': value,
      }),
    },
    {
      values: motionTokens.durations,
    }
  );

  // Dynamic delay utilities
  matchUtilities(
    {
      'delay': (value) => ({
        'animation-delay': value,
        'transition-delay': value,
      }),
    },
    {
      values: motionTokens.delays,
    }
  );

  // Dynamic timing function utilities
  matchUtilities(
    {
      'ease': (value) => ({
        'animation-timing-function': value,
        'transition-timing-function': value,
      }),
    },
    {
      values: motionTokens.curves,
    }
  );

  // Add motion-safe and motion-reduce variants
  addUtilities({
    '.motion-safe': {
      '@media (prefers-reduced-motion: no-preference)': {
        '&': {},
      },
    },
    '.motion-reduce': {
      '@media (prefers-reduced-motion: reduce)': {
        '&': {},
      },
    },
  });
}, {
  theme: {
    extend: {
      keyframes,
      animation: Object.keys(keyframes).reduce((acc, key) => {
        acc[key] = key;
        return acc;
      }, {}),
    },
  },
});
