import React from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { useMockAuth0 } from './MockAuth0Provider';
import Button from '../components/Button';

// Helper hook to determine which auth to use
const useAuth = () => {
  const auth0 = useAuth0();
  const mockAuth = useMockAuth0();
  
  // Check if Auth0 has an error (which would indicate invalid domain)
  if (auth0.error) {
    console.log('Auth0 error detected, using mock auth:', auth0.error);
    return { ...mockAuth, isMock: true };
  }
  
  // If no user and no loading, check if Auth0 domain is working
  if (!auth0.isLoading && !auth0.user && !auth0.isAuthenticated) {
    console.log('Auth0 not functional, using mock auth');
    return { ...mockAuth, isMock: true };
  }
  
  return { ...auth0, isMock: false };
};

const LoginButton = () => {
  const { loginWithRedirect, isLoading, error, isMock } = useAuth();

  console.log('LoginButton state:', { isLoading, error, isMock });

  const handleLogin = () => {
    console.log('Login button clicked');
    try {
      loginWithRedirect();
    } catch (err) {
      console.error('Login error:', err);
    }
  };

  return (
    <Button
      onClick={handleLogin}
      className="bg-blue-600 hover:bg-blue-700 text-white"
      disabled={isLoading}
      data-testid="login-button"
    >
      {isLoading ? 'Loading...' : isMock ? 'Demo Login' : 'Log In'}
    </Button>
  );
};

const LogoutButton = () => {
  const { logout, isMock } = useAuth();

  return (
    <Button
      onClick={() => logout({ returnTo: window.location.origin })}
      className="bg-red-600 hover:bg-red-700 text-white"
    >
      {isMock ? 'Demo Logout' : 'Log Out'}
    </Button>
  );
};

const UserProfile = () => {
  const { user, isAuthenticated, isLoading, isMock } = useAuth();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isAuthenticated) {
    return (
      <div className="flex items-center space-x-4">
        <img 
          src={user.picture} 
          alt={user.name} 
          className="w-8 h-8 rounded-full"
        />
        <span className="text-gray-700">
          {user.name} {isMock && '(Demo)'}
        </span>
        <LogoutButton />
      </div>
    );
  }

  return <LoginButton />;
};

export { LoginButton, LogoutButton, UserProfile };
