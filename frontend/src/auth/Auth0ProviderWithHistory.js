import React from 'react';
import { Auth0Provider } from '@auth0/auth0-react';

const Auth0ProviderWithHistory = ({ children }) => {
  const domain = process.env.REACT_APP_AUTH0_DOMAIN;
  const clientId = process.env.REACT_APP_AUTH0_CLIENT_ID;
  const audience = process.env.REACT_APP_AUTH0_AUDIENCE;
  const redirectUri = process.env.REACT_APP_AUTH0_REDIRECT_URI;

  // Debug logging for Auth0 configuration
  console.log('Auth0 Configuration:', {
    domain,
    clientId,
    audience,
    redirectUri
  });

  if (!domain || !clientId) {
    console.error('Auth0 configuration missing: domain or clientId not found');
    return children; // Return children directly to use mock auth
  }

  return (
    <Auth0Provider
      domain={domain}
      clientId={clientId}
      audience={audience}
      redirectUri={redirectUri}
      scope="openid profile email"
      onRedirectCallback={(appState) => {
        // Handle the redirect callback
        console.log('Auth0 redirect callback:', appState);
      }}
    >
      {children}
    </Auth0Provider>
  );
};

export default Auth0ProviderWithHistory;
