import React, { createContext, useContext, useState, useEffect } from 'react';

// Mock Auth0 context
const Auth0Context = createContext();

export const useMockAuth0 = () => {
  const context = useContext(Auth0Context);
  if (!context) {
    throw new Error('useMockAuth0 must be used within a MockAuth0Provider');
  }
  return context;
};

export const MockAuth0Provider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState(null);
  const [error, setError] = useState(null);

  // Check for existing mock session
  useEffect(() => {
    const mockUser = localStorage.getItem('mockAuth0User');
    if (mockUser) {
      setUser(JSON.parse(mockUser));
      setIsAuthenticated(true);
    }
  }, []);

  const loginWithRedirect = async () => {
    console.log('Mock login initiated');
    setIsLoading(true);
    setError(null);
    
    // Simulate login delay
    setTimeout(() => {
      const mockUser = {
        sub: 'mock|123456789',
        name: 'Test User',
        email: '<EMAIL>',
        email_verified: true,
        picture: 'https://via.placeholder.com/40/4F46E5/FFFFFF?text=TU'
      };
      
      setUser(mockUser);
      setIsAuthenticated(true);
      setIsLoading(false);
      
      // Store in localStorage for persistence
      localStorage.setItem('mockAuth0User', JSON.stringify(mockUser));
      
      console.log('Mock login successful:', mockUser);
    }, 1000);
  };

  const logout = ({ returnTo }) => {
    console.log('Mock logout');
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('mockAuth0User');
    
    if (returnTo) {
      window.location.href = returnTo;
    }
  };

  const getAccessTokenSilently = async () => {
    // Return a mock token
    return 'mock-access-token';
  };

  const value = {
    isAuthenticated,
    isLoading,
    user,
    error,
    loginWithRedirect,
    logout,
    getAccessTokenSilently
  };

  return (
    <Auth0Context.Provider value={value}>
      {children}
    </Auth0Context.Provider>
  );
};

export default MockAuth0Provider;
