import React from 'react';
import { useMockAuth0 } from './MockAuth0Provider';
import Button from '../components/Button';

const DevelopmentAuthButtons = () => {
  const { 
    isAuthenticated, 
    isLoading, 
    user, 
    loginWithRedirect, 
    logout 
  } = useMockAuth0();

  console.log('Development Auth state:', { isAuthenticated, isLoading, user });

  const handleLogin = () => {
    console.log('Development login clicked');
    loginWithRedirect();
  };

  const handleLogout = () => {
    console.log('Development logout clicked');
    logout({ returnTo: window.location.origin });
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isAuthenticated) {
    return (
      <div className="flex items-center space-x-4">
        <img 
          src={user?.picture || 'https://via.placeholder.com/40/4F46E5/FFFFFF?text=U'} 
          alt={user?.name || 'User'} 
          className="w-8 h-8 rounded-full"
        />
        <span className="text-gray-700">
          {user?.name || 'Test User'} (Demo)
        </span>
        <Button
          onClick={handleLogout}
          className="bg-red-600 hover:bg-red-700 text-white"
        >
          Demo Logout
        </Button>
      </div>
    );
  }

  return (
    <Button
      onClick={handleLogin}
      className="bg-blue-600 hover:bg-blue-700 text-white"
      disabled={isLoading}
      data-testid="login-button"
    >
      {isLoading ? 'Loading...' : 'Demo Login'}
    </Button>
  );
};

export default DevelopmentAuthButtons;
