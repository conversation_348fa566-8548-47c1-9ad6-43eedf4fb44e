import React from 'react';
import Button from './components/Button';
import Card from './components/Card';

/**
 * Test page to verify our Phase 1 UI improvements
 */
const TestPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-secondary-lavender-50 to-secondary-coral-50 p-8">
      <div className="max-w-6xl mx-auto space-y-8">

        {/* Typography Testing */}
        <Card variant="default" padding="large">
          <h1 className="mb-6">Typography Hierarchy Test</h1>
          <div className="space-y-4">
            <h1>H1: Main Page Title (2.25rem, bold)</h1>
            <h2>H2: Section Title (1.875rem, semibold)</h2>
            <h3>H3: Subsection Title (1.5rem, semibold)</h3>
            <h4>H4: Component Title (1.25rem, medium)</h4>
            <h5>H5: Small Title (1.125rem, medium)</h5>
            <h6>H6: Micro Title (1rem, medium)</h6>
            <p className="text-body">
              Body text with improved readability, consistent line height, and proper contrast ratios.
              This text should be easy to read and follow WCAG AA guidelines.
            </p>
            <p className="text-body-small">
              Secondary body text for less important information with appropriate hierarchy.
            </p>
          </div>
        </Card>

        {/* Button Component Testing */}
        <Card variant="default" padding="large">
          <h2 className="mb-6">Simplified Button Variants</h2>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <Button variant="primary" size="small">Primary Small</Button>
              <Button variant="primary" size="medium">Primary Medium</Button>
              <Button variant="primary" size="large">Primary Large</Button>
            </div>

            <div className="flex flex-wrap gap-4">
              <Button variant="secondary" size="medium">Secondary</Button>
              <Button variant="ghost" size="medium">Ghost</Button>
              <Button variant="danger" size="medium">Danger</Button>
            </div>

            <div className="flex flex-wrap gap-4">
              <Button variant="primary" size="medium" loading>Loading</Button>
              <Button variant="secondary" size="medium" disabled>Disabled</Button>
            </div>
          </div>
        </Card>

        {/* Card Component Testing */}
        <div className="space-y-6">
          <h2>Card Variants Test</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card variant="default" padding="medium">
              <h3 className="mb-2">Default Card</h3>
              <p className="text-body-small">
                Clean styling with subtle border and smooth hover effects.
              </p>
            </Card>

            <Card variant="elevated" padding="medium">
              <h3 className="mb-2">Elevated Card</h3>
              <p className="text-body-small">
                More prominent shadow for important content sections.
              </p>
            </Card>

            <Card variant="interactive" padding="medium" interactive>
              <h3 className="mb-2">Interactive Card</h3>
              <p className="text-body-small">
                Clickable card with enhanced hover states and cursor pointer.
              </p>
            </Card>

            <Card variant="glass" padding="medium">
              <h3 className="mb-2">Glass Card</h3>
              <p className="text-body-small">
                Modern glassmorphism effect with backdrop blur.
              </p>
            </Card>
          </div>
        </div>

        {/* Glassmorphism Showcase */}
        <div
          className="relative p-8 rounded-2xl bg-gradient-to-r from-primary-400 via-secondary-lavender-400 to-secondary-coral-400"
          style={{
            backgroundImage: `
              radial-gradient(circle at 20% 50%, rgba(42, 157, 143, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(157, 141, 241, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 40% 80%, rgba(231, 111, 81, 0.3) 0%, transparent 50%)
            `
          }}
        >
          <h2 className="text-white text-center mb-8">Glassmorphism Effect Showcase</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card variant="glass" padding="medium">
              <h3 className="mb-2">Glass Card 1</h3>
              <p className="text-body-small">
                This card demonstrates the glassmorphism effect with backdrop blur and transparency.
              </p>
            </Card>

            <Card variant="glass" padding="medium">
              <h3 className="mb-2">Glass Card 2</h3>
              <p className="text-body-small">
                Notice how the background shows through with a beautiful blur effect.
              </p>
            </Card>

            <Card variant="glass" padding="medium">
              <h3 className="mb-2">Glass Card 3</h3>
              <p className="text-body-small">
                The subtle transparency creates depth and modern aesthetics.
              </p>
            </Card>
          </div>
        </div>

        {/* Color System Testing */}
        <Card variant="default" padding="large">
          <h2 className="mb-6">Enhanced Color System</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <div className="bg-primary-50 p-4 rounded-lg text-center text-primary-900">Primary 50</div>
              <div className="bg-primary-500 p-4 rounded-lg text-center text-white">Primary 500</div>
              <div className="bg-primary-600 p-4 rounded-lg text-center text-white">Primary 600</div>
            </div>

            <div className="space-y-2">
              <div className="bg-emotion-positive-50 p-4 rounded-lg text-center text-emotion-positive-900">Positive 50</div>
              <div className="bg-emotion-positive-500 p-4 rounded-lg text-center text-white">Positive 500</div>
              <div className="bg-emotion-positive-600 p-4 rounded-lg text-center text-white">Positive 600</div>
            </div>

            <div className="space-y-2">
              <div className="bg-emotion-neutral-50 p-4 rounded-lg text-center text-emotion-neutral-900">Neutral 50</div>
              <div className="bg-emotion-neutral-500 p-4 rounded-lg text-center text-white">Neutral 500</div>
              <div className="bg-emotion-neutral-600 p-4 rounded-lg text-center text-white">Neutral 600</div>
            </div>

            <div className="space-y-2">
              <div className="bg-emotion-negative-50 p-4 rounded-lg text-center text-emotion-negative-900">Negative 50</div>
              <div className="bg-emotion-negative-500 p-4 rounded-lg text-center text-white">Negative 500</div>
              <div className="bg-emotion-negative-600 p-4 rounded-lg text-center text-white">Negative 600</div>
            </div>
          </div>
        </Card>

        {/* Spacing System Testing */}
        <Card variant="default" padding="large">
          <h2 className="mb-6">Consistent Spacing System</h2>
          <div className="space-y-4">
            <div className="bg-blue-100 p-2 rounded-lg">8px padding (sm)</div>
            <div className="bg-blue-200 p-4 rounded-lg">16px padding (md)</div>
            <div className="bg-blue-300 p-6 rounded-lg">24px padding (lg)</div>
            <div className="bg-blue-400 p-8 rounded-lg text-white">32px padding (xl)</div>
          </div>
        </Card>

        {/* Focus States Testing */}
        <Card variant="default" padding="large">
          <h2 className="mb-6">Enhanced Focus States</h2>
          <p className="text-body mb-4">Tab through these elements to test focus indicators:</p>
          <div className="space-x-4 space-y-2">
            <Button variant="primary" size="medium">Focusable Button</Button>
            <Button variant="secondary" size="medium">Another Button</Button>
            <input
              type="text"
              placeholder="Focusable Input"
              className="border border-border-default rounded-lg px-3 py-2 focus-ring"
            />
            <a
              href="#test"
              className="text-primary-600 underline focus-ring rounded px-1"
            >
              Focusable Link
            </a>
          </div>
        </Card>

      </div>
    </div>
  );
};

export default TestPage;
