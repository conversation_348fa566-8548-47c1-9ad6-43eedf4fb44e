/* Design Tokens - Semantic Color System for My ÆI */

:root {
  /* ===== SURFACE COLORS ===== */
  /* Primary surfaces for main content areas */
  --color-surface-primary: #ffffff;
  --color-surface-secondary: #f9fafb;
  --color-surface-tertiary: #f3f4f6;
  --color-surface-elevated: #ffffff;
  --color-surface-overlay: rgba(0, 0, 0, 0.5);
  
  /* ===== TEXT COLORS ===== */
  /* Text hierarchy for readability */
  --color-text-primary: #111827;
  --color-text-secondary: #6b7280;
  --color-text-tertiary: #9ca3af;
  --color-text-disabled: #d1d5db;
  --color-text-inverse: #ffffff;
  --color-text-link: #2A9D8F;
  --color-text-link-hover: #0d9488;
  
  /* ===== BORDER COLORS ===== */
  /* Consistent border system */
  --color-border-default: #e5e7eb;
  --color-border-medium: #d1d5db;
  --color-border-strong: #9ca3af;
  --color-border-focus: #2A9D8F;
  
  /* ===== BRAND COLORS ===== */
  /* Primary brand color system */
  --color-primary: #2A9D8F;
  --color-primary-hover: #0d9488;
  --color-primary-active: #0f766e;
  --color-primary-disabled: #5eead4;
  --color-primary-subtle: #f0fdfa;
  
  /* Secondary brand colors */
  --color-secondary-coral: #E76F51;
  --color-secondary-coral-hover: #dc2626;
  --color-secondary-coral-subtle: #fef2f2;
  
  --color-secondary-lavender: #9D8DF1;
  --color-secondary-lavender-hover: #8b5cf6;
  --color-secondary-lavender-subtle: #f5f3ff;
  
  --color-secondary-sage: #87A878;
  --color-secondary-sage-hover: #77A868;
  --color-secondary-sage-subtle: #f0f9ff;
  
  /* ===== SEMANTIC COLORS ===== */
  /* Success states */
  --color-success: #4AD295;
  --color-success-hover: #059669;
  --color-success-subtle: #ecfdf5;
  --color-success-text: #065f46;
  
  /* Error states */
  --color-error: #E56B6F;
  --color-error-hover: #dc2626;
  --color-error-subtle: #fef2f2;
  --color-error-text: #991b1b;
  
  /* Warning states */
  --color-warning: #f59e0b;
  --color-warning-hover: #d97706;
  --color-warning-subtle: #fffbeb;
  --color-warning-text: #92400e;
  
  /* Info states */
  --color-info: #3b82f6;
  --color-info-hover: #2563eb;
  --color-info-subtle: #eff6ff;
  --color-info-text: #1d4ed8;
  
  /* ===== GLASS MORPHISM ===== */
  /* Glass effect colors with WCAG AA compliant contrast */
  --color-glass-surface: rgba(255, 255, 255, 0.95);
  --color-glass-surface-hover: rgba(255, 255, 255, 0.98);
  --color-glass-border: rgba(255, 255, 255, 0.4);
  --color-glass-border-hover: rgba(255, 255, 255, 0.6);
  --color-glass-text: #111827;
  --color-glass-text-inverse: #ffffff;
  --color-glass-text-muted: #374151;
  
  /* ===== EMOTIONAL INTELLIGENCE COLORS ===== */
  /* Specific to the emotional analysis features */
  --color-emotion-positive: #4AD295;
  --color-emotion-neutral: #A5B4FC;
  --color-emotion-negative: #E56B6F;
  
  /* Communication flags */
  --color-flag-toxic: #dc2626;
  --color-flag-manipulative: #f59e0b;
  --color-flag-healthy: #059669;
  
  /* ===== GRADIENTS ===== */
  /* Brand gradients */
  --gradient-primary: linear-gradient(135deg, #2A9D8F 0%, #0d9488 100%);
  --gradient-secondary: linear-gradient(135deg, #9D8DF1 0%, #E76F51 100%);
  --gradient-emotional: linear-gradient(135deg, #4AD295 0%, #A5B4FC 50%, #E56B6F 100%);
  --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Dark mode color tokens */
[data-theme="dark"], .dark {
  /* ===== SURFACE COLORS (DARK) ===== */
  --color-surface-primary: #0f172a;
  --color-surface-secondary: #1e293b;
  --color-surface-tertiary: #334155;
  --color-surface-elevated: #1e293b;
  --color-surface-overlay: rgba(0, 0, 0, 0.7);
  
  /* ===== TEXT COLORS (DARK) ===== */
  --color-text-primary: #f9fafb;
  --color-text-secondary: #d1d5db;
  --color-text-tertiary: #9ca3af;
  --color-text-disabled: #4b5563;
  --color-text-inverse: #111827;
  --color-text-link: #5eead4;
  --color-text-link-hover: #2dd4bf;
  
  /* ===== BORDER COLORS (DARK) ===== */
  --color-border-default: #334155;
  --color-border-medium: #475569;
  --color-border-strong: #64748b;
  --color-border-focus: #5eead4;
  
  /* ===== BRAND COLORS (DARK) ===== */
  --color-primary: #5eead4;
  --color-primary-hover: #2dd4bf;
  --color-primary-active: #14b8a6;
  --color-primary-disabled: #134e4a;
  --color-primary-subtle: #042f2e;
  
  /* ===== SEMANTIC COLORS (DARK) ===== */
  --color-success: #34d399;
  --color-success-hover: #10b981;
  --color-success-subtle: #064e3b;
  --color-success-text: #a7f3d0;
  
  --color-error: #f87171;
  --color-error-hover: #ef4444;
  --color-error-subtle: #7f1d1d;
  --color-error-text: #fca5a5;
  
  --color-warning: #fbbf24;
  --color-warning-hover: #f59e0b;
  --color-warning-subtle: #78350f;
  --color-warning-text: #fde68a;
  
  --color-info: #60a5fa;
  --color-info-hover: #3b82f6;
  --color-info-subtle: #1e3a8a;
  --color-info-text: #bfdbfe;
  
  /* ===== GLASS MORPHISM (DARK) ===== */
  --color-glass-surface: rgba(15, 23, 42, 0.95);
  --color-glass-surface-hover: rgba(15, 23, 42, 0.98);
  --color-glass-border: rgba(148, 163, 184, 0.4);
  --color-glass-border-hover: rgba(148, 163, 184, 0.6);
  --color-glass-text: #f9fafb;
  --color-glass-text-inverse: #0f172a;
  --color-glass-text-muted: #d1d5db;
  
  /* ===== GRADIENTS (DARK) ===== */
  --gradient-primary: linear-gradient(135deg, #5eead4 0%, #2dd4bf 100%);
  --gradient-secondary: linear-gradient(135deg, #c4b5fd 0%, #f87171 100%);
  --gradient-hero: linear-gradient(135deg, #4338ca 0%, #7c3aed 100%);
}

/* Utility classes for semantic colors */
.surface-primary { background-color: var(--color-surface-primary); }
.surface-secondary { background-color: var(--color-surface-secondary); }
.surface-elevated { background-color: var(--color-surface-elevated); }

.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-inverse { color: var(--color-text-inverse); }

.border-default { border-color: var(--color-border-default); }
.border-focus { border-color: var(--color-border-focus); }

.bg-primary { background-color: var(--color-primary); }
.bg-primary-hover:hover { background-color: var(--color-primary-hover); }
.text-primary-brand { color: var(--color-primary); }

.bg-success { background-color: var(--color-success); }
.bg-error { background-color: var(--color-error); }
.bg-warning { background-color: var(--color-warning); }
.bg-info { background-color: var(--color-info); }

.text-success { color: var(--color-success-text); }
.text-error { color: var(--color-error-text); }
.text-warning { color: var(--color-warning-text); }
.text-info { color: var(--color-info-text); }

.glass-surface {
  background-color: var(--color-glass-surface);
  border-color: var(--color-glass-border);
  color: var(--color-glass-text);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.glass-surface:hover {
  background-color: var(--color-glass-surface-hover);
  border-color: var(--color-glass-border-hover);
}

.glass-text-muted {
  color: var(--color-glass-text-muted);
}

/* ===== TYPOGRAPHY SYSTEM ===== */
/* Consistent heading hierarchy and text styles */

/* Heading Scale - Mobile First */
.heading-1 {
  font-size: 2rem;      /* 32px */
  line-height: 1.2;
  font-weight: 800;
  letter-spacing: -0.025em;
}

.heading-2 {
  font-size: 1.75rem;   /* 28px */
  line-height: 1.25;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.heading-3 {
  font-size: 1.5rem;    /* 24px */
  line-height: 1.3;
  font-weight: 600;
  letter-spacing: -0.015em;
}

.heading-4 {
  font-size: 1.25rem;   /* 20px */
  line-height: 1.35;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.heading-5 {
  font-size: 1.125rem;  /* 18px */
  line-height: 1.4;
  font-weight: 600;
}

.heading-6 {
  font-size: 1rem;      /* 16px */
  line-height: 1.4;
  font-weight: 600;
}

/* Body Text Styles */
.body-large {
  font-size: 1.125rem;  /* 18px */
  line-height: 1.6;
  font-weight: 400;
}

.body-base {
  font-size: 1rem;      /* 16px */
  line-height: 1.6;
  font-weight: 400;
}

.body-small {
  font-size: 0.875rem;  /* 14px */
  line-height: 1.5;
  font-weight: 400;
}

.body-xs {
  font-size: 0.75rem;   /* 12px */
  line-height: 1.4;
  font-weight: 400;
}

/* Responsive Typography - Tablet and Desktop */
@media (min-width: 768px) {
  .heading-1 {
    font-size: 2.5rem;   /* 40px */
  }

  .heading-2 {
    font-size: 2rem;     /* 32px */
  }

  .heading-3 {
    font-size: 1.75rem;  /* 28px */
  }

  .heading-4 {
    font-size: 1.5rem;   /* 24px */
  }
}

@media (min-width: 1024px) {
  .heading-1 {
    font-size: 3rem;     /* 48px */
  }

  .heading-2 {
    font-size: 2.25rem;  /* 36px */
  }

  .heading-3 {
    font-size: 2rem;     /* 32px */
  }

  .heading-4 {
    font-size: 1.75rem;  /* 28px */
  }
}

@media (min-width: 1280px) {
  .heading-1 {
    font-size: 3.5rem;   /* 56px */
  }

  .heading-2 {
    font-size: 2.75rem;  /* 44px */
  }

  .heading-3 {
    font-size: 2.25rem;  /* 36px */
  }
}

/* Special Typography Classes */
.text-hero {
  font-size: 2.5rem;
  line-height: 1.1;
  font-weight: 900;
  letter-spacing: -0.05em;
}

@media (min-width: 768px) {
  .text-hero {
    font-size: 4rem;
  }
}

@media (min-width: 1024px) {
  .text-hero {
    font-size: 5rem;
  }
}

@media (min-width: 1280px) {
  .text-hero {
    font-size: 6rem;
  }
}

.text-display {
  font-size: 2rem;
  line-height: 1.15;
  font-weight: 800;
  letter-spacing: -0.03em;
}

@media (min-width: 768px) {
  .text-display {
    font-size: 3rem;
  }
}

.text-lead {
  font-size: 1.25rem;
  line-height: 1.6;
  font-weight: 400;
}

@media (min-width: 768px) {
  .text-lead {
    font-size: 1.375rem;
  }
}

/* ===== SPACING SYSTEM ===== */
/* Consistent spacing patterns based on 8px grid */

:root {
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-2xl: 3rem;      /* 48px */
  --space-3xl: 4rem;      /* 64px */
  --space-4xl: 6rem;      /* 96px */
  --space-5xl: 8rem;      /* 128px */
}

/* Content Spacing Utilities */
.content-spacing > * + * {
  margin-top: var(--space-md);
}

.content-spacing-sm > * + * {
  margin-top: var(--space-sm);
}

.content-spacing-lg > * + * {
  margin-top: var(--space-lg);
}

/* Section Spacing */
.section-spacing {
  padding-top: var(--space-3xl);
  padding-bottom: var(--space-3xl);
}

.section-spacing-sm {
  padding-top: var(--space-2xl);
  padding-bottom: var(--space-2xl);
}

.section-spacing-lg {
  padding-top: var(--space-4xl);
  padding-bottom: var(--space-4xl);
}

/* Text Spacing Utilities */
.text-spacing-tight {
  line-height: 1.25;
}

.text-spacing-normal {
  line-height: 1.5;
}

.text-spacing-relaxed {
  line-height: 1.75;
}

.text-spacing-loose {
  line-height: 2;
}

/* Letter Spacing */
.tracking-tighter {
  letter-spacing: -0.05em;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.tracking-normal {
  letter-spacing: 0;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}
