/* Mobile Responsiveness & Touch Optimization Styles */

/* =============================================
   BREAKPOINT SYSTEM
   Mobile-first responsive design breakpoints
   ============================================= */

/* Default: Mobile (320px - 639px) */
/* sm: 640px and up */
/* md: 768px and up */ 
/* lg: 1024px and up */
/* xl: 1280px and up */
/* 2xl: 1536px and up */

/* =============================================
   TOUCH TARGET OPTIMIZATION
   Ensuring 48x48px minimum touch targets
   ============================================= */

@layer utilities {
  /* Touch target utilities */
  .touch-target-sm {
    min-width: 48px;
    min-height: 48px;
    @apply flex items-center justify-center;
  }

  .touch-target-md {
    min-width: 56px;
    min-height: 56px;
    @apply flex items-center justify-center;
  }

  .touch-target-lg {
    min-width: 64px;
    min-height: 64px;
    @apply flex items-center justify-center;
  }

  /* Touch-friendly spacing */
  .touch-spacing {
    @apply space-y-3 sm:space-y-2;
  }

  .touch-gap {
    gap: 12px;
  }

  @media (min-width: 640px) {
    .touch-gap {
      gap: 8px;
    }
  }
}

/* =============================================
   FLUID TYPOGRAPHY
   Responsive font sizing with clamp()
   ============================================= */

@layer base {
  /* Fluid heading scales */
  .fluid-h1 {
    font-size: clamp(2rem, 5vw + 1rem, 4.5rem);
    line-height: 1.1;
    letter-spacing: -0.04em;
  }

  .fluid-h2 {
    font-size: clamp(1.75rem, 4vw + 0.5rem, 3.5rem);
    line-height: 1.2;
    letter-spacing: -0.035em;
  }

  .fluid-h3 {
    font-size: clamp(1.5rem, 3vw + 0.25rem, 2.5rem);
    line-height: 1.3;
    letter-spacing: -0.03em;
  }

  .fluid-h4 {
    font-size: clamp(1.25rem, 2.5vw + 0.25rem, 2rem);
    line-height: 1.4;
    letter-spacing: -0.025em;
  }

  .fluid-h5 {
    font-size: clamp(1.125rem, 2vw + 0.25rem, 1.5rem);
    line-height: 1.5;
    letter-spacing: -0.02em;
  }

  .fluid-h6 {
    font-size: clamp(1rem, 1.5vw + 0.25rem, 1.25rem);
    line-height: 1.6;
    letter-spacing: -0.015em;
  }

  /* Fluid body text */
  .fluid-body {
    font-size: clamp(1rem, 1vw + 0.875rem, 1.125rem);
    line-height: 1.75;
  }

  .fluid-body-lg {
    font-size: clamp(1.125rem, 1.25vw + 1rem, 1.375rem);
    line-height: 1.875;
  }

  .fluid-body-sm {
    font-size: clamp(0.875rem, 0.75vw + 0.75rem, 1rem);
    line-height: 1.625;
  }

  /* Fluid display text */
  .fluid-display {
    font-size: clamp(3rem, 8vw + 1rem, 8rem);
    line-height: 0.95;
    letter-spacing: -0.055em;
  }

  .fluid-display-sm {
    font-size: clamp(2.5rem, 6vw + 0.5rem, 6rem);
    line-height: 1;
    letter-spacing: -0.05em;
  }
}

/* =============================================
   RESPONSIVE IMAGES WITH SRCSET
   Image optimization for different screen sizes
   ============================================= */

@layer components {
  /* Responsive image wrapper */
  .responsive-image-wrapper {
    @apply relative overflow-hidden;
    aspect-ratio: var(--aspect-ratio, 16 / 9);
  }

  .responsive-image {
    @apply w-full h-full object-cover;
  }

  /* Image loading states */
  .image-loading {
    @apply bg-gray-200 animate-pulse;
  }

  /* Art direction for different breakpoints */
  .hero-image-mobile {
    @apply block sm:hidden;
  }

  .hero-image-desktop {
    @apply hidden sm:block;
  }
}

/* =============================================
   SWIPE GESTURE SUPPORT
   Touch interactions and gestures
   ============================================= */

@layer utilities {
  /* Swipeable container */
  .swipeable {
    touch-action: pan-y;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-x: contain;
  }

  .swipeable-x {
    touch-action: pan-x;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
  }

  .swipeable-y {
    touch-action: pan-y;
    overflow-y: auto;
    scroll-snap-type: y mandatory;
    -webkit-overflow-scrolling: touch;
  }

  /* Snap points */
  .snap-start {
    scroll-snap-align: start;
  }

  .snap-center {
    scroll-snap-align: center;
  }

  .snap-end {
    scroll-snap-align: end;
  }

  /* Disable text selection during swipe */
  .no-select-swipe {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

/* =============================================
   MOBILE-OPTIMIZED COMPONENTS
   Component-specific mobile enhancements
   ============================================= */

/* Enhanced Button Touch Targets */
@layer components {
  .btn-touch-optimized {
    @apply inline-flex items-center justify-center;
    min-height: 48px;
    padding: 12px 24px;
  }

  .btn-touch-sm {
    min-height: 44px;
    padding: 10px 20px;
  }

  .btn-touch-lg {
    min-height: 56px;
    padding: 16px 32px;
  }

  /* Icon button touch targets */
  .icon-btn-touch {
    @apply flex items-center justify-center;
    min-width: 48px;
    min-height: 48px;
    padding: 12px;
  }

  /* Mobile-friendly button spacing */
  .btn-group-mobile {
    @apply flex flex-col sm:flex-row gap-3 sm:gap-2 w-full sm:w-auto;
  }

  .btn-group-mobile > * {
    @apply w-full sm:w-auto;
  }
}

/* Mobile Navigation */
@layer components {
  .mobile-nav-item {
    @apply flex items-center justify-between;
    min-height: 56px;
    padding: 16px 20px;
  }

  .mobile-nav-icon {
    @apply touch-target-sm;
  }

  /* Bottom navigation for mobile */
  .bottom-nav {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200;
    padding-bottom: env(safe-area-inset-bottom);
  }

  .bottom-nav-item {
    @apply flex-1 flex flex-col items-center justify-center;
    min-height: 56px;
    padding: 8px 4px;
  }
}

/* Mobile Form Elements */
@layer components {
  .input-touch-optimized {
    @apply w-full;
    min-height: 48px;
    padding: 12px 16px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .select-touch-optimized {
    @apply w-full;
    min-height: 48px;
    padding: 12px 40px 12px 16px;
    font-size: 16px;
  }

  .checkbox-touch-optimized {
    @apply flex items-center;
    min-height: 48px;
  }

  .checkbox-touch-optimized input[type="checkbox"],
  .radio-touch-optimized input[type="radio"] {
    width: 24px;
    height: 24px;
    margin-right: 12px;
  }

  /* Touch-friendly form spacing */
  .form-touch-spacing {
    @apply space-y-4 sm:space-y-3;
  }
}

/* Mobile Cards and List Items */
@layer components {
  .card-touch-optimized {
    @apply p-4 sm:p-6;
    min-height: 80px;
  }

  .list-item-touch {
    @apply flex items-center;
    min-height: 56px;
    padding: 12px 16px;
  }

  /* Swipeable card actions */
  .card-swipe-actions {
    @apply absolute inset-y-0 right-0 flex items-center;
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }

  .card-swipe-active .card-swipe-actions {
    transform: translateX(0);
  }
}

/* =============================================
   RESPONSIVE GRID & LAYOUT
   Mobile-first grid systems
   ============================================= */

@layer utilities {
  /* Mobile-optimized grid */
  .grid-mobile {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
  }

  .grid-mobile-dense {
    @apply grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4;
  }

  /* Stack on mobile, side-by-side on larger screens */
  .stack-mobile {
    @apply flex flex-col sm:flex-row gap-4 sm:gap-6;
  }

  /* Mobile padding adjustments */
  .px-mobile {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .py-mobile {
    @apply py-4 sm:py-6 lg:py-8;
  }

  .p-mobile {
    @apply p-4 sm:p-6 lg:p-8;
  }
}

/* =============================================
   MOBILE MODAL OPTIMIZATION
   Full-screen modals on mobile
   ============================================= */

@layer components {
  .modal-mobile-optimized {
    @apply fixed inset-0 z-50;
  }

  /* Full-screen modal on mobile */
  @media (max-width: 639px) {
    .modal-mobile-optimized .modal-content {
      @apply h-full w-full rounded-none;
      max-height: 100vh;
      max-width: 100vw;
      margin: 0;
    }

    .modal-mobile-optimized .modal-header {
      @apply sticky top-0 z-10 bg-white border-b;
      min-height: 56px;
    }

    .modal-mobile-optimized .modal-body {
      @apply overflow-y-auto;
      max-height: calc(100vh - 56px - env(safe-area-inset-bottom));
      -webkit-overflow-scrolling: touch;
    }

    .modal-mobile-optimized .modal-footer {
      @apply sticky bottom-0 bg-white border-t;
      padding-bottom: env(safe-area-inset-bottom);
    }
  }

  /* Swipe-to-dismiss handle */
  .modal-swipe-handle {
    @apply mx-auto bg-gray-300 rounded-full;
    width: 36px;
    height: 4px;
    margin-top: 8px;
    margin-bottom: 8px;
  }
}

/* =============================================
   PERFORMANCE OPTIMIZATIONS
   Mobile performance enhancements
   ============================================= */

@layer utilities {
  /* Optimize animations for mobile */
  @media (max-width: 639px) {
    .animate-mobile-optimized {
      animation-duration: 0.2s;
      animation-timing-function: ease-out;
    }

    /* Disable complex animations on low-end devices */
    @media (hover: none) and (pointer: coarse) {
      .animate-mobile-simple {
        animation: none !important;
        transition: opacity 0.2s ease, transform 0.2s ease !important;
      }
    }
  }

  /* Hardware acceleration for smooth scrolling */
  .scroll-optimized {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: scroll-position;
  }

  /* Reduce motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    .motion-safe {
      animation: none !important;
      transition: none !important;
    }
  }
}

/* =============================================
   SAFE AREAS (iPhone X and newer)
   Handle device safe areas
   ============================================= */

@layer utilities {
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  .safe-x {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  .safe-y {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }
}
