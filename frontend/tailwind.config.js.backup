/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html"
  ],
  theme: {
    extend: {
      colors: {
        // Enhanced Primary color with full scale
        primary: {
          50: '#f0fdfa',
          100: '#ccfbf1',
          200: '#99f6e4',
          300: '#5eead4',
          400: '#2dd4bf',
          500: '#2A9D8F', // Original primary
          600: '#0d9488',
          700: '#0f766e',
          800: '#115e59',
          900: '#134e4a',
        },

        // Enhanced Secondary colors with scales
        secondary: {
          coral: {
            50: '#fef2f2',
            100: '#fee2e2',
            200: '#fecaca',
            300: '#fca5a5',
            400: '#f87171',
            500: '#E76F51', // Original coral
            600: '#dc2626',
            700: '#b91c1c',
            800: '#991b1b',
            900: '#7f1d1d',
          },
          lavender: {
            50: '#f5f3ff',
            100: '#ede9fe',
            200: '#ddd6fe',
            300: '#c4b5fd',
            400: '#a78bfa',
            500: '#9D8DF1', // Original lavender
            600: '#8b5cf6',
            700: '#7c3aed',
            800: '#6d28d9',
            900: '#5b21b6',
          },
          sage: {
            50: '#f0fdf4',
            100: '#dcfce7',
            200: '#bbf7d0',
            300: '#86efac',
            400: '#4ade80',
            500: '#87A878', // Original sage
            600: '#16a34a',
            700: '#15803d',
            800: '#166534',
            900: '#14532d',
          },
        },

        // Enhanced Emotional state colors
        emotion: {
          positive: {
            50: '#f0fdf4',
            100: '#dcfce7',
            200: '#bbf7d0',
            300: '#86efac',
            400: '#4ade80',
            500: '#4AD295', // Original positive
            600: '#16a34a',
            700: '#15803d',
            800: '#166534',
            900: '#14532d',
          },
          neutral: {
            50: '#f8fafc',
            100: '#f1f5f9',
            200: '#e2e8f0',
            300: '#cbd5e1',
            400: '#94a3b8',
            500: '#A5B4FC', // Original neutral
            600: '#475569',
            700: '#334155',
            800: '#1e293b',
            900: '#0f172a',
          },
          negative: {
            50: '#fef2f2',
            100: '#fee2e2',
            200: '#fecaca',
            300: '#fca5a5',
            400: '#f87171',
            500: '#E56B6F', // Original negative
            600: '#dc2626',
            700: '#b91c1c',
            800: '#991b1b',
            900: '#7f1d1d',
          },
        },

        // Enhanced Text colors
        text: {
          primary: '#111827',    // Darker for better contrast
          secondary: '#6b7280',  // Improved secondary text
          tertiary: '#9ca3af',   // Light text for less important content
          light: '#FFFFFF',
          inverse: '#f9fafb',    // For dark backgrounds
        },

        // Enhanced Background colors
        background: {
          primary: '#FFFFFF',
          secondary: '#f9fafb',
          subtle: '#f3f4f6',
          muted: '#e5e7eb',
          dark: '#1f2937',
          darker: '#111827',
        },

        // Enhanced Border colors
        border: {
          light: '#f3f4f6',
          default: '#e5e7eb',
          medium: '#d1d5db',
          dark: '#9ca3af',
          darker: '#6b7280',
        },

        // Enhanced Glassmorphism colors
        glass: {
          white: 'rgba(255, 255, 255, 0.8)',
          light: 'rgba(255, 255, 255, 0.6)',
          medium: 'rgba(255, 255, 255, 0.4)',
          dark: 'rgba(255, 255, 255, 0.2)',
          primary: 'rgba(42, 157, 143, 0.2)',
          lavender: 'rgba(157, 141, 241, 0.2)',
          backdrop: 'rgba(0, 0, 0, 0.5)',
        },
      },

      // Enhanced shadow system
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'elevated': '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'floating': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'glow': '0 0 15px rgba(124, 58, 237, 0.3)',
        'glow-primary': '0 0 20px rgba(42, 157, 143, 0.4)',
        'glow-coral': '0 0 20px rgba(231, 111, 81, 0.4)',
      },

      // Animation keyframes
      animation: {
        'gradient-x': 'gradient-x 15s ease infinite',
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-soft': 'bounce-soft 2s infinite',
        'shimmer': 'shimmer 2.5s infinite',
        'subtle-glow': 'subtle-glow 4s ease-in-out infinite',
      },

      keyframes: {
        'gradient-x': {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center'
          },
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        'bounce-soft': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        'shimmer': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'subtle-glow': {
          '0%, 100%': { 
            filter: 'drop-shadow(0 0 20px rgba(251, 191, 36, 0.3)) drop-shadow(0 0 40px rgba(245, 158, 11, 0.1))'
          },
          '50%': { 
            filter: 'drop-shadow(0 0 30px rgba(251, 191, 36, 0.5)) drop-shadow(0 0 50px rgba(245, 158, 11, 0.3))'
          },
        },
      },

      // Enhanced backdrop blur utilities
      backdropBlur: {
        'xs': '2px',
        'sm': '4px',
        'md': '8px',
        'lg': '12px',
        'xl': '16px',
        '2xl': '24px',
        '3xl': '40px',
      },

      fontFamily: {
        // Primary font (for body text)
        sans: ['Orbitron', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        // Secondary font (for headings)
        serif: ['Orbitron', 'ui-serif', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
        // Special font for dyslexia (will be used in TY-05)
        dyslexic: ['Orbitron', 'OpenDyslexic', 'ui-sans-serif', 'system-ui', 'sans-serif'],
      },
      // Enhanced spacing system based on 8px base unit
      spacing: {
        'xs': '0.25rem',    // 4px
        'sm': '0.5rem',     // 8px - base unit
        'md': '1rem',       // 16px - 2x base
        'lg': '1.5rem',     // 24px - 3x base
        'xl': '2rem',       // 32px - 4x base
        '2xl': '3rem',      // 48px - 6x base
        '3xl': '4rem',      // 64px - 8x base
        '4xl': '6rem',      // 96px - 12x base
        '5xl': '8rem',      // 128px - 16x base
      },

      fontSize: {
        // Enhanced typographic scale with better hierarchy
        'xs': '0.75rem',      // 12px
        'sm': '0.875rem',     // 14px
        'base': '1rem',       // 16px - base size
        'lg': '1.125rem',     // 18px
        'xl': '1.25rem',      // 20px
        '2xl': '1.5rem',      // 24px
        '3xl': '1.875rem',    // 30px
        '4xl': '2.25rem',     // 36px
        '5xl': '3rem',        // 48px
        '6xl': '3.75rem',     // 60px
      },
      lineHeight: {
        'tight': '1.1',
        'snug': '1.3',
        'normal': '1.5',
        'relaxed': '1.625',
        'loose': '2',
      },
      letterSpacing: {
        'tighter': '-0.05em',
        'tight': '-0.025em',
        'normal': '0em',
        'wide': '0.025em',
        'wider': '0.05em',
        'widest': '0.1em',
      },
    },
  },
  plugins: [],
};
