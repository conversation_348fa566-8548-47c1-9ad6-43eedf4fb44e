# Screenshot Checklist for UI/UX Audit

## Required Screenshots for Visual Validation

### 1. Color & Contrast Testing
- [ ] **Glass morphism components** on different backgrounds:
  - Glass button on light background
  - Glass button on dark background
  - Glass button on image background
  - Glass card with text content
- [ ] **All button variants** side by side for color consistency
- [ ] **Error states** showing color and non-color indicators

### 2. Typography Testing
- [ ] **All heading levels** (h1-h6) on single page
- [ ] **Font toggle states**:
  - Standard font view
  - Dyslexic font view
  - Comparison of same content in both fonts
- [ ] **Long-form text** showing reading width constraints

### 3. Spacing & Layout Testing
- [ ] **Layout component** at these widths:
  - 320px (mobile small)
  - 375px (iPhone SE)
  - 768px (tablet)
  - 1024px (desktop)
  - 1440px (wide desktop)
- [ ] **Card components** showing all padding variants
- [ ] **Grid layouts** at different breakpoints

### 4. Accessibility Testing
- [ ] **Focus states** on:
  - All button variants
  - Form inputs
  - Links
  - Interactive cards
- [ ] **Keyboard navigation flow** (video or GIF)
- [ ] **Screen reader announcement** (video with audio)

### 5. Mobile Responsiveness
- [ ] **Hero section** on 320px width showing text overflow
- [ ] **Dashboard** on mobile devices:
  - 375px width
  - 390px width
  - With charts visible
- [ ] **Touch targets** with finger overlay simulation
- [ ] **Modal with keyboard** open on mobile

### 6. Motion & Animation
- [ ] **All animations** in action (video/GIF):
  - Floating elements in hero
  - Button hover states
  - Card hover effects
  - Loading spinners
- [ ] **Reduced motion mode** comparison

### 7. Page-Specific Screenshots

#### Home Page
- [ ] Full page at desktop (1440px)
- [ ] Full page at tablet (768px)
- [ ] Full page at mobile (375px)
- [ ] Hero section detail
- [ ] Feature cards section

#### Dashboard
- [ ] Full dashboard view
- [ ] Charts at different screen sizes
- [ ] Empty state
- [ ] Loading state
- [ ] Error state

#### Message Analyzer
- [ ] Input interface
- [ ] Analysis results
- [ ] Empty state
- [ ] Processing state

#### Relationship Hub
- [ ] List view
- [ ] Detail view
- [ ] Empty state

### 8. Component States
- [ ] **Button states**: default, hover, active, focus, disabled, loading
- [ ] **Card states**: default, hover, selected
- [ ] **Form states**: empty, filled, focused, error, success

### 9. Performance Testing
- [ ] Lighthouse report for each major page
- [ ] Network waterfall showing font loading
- [ ] Chrome DevTools Performance profile

### 10. Browser Testing
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Samsung Internet (mobile)

## Screenshot Naming Convention

Use this format for consistency:
`[page]-[component]-[state]-[viewport].png`

Examples:
- `home-hero-default-mobile.png`
- `dashboard-chart-loading-tablet.png`
- `button-primary-focus-desktop.png`

## Tools Recommended
- **Chrome DevTools** for responsive testing
- **Lighthouse** for performance metrics
- **axe DevTools** for accessibility testing
- **WAVE** for additional accessibility validation
- **Contrast Checker** for WCAG compliance
- **Screen readers**: NVDA (Windows), VoiceOver (Mac/iOS)
