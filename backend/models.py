"""
Pydantic models for API requests and responses.
Separated from SQLAlchemy database models to avoid conflicts.
"""

import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


# Request Models
class MessageInput(BaseModel):
    text: str
    context: Optional[str] = None
    relationship_id: Optional[str] = None


class RelationshipCreate(BaseModel):
    name: str
    type: str
    notes: Optional[str] = None


class RelationshipUpdate(BaseModel):
    name: Optional[str] = None
    type: Optional[str] = None
    notes: Optional[str] = None


class UserSettingsUpdate(BaseModel):
    theme: Optional[str] = None
    notifications_enabled: Optional[bool] = None
    privacy_mode: Optional[bool] = None
    language: Optional[str] = None


# Response Models
class Flag(BaseModel):
    type: str
    description: str


class AnalysisResultResponse(BaseModel):
    id: str
    text: str
    context: Optional[str] = None
    relationship_id: Optional[str] = None
    relationship_name: Optional[str] = None
    flags: List[Flag] = []
    interpretation: str
    suggestions: List[str] = []
    sentiment: str
    created_at: str

    class Config:
        from_attributes = True


class RelationshipResponse(BaseModel):
    id: str
    name: str
    type: str
    notes: Optional[str] = None
    health_score: int = 75
    last_contact: Optional[str] = None
    sentiment: str = "neutral"
    flag_history: List[Dict[str, Any]] = []
    created_at: str

    class Config:
        from_attributes = True


class UserSettingsResponse(BaseModel):
    theme: str = "light"
    notifications_enabled: bool = True
    privacy_mode: bool = False
    language: str = "en"
    updated_at: str

    class Config:
        from_attributes = True


class GrowthActivity(BaseModel):
    day: int
    title: str
    activity_type: str
    content: str


class WeeklyPlan(BaseModel):
    theme: str
    days: List[GrowthActivity]


class GrowthPlanResponse(BaseModel):
    id: str
    user_id: str
    current_week: WeeklyPlan
    goals: List[str]
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class DashboardResponse(BaseModel):
    health_score: int
    total_analyses: int
    total_flags_detected: int
    flag_counts: Dict[str, int]
    sentiment_timeline: List[Dict[str, Any]]


class ReportResponse(BaseModel):
    report_type: str
    data: Dict[str, Any]
    generated_at: str


# Utility functions to convert between database models and response models
def db_analysis_to_response(db_analysis) -> AnalysisResultResponse:
    """Convert database AnalysisResult to response model"""
    return AnalysisResultResponse(
        id=db_analysis.id,
        text=db_analysis.text,
        context=db_analysis.context,
        relationship_id=db_analysis.relationship_id,
        relationship_name=db_analysis.relationship.name if db_analysis.relationship else None,
        flags=db_analysis.flags or [],
        interpretation=db_analysis.interpretation,
        suggestions=db_analysis.suggestions or [],
        sentiment=db_analysis.sentiment,
        created_at=db_analysis.created_at.isoformat()
    )


def db_relationship_to_response(db_relationship) -> RelationshipResponse:
    """Convert database Relationship to response model"""
    return RelationshipResponse(
        id=db_relationship.id,
        name=db_relationship.name,
        type=db_relationship.type,
        notes=db_relationship.notes,
        health_score=db_relationship.health_score,
        last_contact=db_relationship.last_contact.isoformat() if db_relationship.last_contact else None,
        sentiment=db_relationship.sentiment,
        flag_history=db_relationship.flag_history or [],
        created_at=db_relationship.created_at.isoformat()
    )


def db_settings_to_response(db_settings) -> UserSettingsResponse:
    """Convert database UserSettings to response model"""
    return UserSettingsResponse(
        theme=db_settings.theme,
        notifications_enabled=db_settings.notifications_enabled,
        privacy_mode=db_settings.privacy_mode,
        language=db_settings.language,
        updated_at=db_settings.updated_at.isoformat()
    )


class GrowthPlanCreate(BaseModel):
    goals: List[str]
    current_week: WeeklyPlan


# Legacy models for backward compatibility (these were in the original server.py)
class Relationship(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    type: str
    notes: Optional[str] = None
    health_score: int = 75
    last_contact: str = Field(default_factory=lambda: datetime.now().isoformat())
    sentiment: str = "neutral"
    flag_history: List[Dict[str, Any]] = []
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())


class AnalysisResult(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    text: str
    context: Optional[str] = None
    relationship_id: Optional[str] = None
    relationship_name: Optional[str] = None
    flags: List[Flag] = []
    interpretation: str
    suggestions: List[str] = []
    sentiment: str
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())


class GrowthPlan(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = None
    current_week: WeeklyPlan
    goals: List[str]
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())
