"""
Database models and connection handling using SQLAlchemy
"""
import os
from datetime import datetime
from typing import List, Optional, Iterator
from sqlalchemy import create_engine, Column, String, Integer, DateTime, Text, JSON, <PERSON>olean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./aei_app.db")

# Create engine
engine = create_engine(
    DATABASE_URL,
    echo=False,  # Set to True for SQL query logging
    connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {}
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for all models
Base = declarative_base()


class User(Base):
    """User model with authentication and profile data"""
    __tablename__ = "users"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String, unique=True, index=True, nullable=False)
    name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    preferences = Column(JSON, default=dict)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    relationships = relationship("Relationship", back_populates="user", cascade="all, delete-orphan")
    analysis_results = relationship("AnalysisResult", back_populates="user", cascade="all, delete-orphan")
    growth_plans = relationship("GrowthPlan", back_populates="user", cascade="all, delete-orphan")


class Relationship(Base):
    """Relationship tracking model"""
    __tablename__ = "relationships"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)
    notes = Column(Text)
    health_score = Column(Integer, default=75)
    last_contact = Column(DateTime, default=datetime.utcnow)
    sentiment = Column(String, default="neutral")
    flag_history = Column(JSON, default=list)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="relationships")
    analysis_results = relationship("AnalysisResult", back_populates="relationship", cascade="all, delete-orphan")


class AnalysisResult(Base):
    """Message analysis results"""
    __tablename__ = "analysis_results"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    relationship_id = Column(String, ForeignKey("relationships.id"), nullable=True)
    text = Column(Text, nullable=False)
    context = Column(Text)
    flags = Column(JSON, default=list)
    interpretation = Column(Text)
    suggestions = Column(JSON, default=list)
    sentiment = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="analysis_results")
    relationship = relationship("Relationship", back_populates="analysis_results")


class GrowthPlan(Base):
    """Personal growth plans"""
    __tablename__ = "growth_plans"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    current_week = Column(JSON)
    goals = Column(JSON, default=list)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="growth_plans")


class UserSession(Base):
    """User session tracking for authentication"""
    __tablename__ = "user_sessions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    token = Column(String, unique=True, nullable=False, index=True)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)


class UserSettings(Base):
    """User settings and preferences"""
    __tablename__ = "user_settings"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), unique=True, nullable=False)
    theme = Column(String, default="light")
    notifications_enabled = Column(Boolean, default=True)
    privacy_mode = Column(Boolean, default=False)
    language = Column(String, default="en")
    analysis_settings = Column(JSON, default=dict)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# Database dependency
def get_db() -> Iterator[Session]:
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Initialize database
def init_database():
    """Create all tables"""
    Base.metadata.create_all(bind=engine)


# Database utility functions
def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """Get user by email"""
    return db.query(User).filter(User.email == email).first()


def get_user_by_id(db: Session, user_id: str) -> Optional[User]:
    """Get user by ID"""
    return db.query(User).filter(User.id == user_id).first()


def create_user(db: Session, email: str, name: str, hashed_password: str) -> User:
    """Create new user"""
    user = User(
        email=email,
        name=name,
        hashed_password=hashed_password
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


def get_user_relationships(db: Session, user_id: str) -> List[Relationship]:
    """Get all relationships for a user"""
    return db.query(Relationship).filter(Relationship.user_id == user_id).all()


def get_user_analysis_results(db: Session, user_id: str, limit: int = 100) -> List[AnalysisResult]:
    """Get analysis results for a user"""
    return db.query(AnalysisResult).filter(AnalysisResult.user_id == user_id).order_by(AnalysisResult.created_at.desc()).limit(limit).all()


def create_user_session(db: Session, user_id: str, token: str, expires_at: datetime) -> UserSession:
    """Create new user session"""
    session = UserSession(
        user_id=user_id,
        token=token,
        expires_at=expires_at
    )
    db.add(session)
    db.commit()
    db.refresh(session)
    return session


def get_session_by_token(db: Session, token: str) -> Optional[UserSession]:
    """Get session by token"""
    return db.query(UserSession).filter(
        UserSession.token == token,
        UserSession.is_active == True,
        UserSession.expires_at > datetime.utcnow()
    ).first()


def invalidate_session(db: Session, token: str):
    """Invalidate a session"""
    session = db.query(UserSession).filter(UserSession.token == token).first()
    if session:
        # Use SQLAlchemy update method for proper column assignment
        db.query(UserSession).filter(UserSession.token == token).update({"is_active": False})
        db.commit()


def get_or_create_user_settings(db: Session, user_id: str) -> UserSettings:
    """Get or create user settings"""
    settings = db.query(UserSettings).filter(UserSettings.user_id == user_id).first()
    if not settings:
        settings = UserSettings(user_id=user_id)
        db.add(settings)
        db.commit()
        db.refresh(settings)
    return settings
