"""
Auth0 token verification for FastAPI
"""
import os
import json
import requests
from functools import wraps
from typing import Dict, Optional
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt, JWTError
from jose.exceptions import ExpiredSignatureError, JWTClaimsError

# Auth0 Configuration
AUTH0_DOMAIN = os.getenv("AUTH0_DOMAIN", "my-aei-dev.us.auth0.com")
AUTH0_AUDIENCE = os.getenv("AUTH0_AUDIENCE", "https://my-aei-dev.us.auth0.com/api/v2/")
ALGORITHMS = ["RS256"]

# Security scheme
security = HTTPBearer()

class AuthError(Exception):
    """Auth error exception"""
    def __init__(self, error: Dict[str, str], status_code: int):
        self.error = error
        self.status_code = status_code


def get_rsa_key(token: str) -> Dict:
    """Get RSA key from Auth0 JWKS endpoint"""
    jsonurl = requests.get(f"https://{AUTH0_DOMAIN}/.well-known/jwks.json")
    jwks = jsonurl.json()
    
    try:
        unverified_header = jwt.get_unverified_header(token)
    except JWTError:
        raise AuthError({
            "code": "invalid_header",
            "description": "Invalid header. Use an RS256 signed JWT Access Token"
        }, 401)
    
    if unverified_header["alg"] == "HS256":
        raise AuthError({
            "code": "invalid_header",
            "description": "Invalid header. Use an RS256 signed JWT Access Token"
        }, 401)
    
    rsa_key = {}
    for key in jwks["keys"]:
        if key["kid"] == unverified_header["kid"]:
            rsa_key = {
                "kty": key["kty"],
                "kid": key["kid"],
                "use": key["use"],
                "n": key["n"],
                "e": key["e"]
            }
            break
    
    if not rsa_key:
        raise AuthError({
            "code": "invalid_header",
            "description": "Unable to find appropriate key"
        }, 401)
    
    return rsa_key


def verify_decode_jwt(token: str) -> Dict:
    """Verify and decode Auth0 JWT token"""
    try:
        rsa_key = get_rsa_key(token)
        
        payload = jwt.decode(
            token,
            rsa_key,
            algorithms=ALGORITHMS,
            audience=AUTH0_AUDIENCE,
            issuer=f"https://{AUTH0_DOMAIN}/"
        )
        
        return payload
        
    except ExpiredSignatureError:
        raise AuthError({
            "code": "token_expired",
            "description": "Token is expired"
        }, 401)
    except JWTClaimsError:
        raise AuthError({
            "code": "invalid_claims",
            "description": "Incorrect claims, please check the audience and issuer"
        }, 401)
    except Exception:
        raise AuthError({
            "code": "invalid_header",
            "description": "Unable to parse authentication token"
        }, 401)


def get_current_user_auth0(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current user from Auth0 token"""
    try:
        token = credentials.credentials
        print(f"[AUTH0_DEBUG] Token received: {token[:20]}...")
        
        payload = verify_decode_jwt(token)
        print(f"[AUTH0_DEBUG] Token verified. Sub: {payload.get('sub')}, Email: {payload.get('email')}")
        
        # Create a user-like object with Auth0 data
        user = {
            'id': payload.get('sub'),
            'email': payload.get('email'),
            'name': payload.get('name', payload.get('email', 'Unknown')),
            'is_active': True
        }
        
        return user
        
    except AuthError as e:
        print(f"[AUTH0_DEBUG] Auth error: {e.error}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.error['description']
        )
    except Exception as e:
        print(f"[AUTH0_DEBUG] Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
