"""
Authentication routes and endpoints
"""
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr

from .security import (
    hash_password, verify_password, create_access_token, verify_token,
    generate_session_token, validate_password_strength, sanitize_input,
    User<PERSON><PERSON>, UserRegister, TokenData
)
from database import (
    get_db, get_user_by_email, get_user_by_id, create_user,
    create_user_session, get_session_by_token, invalidate_session
)

router = APIRouter(prefix="/api/auth", tags=["authentication"])
security = HTTPBearer()


class UserResponse(BaseModel):
    id: str
    email: str
    name: str
    is_active: bool
    created_at: datetime


class TokenResponse(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user: UserResponse


class PasswordReset(BaseModel):
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    email: EmailStr
    reset_code: str
    new_password: str


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Get current authenticated user"""
    print(f"[AUTH_DEBUG] get_current_user called. Path: {{request.url.path if request else 'Unknown'}}")
    if not credentials:
        print("[AUTH_DEBUG] No credentials found.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token = credentials.credentials
    print(f"[AUTH_DEBUG] Token received: {{token[:20]}}...") # Log only a part of the token
    
    try:
        token_data = verify_token(token)
        print(f"[AUTH_DEBUG] Token verified. User ID: {{token_data.user_id}}, Email: {{token_data.email}}")
    except HTTPException as e:
        print(f"[AUTH_DEBUG] Token verification failed: {{e.detail}}")
        raise e # Re-raise the exception from verify_token
    
    if not token_data.user_id:
        print("[AUTH_DEBUG] Invalid token data: user_id is missing.")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token data",
        )
    
    print(f"[AUTH_DEBUG] Attempting to fetch user by ID: {{token_data.user_id}}")
    user = get_user_by_id(db, token_data.user_id)
    
    if user is None:
        print(f"[AUTH_DEBUG] User not found for ID: {{token_data.user_id}}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
        )
    print(f"[AUTH_DEBUG] User found: {{user.email}}, Active: {{getattr(user, 'is_active', 'N/A')}}")
    
    if not getattr(user, 'is_active', False):
        print(f"[AUTH_DEBUG] User account is disabled: {{user.email}}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is disabled",
        )
    
    print(f"[AUTH_DEBUG] User {{user.email}} authenticated successfully.")
    return user


@router.post("/register", response_model=TokenResponse)
async def register(user_data: UserRegister, db: Session = Depends(get_db)):
    """Register a new user"""
    # Sanitize inputs
    user_data.email = sanitize_input(user_data.email).lower()
    user_data.name = sanitize_input(user_data.name)
    
    # Validate password match
    if user_data.password != user_data.confirm_password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Passwords do not match"
        )
    
    # Validate password strength
    if not validate_password_strength(user_data.password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must be at least 8 characters long and contain uppercase, lowercase, and numbers"
        )
    
    # Check if user already exists
    existing_user = get_user_by_email(db, user_data.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Hash password
    hashed_password = hash_password(user_data.password)
    
    # Create user
    user = create_user(db, user_data.email, user_data.name, hashed_password)
    
    # Create access token
    access_token_expires = timedelta(minutes=1440)  # 24 hours
    access_token = create_access_token(
        data={"sub": str(user.id), "email": str(user.email)},
        expires_delta=access_token_expires
    )
    
    # Create session
    session_token = generate_session_token()
    expires_at = datetime.utcnow() + access_token_expires
    create_user_session(db, str(user.id), session_token, expires_at)
    
    return TokenResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=86400,  # 24 hours in seconds
        user=UserResponse(
            id=str(user.id),
            email=str(user.email),
            name=str(user.name),
            is_active=bool(getattr(user, 'is_active', False)),
            created_at=getattr(user, 'created_at')
        )
    )


@router.post("/login", response_model=TokenResponse)
async def login(user_auth: UserAuth, db: Session = Depends(get_db)):
    """Authenticate user and return access token"""
    # Sanitize email
    user_auth.email = sanitize_input(user_auth.email).lower()
    
    # Get user
    user = get_user_by_email(db, user_auth.email)
    if not user or not verify_password(user_auth.password, str(user.hashed_password)):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
        )
    
    if not getattr(user, 'is_active', False):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is disabled",
        )
    
    # Create access token
    access_token_expires = timedelta(minutes=1440)  # 24 hours
    access_token = create_access_token(
        data={"sub": str(user.id), "email": str(user.email)},
        expires_delta=access_token_expires
    )
    
    # Create session
    session_token = generate_session_token()
    expires_at = datetime.utcnow() + access_token_expires
    create_user_session(db, str(user.id), session_token, expires_at)
    
    return TokenResponse(
        access_token=access_token,
        token_type="bearer",
        expires_in=86400,  # 24 hours in seconds
        user=UserResponse(
            id=str(user.id),
            email=str(user.email),
            name=str(user.name),
            is_active=bool(getattr(user, 'is_active', False)),
            created_at=getattr(user, 'created_at')
        )
    )


@router.post("/logout")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Logout user and invalidate session"""
    token = credentials.credentials
    invalidate_session(db, token)
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user = Depends(get_current_user)
):
    """Get current user information"""
    return UserResponse(
        id=str(current_user.id),
        email=str(current_user.email),
        name=str(current_user.name),
        is_active=bool(getattr(current_user, 'is_active', False)),
        created_at=getattr(current_user, 'created_at')
    )


@router.post("/refresh")
async def refresh_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """Refresh access token"""
    token = credentials.credentials
    
    # Verify current token
    token_data = verify_token(token)
    
    # Check if user_id is present
    if not token_data.user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token data",
        )
    
    user = get_user_by_id(db, token_data.user_id)
    
    if not user or not getattr(user, 'is_active', False):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
        )
    
    # Create new access token
    access_token_expires = timedelta(minutes=1440)  # 24 hours
    new_access_token = create_access_token(
        data={"sub": str(user.id), "email": str(user.email)},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": new_access_token,
        "token_type": "bearer",
        "expires_in": 86400
    }


@router.post("/password-reset")
async def request_password_reset(
    reset_data: PasswordReset,
    db: Session = Depends(get_db)
):
    """Request password reset (placeholder - would send email in production)"""
    # Sanitize email
    email = sanitize_input(reset_data.email).lower()
    
    # Check if user exists
    user = get_user_by_email(db, email)
    if not user:
        # Don't reveal if email exists or not for security
        return {"message": "If the email exists, a reset link has been sent"}
    
    # In production, you would:
    # 1. Generate a secure reset token
    # 2. Store it in database with expiration
    # 3. Send email with reset link
    
    return {"message": "Password reset functionality is not yet implemented in this demo"}


@router.post("/password-reset/confirm")
async def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    db: Session = Depends(get_db)
):
    """Confirm password reset (placeholder)"""
    return {"message": "Password reset confirmation is not yet implemented in this demo"}


@router.get("/health")
async def auth_health():
    """Authentication service health check"""
    return {"status": "healthy", "service": "authentication"}
