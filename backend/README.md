# My AEI Backend

This is the backend part of the My AEI application. For complete instructions on setting up and running the full application (frontend and backend), please refer to the [main README.md](../README.md) file in the project root.

## Quick Start

```bash
# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Start the server
uvicorn server:app --host 0.0.0.0 --port 8001
```

The backend API will be available at [http://localhost:8001](http://localhost:8001).

## API Documentation

Once the server is running, you can access the API documentation at:
- Swagger UI: [http://localhost:8001/docs](http://localhost:8001/docs)
- ReDoc: [http://localhost:8001/redoc](http://localhost:8001/redoc)

## Development

The main application file is `server.py`. This is where the FastAPI application is defined and configured.

## Testing

To run the backend tests:

```bash
python backend_test.py
```