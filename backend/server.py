import os
import uuid
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from fastapi import FastAP<PERSON>, HTTPException, Request, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
import groq

# Database imports
from database import (
    init_database, get_db, User, 
    Relationship as DBRelationship, 
    AnalysisResult as DBAnalysisResult, 
    GrowthPlan as DBGrowthPlan, 
    UserSettings as DBUserSettings,
    get_or_create_user_settings
)

# Authentication imports  
from auth.routes import router as auth_router
from auth.auth0_verification import get_current_user_auth0
from auth.security import sanitize_input

# Services
from anomaly_detection import AnomalyDetector
from reports import ReportGenerator

# API Models imports
from models import (
    MessageInput, RelationshipCreate, RelationshipUpdate, UserSettingsUpdate,
    AnalysisResultResponse, RelationshipResponse, UserSettingsResponse, 
    GrowthPlanResponse, DashboardResponse, ReportResponse,
    db_analysis_to_response, db_relationship_to_response, db_settings_to_response,
    # Legacy models for backward compatibility
    Relationship, AnalysisResult, GrowthPlan, GrowthActivity, WeeklyPlan
)

# Initialize FastAPI app
app = FastAPI(title="My ÆI API", description="Emotional Intelligence and Relationship Analysis API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include authentication routes
app.include_router(auth_router)

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    init_database()

# Legacy in-memory storage for backward compatibility during transition
in_memory_db = {
    "analysis_results": [],
    "relationships": [],
    "growth_plans": []
}

# Routes
@app.get("/api/health")
async def health():
    return {"status": "healthy", "version": "1.0.0"}

@app.post("/api/analyze", response_model=AnalysisResultResponse)
async def analyze_message(
    message_input: MessageInput, 
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_auth0)
):
    # Sanitize input
    message_input.text = sanitize_input(message_input.text)
    if message_input.context:
        message_input.context = sanitize_input(message_input.context)
    
    # Initialize Groq client
    try:
        groq_api_key = os.environ.get("GROQ_API_KEY", "********************************************************")
        client = groq.Groq(api_key=groq_api_key)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to initialize Groq client: {str(e)}")
    
    # Verify relationship exists if provided
    relationship = None
    if message_input.relationship_id:
        relationship = db.query(DBRelationship).filter(
            DBRelationship.id == message_input.relationship_id,
            DBRelationship.user_id == current_user['id']
        ).first()
        if not relationship:
            raise HTTPException(status_code=404, detail="Relationship not found")
    
    # Analyze the message with Groq
    try:
        # Create a comprehensive analysis prompt
        context_info = f" in the context of {message_input.context}" if message_input.context else ""
        relationship_info = f" regarding your relationship with {relationship.name}" if relationship else ""
        
        prompt = f"""
        Please analyze the following message{context_info}{relationship_info} for emotional intelligence insights:
        
        Message: "{message_input.text}"
        
        Provide analysis in the following JSON format:
        {{
            "interpretation": "A thoughtful interpretation of the emotional content and communication patterns",
            "flags": [
                {{"type": "flag_category", "description": "specific concern or pattern identified"}}
            ],
            "suggestions": [
                "actionable suggestion 1",
                "actionable suggestion 2"
            ],
            "sentiment": "positive, neutral, or negative"
        }}
        
        Focus on:
        1. Emotional patterns and communication style
        2. Potential red flags or concerns
        3. Suggestions for healthier communication
        4. Overall sentiment and emotional tone
        
        Be constructive and supportive in your analysis.
        """
        
        completion = client.chat.completions.create(
            messages=[
                {"role": "system", "content": "You are an expert emotional intelligence coach specializing in relationship communication analysis. Provide thoughtful, constructive insights."},
                {"role": "user", "content": prompt}
            ],
            model="llama3-70b-8192",
            temperature=0.3,
            max_tokens=1500
        )
        
        response_text = completion.choices[0].message.content
        
        # Handle potential None response
        if response_text is None:
            response_text = "Analysis could not be completed"
        
        # Try to parse JSON response
        try:
            # Extract JSON from response if it contains other text
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start != -1 and json_end != -1:
                json_text = response_text[json_start:json_end]
                analysis_data = json.loads(json_text)
            else:
                raise ValueError("No JSON found in response")
        except (json.JSONDecodeError, ValueError):
            # Fallback if JSON parsing fails
            analysis_data = {
                "interpretation": response_text,
                "flags": [],
                "suggestions": ["Consider the emotional context of your communication"],
                "sentiment": "neutral"
            }
        
        # Generate unique ID for this analysis
        analysis_id = str(uuid.uuid4())
        
        # Save to database
        db_analysis = DBAnalysisResult(
            id=analysis_id,
            user_id=current_user['id'],
            relationship_id=message_input.relationship_id,
            text=message_input.text,
            context=message_input.context,
            flags=analysis_data.get("flags", []),
            interpretation=analysis_data.get("interpretation", "Analysis completed"),
            suggestions=analysis_data.get("suggestions", []),
            sentiment=analysis_data.get("sentiment", "neutral")
        )
        
        db.add(db_analysis)
        db.commit()
        db.refresh(db_analysis)
        
        # Convert to response model
        return db_analysis_to_response(db_analysis)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to analyze message: {str(e)}")

@app.get("/api/history")
async def get_history():
    try:
        # Sort by created_at in descending order
        results = sorted(in_memory_db["analysis_results"], key=lambda x: x["created_at"], reverse=True)
        return results[:50]  # Return at most 50 results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve history: {str(e)}")

@app.get("/api/dashboard")
async def get_dashboard():
    try:
        # Get analysis history
        analysis_results = in_memory_db["analysis_results"]
        
        # Calculate overall health score based on flag frequency and sentiment
        total_analyses = len(analysis_results)
        if total_analyses == 0:
            return {
                "health_score": 75,  # Default score
                "total_analyses": 0,
                "total_flags_detected": 0,
                "flag_counts": {},
                "sentiment_timeline": []
            }
        
        # Count flags
        total_flags = 0
        flag_counts = {}
        for result in analysis_results:
            flags = result.get("flags", [])
            total_flags += len(flags)
            
            for flag in flags:
                flag_type = flag.get("type", "Unknown")
                flag_counts[flag_type] = flag_counts.get(flag_type, 0) + 1
        
        # Calculate health score (simple algorithm for demo)
        flag_ratio = total_flags / total_analyses
        health_score = max(0, min(100, int(100 - (flag_ratio * 100))))
        
        # Build sentiment timeline
        sentiment_timeline = []
        dates_seen = set()
        for result in analysis_results:
            date = result.get("created_at", "").split("T")[0]
            if date and date not in dates_seen:
                dates_seen.add(date)
                sentiment_timeline.append([date, result.get("sentiment", "neutral")])
        
        # Sort chronologically
        sentiment_timeline.sort(key=lambda x: x[0])
        
        return {
            "health_score": health_score,
            "total_analyses": total_analyses,
            "total_flags_detected": total_flags,
            "flag_counts": flag_counts,
            "sentiment_timeline": sentiment_timeline
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve dashboard data: {str(e)}")

@app.get("/api/relationships")
async def get_relationships(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_auth0)
):
    """Get all relationships for the authenticated user"""
    try:
        relationships = db.query(DBRelationship).filter(DBRelationship.user_id == current_user['id']).all()
        return [
            {
                "id": rel.id,
                "name": rel.name,
                "type": rel.type,
                "notes": rel.notes,
                "health_score": rel.health_score,
                "last_contact": rel.last_contact.isoformat() if getattr(rel, 'last_contact', None) else None,
                "sentiment": rel.sentiment,
                "flag_history": rel.flag_history,
                "created_at": rel.created_at.isoformat(),
                "updated_at": rel.updated_at.isoformat()
            }
            for rel in relationships
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve relationships: {str(e)}")

@app.post("/api/relationships", response_model=RelationshipResponse)
async def create_relationship(
    relationship_data: RelationshipCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_auth0)
):
    """Create a new relationship"""
    try:
        # Sanitize inputs
        name = sanitize_input(relationship_data.name)
        relationship_type = sanitize_input(relationship_data.type)
        notes = sanitize_input(relationship_data.notes) if relationship_data.notes else None
        
        if not name or not relationship_type:
            raise HTTPException(status_code=400, detail="Name and type are required")
        
        # Create relationship in database
        user_id = str(current_user['id'])  # Ensure it's a string
        new_relationship = DBRelationship(
            id=str(uuid.uuid4()),
            user_id=user_id,
            name=name,
            type=relationship_type,
            notes=notes,
            health_score=75,
            sentiment="neutral",
            flag_history=[]
        )
        
        db.add(new_relationship)
        db.commit()
        db.refresh(new_relationship)
        
        return db_relationship_to_response(new_relationship)
        
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create relationship: {str(e)}")

@app.get("/api/relationships/{relationship_id}")
async def get_relationship(relationship_id: str):
    try:
        for relationship in in_memory_db["relationships"]:
            if relationship["id"] == relationship_id:
                return relationship
        raise HTTPException(status_code=404, detail="Relationship not found")
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve relationship: {str(e)}")

@app.post("/api/relationships/{relationship_id}/analyze", response_model=AnalysisResult)
async def analyze_relationship_message(relationship_id: str, message_input: MessageInput):
    try:
        # Verify relationship exists
        relationship_exists = False
        for relationship in in_memory_db["relationships"]:
            if relationship["id"] == relationship_id:
                relationship_exists = True
                break
        
        if not relationship_exists:
            raise HTTPException(status_code=404, detail="Relationship not found")
        
        # Add relationship_id to the message input if not already present
        if not message_input.relationship_id:
            message_input.relationship_id = relationship_id
        
        # Call the analyze endpoint
        result = await analyze_message(message_input)
        
        return result
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to analyze message: {str(e)}")

@app.get("/api/relationships/{relationship_id}/history")
async def get_relationship_history(relationship_id: str):
    try:
        # Get all analyses for this relationship
        analyses = [
            analysis for analysis in in_memory_db["analysis_results"]
            if analysis.get("relationship_id") == relationship_id
        ]
        
        # Sort by created_at in descending order
        analyses = sorted(analyses, key=lambda x: x["created_at"], reverse=True)
        
        # Get the relationship
        relationship = None
        for rel in in_memory_db["relationships"]:
            if rel["id"] == relationship_id:
                relationship = rel
                break
        
        if not relationship:
            raise HTTPException(status_code=404, detail="Relationship not found")
        
        # Create trend data
        health_trend = []
        sentiment_counts = {"positive": 0, "neutral": 0, "negative": 0}
        flag_types = {}
        
        # Group analyses by date
        analyses_by_date = {}
        for analysis in analyses:
            date = analysis.get("created_at", "").split("T")[0]
            if date not in analyses_by_date:
                analyses_by_date[date] = []
            analyses_by_date[date].append(analysis)
            
            # Count sentiments
            sentiment = analysis.get("sentiment", "neutral")
            sentiment_counts[sentiment] = sentiment_counts.get(sentiment, 0) + 1
            
            # Count flag types
            for flag in analysis.get("flags", []):
                flag_type = flag.get("type", "Unknown")
                flag_types[flag_type] = flag_types.get(flag_type, 0) + 1
        
        # Create daily health scores (simulated for demo)
        dates = sorted(analyses_by_date.keys())
        for date in dates:
            day_analyses = analyses_by_date[date]
            # Simple algorithm: start with base score and reduce for each flag
            day_score = 75
            total_flags = sum(len(a.get("flags", [])) for a in day_analyses)
            day_score = max(0, min(100, day_score - (total_flags * 5)))
            
            health_trend.append({
                "date": date,
                "score": day_score
            })
        
        return {
            "relationship": relationship,
            "analyses": analyses,
            "health_trend": health_trend,
            "sentiment_counts": sentiment_counts,
            "flag_types": flag_types
        }
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve relationship history: {str(e)}")

@app.put("/api/relationships/{relationship_id}", response_model=Relationship)
async def update_relationship(relationship_id: str, relationship_update: dict):
    try:
        # Ensure id isn't changed
        if "id" in relationship_update and relationship_update["id"] != relationship_id:
            raise HTTPException(status_code=400, detail="Cannot change relationship ID")
        
        # Make sure the relationship exists
        relationship_index = None
        for i, relationship in enumerate(in_memory_db["relationships"]):
            if relationship["id"] == relationship_id:
                relationship_index = i
                break
        
        if relationship_index is None:
            raise HTTPException(status_code=404, detail="Relationship not found")
        
        # Update the relationship
        in_memory_db["relationships"][relationship_index].update({
            **relationship_update,
            "updated_at": datetime.now().isoformat()
        })
        
        # Return the updated relationship
        return in_memory_db["relationships"][relationship_index]
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update relationship: {str(e)}")

@app.delete("/api/relationships/{relationship_id}")
async def delete_relationship(relationship_id: str):
    try:
        # Make sure the relationship exists
        relationship_index = None
        for i, relationship in enumerate(in_memory_db["relationships"]):
            if relationship["id"] == relationship_id:
                relationship_index = i
                break
        
        if relationship_index is None:
            raise HTTPException(status_code=404, detail="Relationship not found")
        
        # Delete the relationship
        del in_memory_db["relationships"][relationship_index]
        
        # Delete all analyses related to this relationship
        in_memory_db["analysis_results"] = [
            analysis for analysis in in_memory_db["analysis_results"]
            if analysis.get("relationship_id") != relationship_id
        ]
        
        return {"success": True, "message": "Relationship deleted successfully"}
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete relationship: {str(e)}")

@app.get("/api/growth-plan")
async def get_growth_plan(user_id: Optional[str] = None):
    try:
        # In a real app, we'd filter by user_id
        growth_plan = None
        for plan in in_memory_db["growth_plans"]:
            if (user_id is None) or (plan.get("user_id") == user_id):
                growth_plan = plan
                break
        
        if not growth_plan:
            # Create a default growth plan
            default_plan = GrowthPlan(
                user_id=user_id,
                current_week=WeeklyPlan(
                    theme="Emotional self-validation",
                    days=[
                        GrowthActivity(
                            day=1,
                            title="Understanding Self-Validation",
                            activity_type="Guided Self-Inquiry",
                            content="Reflect on when you've dismissed your own feelings. What triggers self-doubt about your emotional responses?"
                        ),
                        GrowthActivity(
                            day=2,
                            title="Recognizing Emotional Patterns",
                            activity_type="Emotional Pattern Reframe",
                            content="Identify a recurring emotional response that you often judge. How would you respond to a friend with the same feelings?"
                        ),
                        GrowthActivity(
                            day=3,
                            title="Practice Validating Language",
                            activity_type="Practice/Script Challenge",
                            content="Write three statements that validate your feelings about a recent difficult situation."
                        ),
                        GrowthActivity(
                            day=4,
                            title="Micro-Ritual",
                            activity_type="Daily Practice",
                            content="Each time you notice self-criticism today, place a hand over your heart and say 'This feeling is valid.'"
                        ),
                        GrowthActivity(
                            day=5,
                            title="Weekly Reflection",
                            activity_type="Journal Prompt",
                            content="How has validating your emotions changed your interactions this week? What differences did you notice?"
                        )
                    ]
                ),
                goals=[
                    "Affirm my feelings without judgment",
                    "Recognize when I'm dismissing my own emotions",
                    "Respond to myself with the same compassion I'd offer others"
                ]
            )
            
            growth_plan_dict = default_plan.dict()
            in_memory_db["growth_plans"].append(growth_plan_dict)
            return growth_plan_dict
        
        return growth_plan
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve growth plan: {str(e)}")

# Additional Models for new endpoints
class UserProfile(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    email: str
    name: str
    preferences: Dict[str, Any] = {}
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())

class Settings(BaseModel):
    theme: str = "light"
    notifications_enabled: bool = True
    privacy_mode: bool = False
    language: str = "en"
    analysis_settings: Dict[str, Any] = {}

class AnomalyDetectionRequest(BaseModel):
    data: List[Dict[str, Any]]
    threshold: Optional[float] = 0.5
    method: Optional[str] = "statistical"

# Settings endpoints
@app.get("/api/settings", response_model=UserSettingsResponse)
async def get_settings(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_auth0)
):
    """Get user settings"""
    try:
        user_id = str(current_user['id'])  # Ensure it's a string
        settings = get_or_create_user_settings(db, user_id)
        return db_settings_to_response(settings)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve settings: {str(e)}")

@app.post("/api/settings", response_model=UserSettingsResponse)
async def update_settings(
    settings_data: UserSettingsUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_auth0)
):
    """Update user settings"""
    try:
        user_id = str(current_user['id'])  # Ensure it's a string
        settings = get_or_create_user_settings(db, user_id)
        
        # Update settings using the proper SQLAlchemy update method
        update_dict = {}
        if settings_data.theme is not None:
            update_dict['theme'] = sanitize_input(settings_data.theme)
        if settings_data.notifications_enabled is not None:
            update_dict['notifications_enabled'] = settings_data.notifications_enabled
        if settings_data.privacy_mode is not None:
            update_dict['privacy_mode'] = settings_data.privacy_mode
        if settings_data.language is not None:
            update_dict['language'] = sanitize_input(settings_data.language)
        
        # Update timestamp
        update_dict['updated_at'] = datetime.utcnow()
        
        # Use SQLAlchemy update method
        db.query(DBUserSettings).filter(
            DBUserSettings.user_id == user_id
        ).update(update_dict)
        db.commit()
        
        # Refresh the settings object
        db.refresh(settings)
        return db_settings_to_response(settings)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update settings: {str(e)}")

# User profile endpoints
@app.get("/api/user/profile")
async def get_user_profile():
    """Get user profile - placeholder implementation"""
    try:
        return {
            "message": "User profile functionality is pending implementation",
            "placeholder_data": {
                "id": str(uuid.uuid4()),
                "email": "<EMAIL>",
                "name": "John Doe",
                "preferences": {},
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve user profile: {str(e)}")

@app.post("/api/user/profile")
async def update_user_profile(profile_data: dict):
    """Update user profile - placeholder implementation"""
    try:
        return {
            "message": "User profile has been updated (placeholder)",
            "updated_profile": {
                **profile_data,
                "updated_at": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update user profile: {str(e)}")

# Reports endpoints
@app.get("/api/reports/generate/{report_type}")
async def generate_report(
    report_type: str,
    relationship_id: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_auth0)
):
    """Generate report by type"""
    try:
        from reports import ReportGenerator
        
        generator = ReportGenerator(db, current_user)
        
        if report_type == "relationship_summary":
            report_data = generator.generate_relationship_summary(relationship_id)
        elif report_type == "growth_report":
            report_data = generator.generate_growth_report()
        elif report_type == "csv_export":
            csv_data = generator.generate_csv_export(relationship_id)
            return {
                "report_type": report_type,
                "format": "csv",
                "data": csv_data,
                "filename": f"aei_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            }
        else:
            raise HTTPException(status_code=400, detail=f"Unknown report type: {report_type}")
        
        return {
            "report_type": report_type,
            "format": "json",
            "data": report_data,
            "generated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate report: {str(e)}")

# Anomaly Detection endpoint
@app.get("/api/detect-anomalies")
async def detect_anomalies(
    relationship_id: Optional[str] = None,
    days: int = 30,
    method: str = "statistical",
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user_auth0)
):
    """Detect communication and relationship anomalies"""
    try:
        detector = AnomalyDetector(db)
        
        anomalies = []
        user_id = str(current_user['id'])  # Ensure it's a string
        
        # Detect communication anomalies
        comm_anomalies = detector.detect_communication_anomalies(user_id, days)
        anomalies.extend(comm_anomalies)
        
        # Detect relationship-specific anomalies
        rel_anomalies = detector.detect_relationship_anomalies(user_id, relationship_id)
        anomalies.extend(rel_anomalies)
        
        return {
            "anomalies": anomalies,
            "total_count": len(anomalies),
            "method": method,
            "analysis_period_days": days,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to detect anomalies: {str(e)}")

# Run the server if executed directly
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("server:app", host="0.0.0.0", port=8001, reload=True)
