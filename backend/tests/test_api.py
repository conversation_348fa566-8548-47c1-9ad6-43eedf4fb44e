import pytest
import json
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime, timedelta

# Import your application
from server import app
from database import Base, get_db, User, Relationship, AnalysisResult, GrowthPlan

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

# Create test client
client = TestClient(app)

@pytest.fixture(scope="module")
def setup_database():
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def db_session():
    connection = engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    yield session
    session.close()
    transaction.rollback()
    connection.close()

@pytest.fixture
def test_user(db_session):
    user = User(
        auth0_id="test_user_123",
        email="<EMAIL>",
        name="Test User"
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture
def test_relationship(db_session, test_user):
    relationship = Relationship(
        user_id=test_user.id,
        name="Test Relationship",
        relationship_type="friend",
        description="A test relationship"
    )
    db_session.add(relationship)
    db_session.commit()
    db_session.refresh(relationship)
    return relationship

@pytest.fixture
def auth_headers():
    return {"Authorization": "Bearer test_token"}

class TestHealthEndpoint:
    def test_health_check(self):
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json() == {"status": "healthy"}

class TestAuthenticationEndpoints:
    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_get_current_user_success(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        response = client.get("/auth/user", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["email"] == "<EMAIL>"
        assert data["name"] == "Test User"

    def test_get_current_user_unauthorized(self):
        response = client.get("/auth/user")
        assert response.status_code == 401

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_user_creation_on_first_login(self, mock_auth, db_session):
        # Mock Auth0 user data
        mock_auth0_user = {
            "sub": "auth0|new_user_123",
            "email": "<EMAIL>",
            "name": "New User"
        }
        
        new_user = User(
            auth0_id="auth0|new_user_123",
            email="<EMAIL>",
            name="New User"
        )
        mock_auth.return_value = new_user
        
        response = client.get("/auth/user", headers={"Authorization": "Bearer new_token"})
        assert response.status_code == 200
        
        # Verify user was created in database
        user = db_session.query(User).filter_by(email="<EMAIL>").first()
        assert user is not None
        assert user.name == "New User"

class TestDashboardEndpoints:
    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_get_dashboard_data(self, mock_auth, test_user, test_relationship, auth_headers, db_session):
        mock_auth.return_value = test_user
        
        # Add some test data
        analysis = AnalysisResult(
            user_id=test_user.id,
            relationship_id=test_relationship.id,
            message_content="Test message",
            sentiment_score=0.8,
            emotional_indicators=["positive"],
            communication_flags=[],
            ai_insights="Test insights"
        )
        db_session.add(analysis)
        db_session.commit()
        
        response = client.get("/dashboard", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "total_relationships" in data
        assert "total_analyses" in data
        assert "recent_activity" in data
        assert "sentiment_timeline" in data
        assert "flag_counts" in data
        assert data["total_relationships"] == 1
        assert data["total_analyses"] == 1

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_dashboard_empty_data(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        response = client.get("/dashboard", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["total_relationships"] == 0
        assert data["total_analyses"] == 0
        assert data["recent_activity"] == []

class TestRelationshipEndpoints:
    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_get_relationships(self, mock_auth, test_user, test_relationship, auth_headers):
        mock_auth.return_value = test_user
        
        response = client.get("/relationships", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert data[0]["name"] == "Test Relationship"
        assert data[0]["relationship_type"] == "friend"

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_create_relationship(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        relationship_data = {
            "name": "New Relationship",
            "relationship_type": "romantic",
            "description": "A new test relationship"
        }
        
        response = client.post("/relationships", json=relationship_data, headers=auth_headers)
        assert response.status_code == 201
        
        data = response.json()
        assert data["name"] == "New Relationship"
        assert data["relationship_type"] == "romantic"
        assert "id" in data
        assert "created_at" in data

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_update_relationship(self, mock_auth, test_user, test_relationship, auth_headers):
        mock_auth.return_value = test_user
        
        update_data = {
            "name": "Updated Relationship",
            "description": "Updated description"
        }
        
        response = client.put(f"/relationships/{test_relationship.id}", json=update_data, headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == "Updated Relationship"
        assert data["description"] == "Updated description"

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_delete_relationship(self, mock_auth, test_user, test_relationship, auth_headers):
        mock_auth.return_value = test_user
        
        response = client.delete(f"/relationships/{test_relationship.id}", headers=auth_headers)
        assert response.status_code == 204

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_relationship_not_found(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        response = client.get("/relationships/999", headers=auth_headers)
        assert response.status_code == 404

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_unauthorized_relationship_access(self, mock_auth, test_user, auth_headers, db_session):
        # Create another user
        other_user = User(
            auth0_id="other_user_123",
            email="<EMAIL>",
            name="Other User"
        )
        db_session.add(other_user)
        db_session.commit()
        
        # Create relationship for other user
        other_relationship = Relationship(
            user_id=other_user.id,
            name="Other's Relationship",
            relationship_type="friend"
        )
        db_session.add(other_relationship)
        db_session.commit()
        
        mock_auth.return_value = test_user
        
        # Try to access other user's relationship
        response = client.get(f"/relationships/{other_relationship.id}", headers=auth_headers)
        assert response.status_code == 404  # Should not find it due to user filtering

class TestMessageAnalysisEndpoints:
    @patch('auth.auth0_verification.get_current_user_auth0')
    @patch('groq.Groq')
    def test_analyze_message_success(self, mock_groq, mock_auth, test_user, test_relationship, auth_headers):
        mock_auth.return_value = test_user
        
        # Mock Groq API response
        mock_client = Mock()
        mock_groq.return_value = mock_client
        mock_client.chat.completions.create.return_value = Mock(
            choices=[Mock(
                message=Mock(
                    content=json.dumps({
                        "sentiment_score": 0.8,
                        "emotional_indicators": ["positive", "excited"],
                        "communication_flags": [],
                        "ai_insights": "This message shows positive sentiment",
                        "relationship_impact": "Likely to strengthen relationship",
                        "suggested_responses": ["That's wonderful!", "Tell me more!"]
                    })
                )
            )]
        )
        
        analysis_data = {
            "message": "I'm so excited about our upcoming trip!",
            "relationship_id": test_relationship.id,
            "context": "Planning vacation"
        }
        
        response = client.post("/analyze", json=analysis_data, headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["sentiment_score"] == 0.8
        assert "positive" in data["emotional_indicators"]
        assert data["ai_insights"] == "This message shows positive sentiment"
        assert len(data["suggested_responses"]) == 2

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_analyze_message_validation_errors(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        # Test empty message
        response = client.post("/analyze", json={"message": ""}, headers=auth_headers)
        assert response.status_code == 422
        
        # Test missing required fields
        response = client.post("/analyze", json={}, headers=auth_headers)
        assert response.status_code == 422

    @patch('auth.auth0_verification.get_current_user_auth0')
    @patch('groq.Groq')
    def test_analyze_message_ai_service_error(self, mock_groq, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        # Mock Groq API error
        mock_client = Mock()
        mock_groq.return_value = mock_client
        mock_client.chat.completions.create.side_effect = Exception("AI service unavailable")
        
        analysis_data = {
            "message": "Test message",
            "relationship_id": None
        }
        
        response = client.post("/analyze", json=analysis_data, headers=auth_headers)
        assert response.status_code == 500
        assert "AI service unavailable" in response.json()["detail"]

class TestAnomalyDetectionEndpoints:
    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_get_anomalies(self, mock_auth, test_user, auth_headers, db_session):
        mock_auth.return_value = test_user
        
        # Create test data for anomaly detection
        relationship = Relationship(
            user_id=test_user.id,
            name="Test Relationship",
            relationship_type="friend"
        )
        db_session.add(relationship)
        db_session.commit()
        
        # Add analyses with varying sentiment scores
        yesterday = datetime.now() - timedelta(days=1)
        today = datetime.now()
        
        # Normal sentiment
        analysis1 = AnalysisResult(
            user_id=test_user.id,
            relationship_id=relationship.id,
            message_content="Normal message",
            sentiment_score=0.8,
            timestamp=yesterday
        )
        
        # Anomalous sentiment drop
        analysis2 = AnalysisResult(
            user_id=test_user.id,
            relationship_id=relationship.id,
            message_content="Concerning message",
            sentiment_score=0.2,
            timestamp=today
        )
        
        db_session.add_all([analysis1, analysis2])
        db_session.commit()
        
        response = client.get("/anomalies", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        # Would expect anomaly detection to identify the sentiment drop

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_get_anomalies_with_filters(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        params = {
            "severity": "high",
            "days": 7,
            "relationship_id": 1
        }
        
        response = client.get("/anomalies", params=params, headers=auth_headers)
        assert response.status_code == 200

class TestReportsEndpoints:
    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_generate_report(self, mock_auth, test_user, test_relationship, auth_headers, db_session):
        mock_auth.return_value = test_user
        
        # Add test data
        analysis = AnalysisResult(
            user_id=test_user.id,
            relationship_id=test_relationship.id,
            message_content="Test message",
            sentiment_score=0.7,
            emotional_indicators=["positive"],
            communication_flags=[],
            ai_insights="Test insights"
        )
        db_session.add(analysis)
        db_session.commit()
        
        response = client.get("/reports", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "summary" in data
        assert "sentiment_timeline" in data
        assert "flag_counts" in data
        assert "relationship_insights" in data

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_report_date_filtering(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        params = {
            "start_date": "2024-01-01",
            "end_date": "2024-01-31"
        }
        
        response = client.get("/reports", params=params, headers=auth_headers)
        assert response.status_code == 200

class TestGrowthPlanEndpoints:
    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_get_growth_plans(self, mock_auth, test_user, auth_headers, db_session):
        mock_auth.return_value = test_user
        
        # Create test growth plan
        growth_plan = GrowthPlan(
            user_id=test_user.id,
            title="Improve Communication",
            description="Focus on active listening",
            target_relationship="Test Relationship",
            activities=[{
                "id": "1",
                "title": "Practice active listening",
                "description": "Listen without interrupting",
                "completed": False
            }],
            progress=25
        )
        db_session.add(growth_plan)
        db_session.commit()
        
        response = client.get("/growth-plans", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Improve Communication"
        assert data[0]["progress"] == 25

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_create_growth_plan(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        plan_data = {
            "title": "New Growth Plan",
            "description": "A new plan for growth",
            "target_relationship": "Sarah",
            "activities": [
                {
                    "title": "Daily check-in",
                    "description": "Send a daily message",
                    "due_date": "2024-02-01"
                }
            ]
        }
        
        response = client.post("/growth-plans", json=plan_data, headers=auth_headers)
        assert response.status_code == 201
        
        data = response.json()
        assert data["title"] == "New Growth Plan"
        assert len(data["activities"]) == 1
        assert data["progress"] == 0

class TestUserSettingsEndpoints:
    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_get_user_settings(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        response = client.get("/settings", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "faith_mode_enabled" in data
        assert "font_preference" in data
        assert "motion_preference" in data
        assert "privacy_level" in data

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_update_user_settings(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        settings_data = {
            "faith_mode_enabled": True,
            "font_preference": "dyslexic",
            "motion_preference": "reduced",
            "privacy_level": "high"
        }
        
        response = client.put("/settings", json=settings_data, headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["faith_mode_enabled"] == True
        assert data["font_preference"] == "dyslexic"

class TestErrorHandling:
    def test_404_not_found(self):
        response = client.get("/non-existent-endpoint")
        assert response.status_code == 404

    def test_method_not_allowed(self):
        response = client.post("/health")  # GET only endpoint
        assert response.status_code == 405

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_database_error_handling(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        # Mock database connection error
        with patch('database.get_db') as mock_db:
            mock_db.side_effect = Exception("Database connection failed")
            
            response = client.get("/relationships", headers=auth_headers)
            assert response.status_code == 500

class TestInputValidation:
    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_sql_injection_prevention(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        # Try SQL injection in relationship name
        malicious_data = {
            "name": "'; DROP TABLE relationships; --",
            "relationship_type": "friend"
        }
        
        response = client.post("/relationships", json=malicious_data, headers=auth_headers)
        # Should either succeed with sanitized input or fail validation
        assert response.status_code in [201, 422]

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_xss_prevention(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        # Try XSS in message content
        xss_data = {
            "message": "<script>alert('xss')</script>",
            "relationship_id": None
        }
        
        response = client.post("/analyze", json=xss_data, headers=auth_headers)
        # Should succeed but content should be sanitized
        if response.status_code == 200:
            # Verify XSS was prevented (this would depend on your sanitization)
            pass

class TestPerformance:
    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_large_dataset_handling(self, mock_auth, test_user, auth_headers, db_session):
        mock_auth.return_value = test_user
        
        # Create many relationships
        relationships = []
        for i in range(100):
            rel = Relationship(
                user_id=test_user.id,
                name=f"Relationship {i}",
                relationship_type="friend"
            )
            relationships.append(rel)
        
        db_session.add_all(relationships)
        db_session.commit()
        
        import time
        start_time = time.time()
        response = client.get("/relationships", headers=auth_headers)
        end_time = time.time()
        
        assert response.status_code == 200
        assert len(response.json()) == 100
        # Should respond within reasonable time
        assert (end_time - start_time) < 2.0

    @patch('auth.auth0_verification.get_current_user_auth0')
    def test_concurrent_requests(self, mock_auth, test_user, auth_headers):
        mock_auth.return_value = test_user
        
        import threading
        import time
        
        results = []
        
        def make_request():
            response = client.get("/dashboard", headers=auth_headers)
            results.append(response.status_code)
        
        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
        
        # Start all threads
        start_time = time.time()
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        end_time = time.time()
        
        # All requests should succeed
        assert all(status == 200 for status in results)
        # Should handle concurrent requests efficiently
        assert (end_time - start_time) < 5.0

if __name__ == "__main__":
    pytest.main([__file__])