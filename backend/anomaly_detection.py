"""
Anomaly detection service for identifying unusual patterns in communication and relationships.
"""

import statistics
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from database import AnalysisResult, Relationship as DBRelationship


class AnomalyDetector:
    """Service for detecting anomalies in communication patterns and relationships"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def detect_communication_anomalies(self, user_id: str, days: int = 30) -> List[Dict[str, Any]]:
        """Detect anomalies in communication patterns"""
        anomalies = []
        
        # Get recent analyses for the user
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        analyses = self.db.query(AnalysisResult).filter(
            AnalysisResult.user_id == user_id,
            AnalysisResult.created_at >= cutoff_date
        ).all()
        
        if len(analyses) < 5:  # Need minimum data points
            return anomalies
        
        # Detect sentiment anomalies
        sentiment_scores = self._convert_sentiment_to_scores(analyses)
        sentiment_anomalies = self._detect_statistical_anomalies(
            sentiment_scores, 
            threshold=2.0,
            anomaly_type="sentiment"
        )
        anomalies.extend(sentiment_anomalies)
        
        # Detect flag frequency anomalies
        flag_anomalies = self._detect_flag_anomalies(analyses)
        anomalies.extend(flag_anomalies)
        
        # Detect communication frequency anomalies
        frequency_anomalies = self._detect_frequency_anomalies(analyses)
        anomalies.extend(frequency_anomalies)
        
        return anomalies
    
    def detect_relationship_anomalies(self, user_id: str, relationship_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Detect anomalies in specific relationships"""
        anomalies = []
        
        # Get relationships to analyze
        relationships_query = self.db.query(DBRelationship).filter(
            DBRelationship.user_id == user_id
        )
        
        if relationship_id:
            relationships_query = relationships_query.filter(
                DBRelationship.id == relationship_id
            )
        
        relationships = relationships_query.all()
        
        for relationship in relationships:
            # Get relationship ID (type cast for type checker)
            relationship_id_str = str(relationship.id)
            
            # Get analyses for this relationship
            analyses = self.db.query(AnalysisResult).filter(
                AnalysisResult.user_id == user_id,
                AnalysisResult.relationship_id == relationship.id
            ).order_by(AnalysisResult.created_at.desc()).limit(50).all()
            
            if len(analyses) < 3:  # Need minimum data
                continue
            
            # Detect sudden sentiment changes
            sentiment_changes = self._detect_sentiment_changes(analyses, relationship_id_str)
            anomalies.extend(sentiment_changes)
            
            # Detect escalating flag patterns
            flag_escalations = self._detect_flag_escalations(analyses, relationship_id_str)
            anomalies.extend(flag_escalations)
        
        return anomalies
    
    def _convert_sentiment_to_scores(self, analyses: List[AnalysisResult]) -> List[float]:
        """Convert sentiment strings to numerical scores"""
        sentiment_mapping = {
            "positive": 1.0,
            "neutral": 0.0,
            "negative": -1.0
        }
        
        return [
            sentiment_mapping.get(getattr(analysis, 'sentiment', 'neutral'), 0.0) 
            for analysis in analyses
        ]
    
    def _detect_statistical_anomalies(self, values: List[float], threshold: float = 2.0, anomaly_type: str = "general") -> List[Dict[str, Any]]:
        """Detect statistical anomalies using z-score"""
        if len(values) < 3:
            return []
        
        anomalies = []
        mean_val = statistics.mean(values)
        std_dev = statistics.stdev(values) if len(values) > 1 else 0
        
        if std_dev == 0:  # No variation
            return []
        
        for i, value in enumerate(values):
            z_score = abs((value - mean_val) / std_dev)
            if z_score > threshold:
                anomalies.append({
                    "type": f"{anomaly_type}_anomaly",
                    "severity": "high" if z_score > 3.0 else "medium",
                    "description": f"Unusual {anomaly_type} pattern detected",
                    "z_score": round(z_score, 2),
                    "value": value,
                    "mean": round(mean_val, 2),
                    "detected_at": datetime.utcnow().isoformat()
                })
        
        return anomalies
    
    def _detect_flag_anomalies(self, analyses: List[AnalysisResult]) -> List[Dict[str, Any]]:
        """Detect anomalies in flag patterns"""
        anomalies = []
        
        # Count flags by type over time
        flag_counts_by_day = {}
        
        for analysis in analyses:
            day = analysis.created_at.date()
            if day not in flag_counts_by_day:
                flag_counts_by_day[day] = {}
            
            for flag in (analysis.flags or []):
                flag_type = flag.get("type", "unknown")
                if flag_type not in flag_counts_by_day[day]:
                    flag_counts_by_day[day][flag_type] = 0
                flag_counts_by_day[day][flag_type] += 1
        
        # Check for sudden spikes in specific flag types
        for flag_type in ["manipulation", "conflict", "emotional_distance", "communication_breakdown"]:
            daily_counts = []
            for day_flags in flag_counts_by_day.values():
                daily_counts.append(day_flags.get(flag_type, 0))
            
            if len(daily_counts) >= 3:
                flag_anomalies = self._detect_statistical_anomalies(
                    [float(count) for count in daily_counts],
                    threshold=1.5,
                    anomaly_type=f"flag_{flag_type}"
                )
                anomalies.extend(flag_anomalies)
        
        return anomalies
    
    def _detect_frequency_anomalies(self, analyses: List[AnalysisResult]) -> List[Dict[str, Any]]:
        """Detect anomalies in communication frequency"""
        anomalies = []
        
        # Group analyses by day
        daily_counts = {}
        for analysis in analyses:
            day = analysis.created_at.date()
            daily_counts[day] = daily_counts.get(day, 0) + 1
        
        if len(daily_counts) < 7:  # Need at least a week of data
            return anomalies
        
        counts = list(daily_counts.values())
        frequency_anomalies = self._detect_statistical_anomalies(
            [float(count) for count in counts],
            threshold=2.0,
            anomaly_type="communication_frequency"
        )
        
        return frequency_anomalies
    
    def _detect_sentiment_changes(self, analyses: List[AnalysisResult], relationship_id: str) -> List[Dict[str, Any]]:
        """Detect sudden changes in relationship sentiment"""
        anomalies = []
        
        if len(analyses) < 5:
            return anomalies
        
        # Check for dramatic sentiment shifts
        sentiment_scores = self._convert_sentiment_to_scores(analyses)
        
        for i in range(1, len(sentiment_scores)):
            current = sentiment_scores[i]
            previous = sentiment_scores[i-1]
            
            # Check for dramatic shift (e.g., positive to negative)
            if abs(current - previous) >= 1.5:
                anomalies.append({
                    "type": "sentiment_shift",
                    "severity": "high",
                    "description": f"Dramatic sentiment change in relationship",
                    "relationship_id": relationship_id,
                    "from_sentiment": self._score_to_sentiment(previous),
                    "to_sentiment": self._score_to_sentiment(current),
                    "detected_at": datetime.utcnow().isoformat()
                })
        
        return anomalies
    
    def _detect_flag_escalations(self, analyses: List[AnalysisResult], relationship_id: str) -> List[Dict[str, Any]]:
        """Detect escalating patterns of concerning flags"""
        anomalies = []
        
        serious_flags = ["manipulation", "emotional_abuse", "conflict", "communication_breakdown"]
        
        # Look for increasing frequency of serious flags
        recent_flags = []
        for analysis in analyses[:10]:  # Last 10 analyses
            for flag in (analysis.flags or []):
                if flag.get("type") in serious_flags:
                    recent_flags.append(flag.get("type"))
        
        # Check if there's an escalating pattern
        if len(recent_flags) >= 3:
            # Check if recent half has more flags than older half
            midpoint = len(recent_flags) // 2
            recent_half = recent_flags[:midpoint]
            older_half = recent_flags[midpoint:]
            
            if len(recent_half) > len(older_half) * 1.5:
                anomalies.append({
                    "type": "flag_escalation",
                    "severity": "high",
                    "description": "Escalating pattern of concerning communication flags detected",
                    "relationship_id": relationship_id,
                    "recent_flags": recent_half,
                    "flag_count_increase": len(recent_half) - len(older_half),
                    "detected_at": datetime.utcnow().isoformat()
                })
        
        return anomalies
    
    def _score_to_sentiment(self, score: float) -> str:
        """Convert numerical score back to sentiment"""
        if score > 0.5:
            return "positive"
        elif score < -0.5:
            return "negative"
        else:
            return "neutral"
