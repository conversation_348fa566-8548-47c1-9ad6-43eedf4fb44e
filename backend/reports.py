"""
Report generation service for AEI application
"""
import json
import csv
import io
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from database import AnalysisResult, Relationship, User


class ReportGenerator:
    """Generate various types of reports for user analysis data"""
    
    def __init__(self, db: Session, user: User):
        self.db = db
        self.user = user
    
    def generate_relationship_summary(self, relationship_id: Optional[str] = None) -> Dict[str, Any]:
        """Generate a summary report for relationships"""
        query = self.db.query(AnalysisResult).filter(AnalysisResult.user_id == self.user.id)
        
        if relationship_id:
            query = query.filter(AnalysisResult.relationship_id == relationship_id)
            relationship = self.db.query(Relationship).filter(
                Relationship.id == relationship_id,
                Relationship.user_id == self.user.id
            ).first()
            title = f"Relationship Report: {relationship.name if relationship else 'Unknown'}"
        else:
            title = "Overall Relationship Summary"
        
        analyses = query.all()
        
        # Calculate statistics
        total_analyses = len(analyses)
        sentiment_counts = {"positive": 0, "neutral": 0, "negative": 0}
        flag_summary = {}
        monthly_trends = {}
        
        for analysis in analyses:
            # Count sentiments (explicit extraction for type checker)
            sentiment_value = getattr(analysis, 'sentiment', None)
            sentiment = sentiment_value if sentiment_value else "neutral"
            sentiment_counts[sentiment] = sentiment_counts.get(sentiment, 0) + 1
            
            # Count flag types (explicit extraction for type checker)
            flags_value = getattr(analysis, 'flags', None)
            flags = flags_value if flags_value else []
            for flag in flags:
                flag_type = flag.get("type", "Unknown") if isinstance(flag, dict) else str(flag)
                flag_summary[flag_type] = flag_summary.get(flag_type, 0) + 1
            
            # Monthly trends
            month_key = analysis.created_at.strftime("%Y-%m")
            if month_key not in monthly_trends:
                monthly_trends[month_key] = {"count": 0, "flags": 0}
            monthly_trends[month_key]["count"] += 1
            monthly_trends[month_key]["flags"] += len(flags)
        
        # Calculate health score
        health_score = 100
        if total_analyses > 0:
            negative_ratio = sentiment_counts["negative"] / total_analyses
            flag_ratio = sum(flag_summary.values()) / total_analyses
            health_score = max(0, 100 - (negative_ratio * 40) - (flag_ratio * 30))
        
        return {
            "title": title,
            "generated_at": datetime.utcnow().isoformat(),
            "period": {
                "start": min(getattr(a, 'created_at') for a in analyses).isoformat() if analyses else None,
                "end": max(getattr(a, 'created_at') for a in analyses).isoformat() if analyses else None
            },
            "summary": {
                "total_analyses": total_analyses,
                "health_score": round(health_score, 1),
                "sentiment_distribution": sentiment_counts,
                "top_flags": dict(sorted(flag_summary.items(), key=lambda x: x[1], reverse=True)[:5])
            },
            "trends": {
                "monthly": monthly_trends
            },
            "recommendations": self._generate_recommendations(sentiment_counts, flag_summary, total_analyses)
        }
    
    def generate_csv_export(self, relationship_id: Optional[str] = None) -> str:
        """Generate CSV export of analysis data"""
        query = self.db.query(AnalysisResult).filter(AnalysisResult.user_id == self.user.id)
        
        if relationship_id:
            query = query.filter(AnalysisResult.relationship_id == relationship_id)
        
        analyses = query.order_by(AnalysisResult.created_at.desc()).all()
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            "Date", "Relationship", "Message", "Sentiment", "Flags", 
            "Interpretation", "Suggestions"
        ])
        
        # Write data
        for analysis in analyses:
            relationship = None
            relationship_id = getattr(analysis, 'relationship_id', None)
            if relationship_id:
                relationship = self.db.query(Relationship).filter(
                    Relationship.id == relationship_id
                ).first()
            
            flags_list = getattr(analysis, 'flags', None) or []
            flags_str = "; ".join([
                f"{flag.get('type', '')}: {flag.get('description', '')}" 
                if isinstance(flag, dict) else str(flag)
                for flag in flags_list
            ])
            
            suggestions_list = getattr(analysis, 'suggestions', None) or []
            suggestions_str = "; ".join(suggestions_list)
            
            writer.writerow([
                analysis.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                relationship.name if relationship else "General",
                analysis.text,
                analysis.sentiment or "neutral",
                flags_str,
                analysis.interpretation or "",
                suggestions_str
            ])
        
        return output.getvalue()
    
    def generate_growth_report(self) -> Dict[str, Any]:
        """Generate personal growth insights report"""
        # Get analyses from the last 30 days
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        recent_analyses = self.db.query(AnalysisResult).filter(
            AnalysisResult.user_id == self.user.id,
            AnalysisResult.created_at >= thirty_days_ago
        ).all()
        
        # Get older analyses for comparison
        older_analyses = self.db.query(AnalysisResult).filter(
            AnalysisResult.user_id == self.user.id,
            AnalysisResult.created_at < thirty_days_ago
        ).all()
        
        recent_sentiment_counts = {"positive": 0, "neutral": 0, "negative": 0}
        older_sentiment_counts = {"positive": 0, "neutral": 0, "negative": 0}
        
        recent_flag_count = 0
        older_flag_count = 0
        
        for analysis in recent_analyses:
            sentiment = getattr(analysis, 'sentiment', None) or "neutral"
            recent_sentiment_counts[sentiment] += 1
            flags = getattr(analysis, 'flags', None) or []
            recent_flag_count += len(flags)
        
        for analysis in older_analyses:
            sentiment = getattr(analysis, 'sentiment', None) or "neutral"
            older_sentiment_counts[sentiment] += 1
            flags = getattr(analysis, 'flags', None) or []
            older_flag_count += len(flags)
        
        # Calculate improvements
        improvements = []
        concerns = []
        
        if len(recent_analyses) > 0 and len(older_analyses) > 0:
            recent_positive_ratio = recent_sentiment_counts["positive"] / len(recent_analyses)
            older_positive_ratio = older_sentiment_counts["positive"] / len(older_analyses)
            
            recent_flag_ratio = recent_flag_count / len(recent_analyses)
            older_flag_ratio = older_flag_count / len(older_analyses)
            
            if recent_positive_ratio > older_positive_ratio:
                improvements.append("Increased positive communication patterns")
            
            if recent_flag_ratio < older_flag_ratio:
                improvements.append("Reduced emotional red flags in communications")
            
            if recent_positive_ratio < older_positive_ratio:
                concerns.append("Decreased positive sentiment in recent communications")
            
            if recent_flag_ratio > older_flag_ratio:
                concerns.append("Increased emotional warning signs detected")
        
        return {
            "title": "Personal Growth Report",
            "generated_at": datetime.utcnow().isoformat(),
            "period": "Last 30 days",
            "recent_metrics": {
                "total_analyses": len(recent_analyses),
                "sentiment_distribution": recent_sentiment_counts,
                "average_flags_per_message": round(recent_flag_count / len(recent_analyses), 2) if recent_analyses else 0
            },
            "improvements": improvements,
            "areas_for_attention": concerns,
            "recommendations": self._generate_growth_recommendations(recent_analyses)
        }
    
    def _generate_recommendations(self, sentiment_counts: Dict, flag_summary: Dict, total_analyses: int) -> List[str]:
        """Generate recommendations based on analysis data"""
        recommendations = []
        
        if total_analyses == 0:
            return ["Start analyzing your communications to receive personalized recommendations"]
        
        negative_ratio = sentiment_counts.get("negative", 0) / total_analyses
        flag_ratio = sum(flag_summary.values()) / total_analyses
        
        if negative_ratio > 0.3:
            recommendations.append("Consider focusing on more positive communication patterns")
        
        if flag_ratio > 0.5:
            recommendations.append("Review recurring emotional patterns that may need attention")
        
        if "gaslighting" in flag_summary:
            recommendations.append("Consider seeking support for communication boundaries")
        
        if "defensiveness" in flag_summary:
            recommendations.append("Practice active listening techniques")
        
        return recommendations or ["Continue monitoring your communication patterns"]
    
    def _generate_growth_recommendations(self, analyses: List) -> List[str]:
        """Generate growth-focused recommendations"""
        if not analyses:
            return ["Start tracking your communications for personalized growth insights"]
        
        recommendations = [
            "Practice mindful communication by pausing before responding",
            "Regularly reflect on your emotional patterns and triggers",
            "Consider setting healthy boundaries in your relationships"
        ]
        
        return recommendations
