{"type": "external_account", "audience": "//iam.googleapis.com/projects/*************/locations/global/workloadIdentityPools/github-actions-pool/providers/github-actions-provider-v2", "subject_token_type": "urn:ietf:params:oauth:token-type:jwt", "token_url": "https://sts.googleapis.com/v1/token", "credential_source": {"url": "https://run-actions-2-azure-eastus.actions.githubusercontent.com/104//idtoken/82df3877-9140-4b2d-8e95-2616bd6b8e70/4d22ab95-4799-527c-af78-f407d3c84965?api-version=2.0&audience=https%3A%2F%2Fiam.googleapis.com%2Fprojects%2F*************%2Flocations%2Fglobal%2FworkloadIdentityPools%2Fgithub-actions-pool%2Fproviders%2Fgithub-actions-provider-v2", "headers": {"Authorization": "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjM4ODI2YjE3LTZhMzAtNWY5Yi1iMTY5LThiZWI4MjAyZjcyMyIsInR5cCI6IkpXVCIsIng1dCI6InlrTmFZNHFNX3RhNGsyVGdaT0NFWUxrY1lsQSJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Hm7eBLbs_TrMAWg03530F6YQ6avnUtWct0selyow1nL-Y9JNNmG2hxLF8WGZ1qZO5N6CdUqKvTQ8pOq42IzvTbrN58GJZVKDV6bQGmlKxVP4Vmec6BppBQkJAYbKlkpInk354mzOwq69R5_7RflhGulEe3fuB-Td3wkuO2t2eglLe-I8y7EE6d6svGP-HXXywSLoxK8EAvEEUnKo2mIOgZdos5E50tHh5XnW1b3uKNZNd7dsXfg2RRPV0isqhxf7E19Qp0R8FLA3ocwef5Iwee32lVSYW9qoV_s1i2PhCXllmvVK5GzOvbantHneM4FLiUs5TgPrhPApUCgOlI9wZQ"}, "format": {"type": "json", "subject_token_field_name": "value"}}, "service_account_impersonation_url": "https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/<EMAIL>:generateAccessToken"}