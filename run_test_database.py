#!/usr/bin/env python3
"""
Test runner for database tests - handles import paths correctly
"""

import sys
import os
from pathlib import Path

def run_database_test():
    """Run the database test with proper path setup"""
    # Get project root
    project_root = Path(__file__).parent
    backend_dir = project_root / 'backend'
    
    # Add backend to path
    sys.path.insert(0, str(backend_dir))
    
    # Now we can safely import and run the test
    try:
        from database import init_database, get_db, User, create_user
        from auth.security import hash_password, verify_password
        from sqlalchemy.orm import Session
        
        print("✅ All imports successful!")
        print("Initializing database...")
        init_database()
        
        print("Testing database connection...")
        db_gen = get_db()
        db = next(db_gen)
        print("✅ Database connection successful!")
        
        # Test user creation
        print("Testing user creation...")
        test_email = "<EMAIL>"
        test_name = "Test User"
        test_password = "test_password_123"
        
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == test_email).first()
        if existing_user:
            print(f"✅ Test user already exists: {existing_user.email}")
        else:
            hashed_pw = hash_password(test_password)
            user = create_user(db, test_email, test_name, hashed_pw)
            print(f"✅ Created user: {user.email}")
        
        # Test password verification
        print("Testing password verification...")
        hashed_pw = hash_password(test_password)
        is_valid = verify_password(test_password, hashed_pw)
        print(f"✅ Password verification: {'PASS' if is_valid else 'FAIL'}")
        
        db.close()
        print("✅ All database tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_database_test()
